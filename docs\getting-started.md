# Getting Started with RevitAddIn2025

This guide will help you set up, install and get familiar with RevitAddIn2025.

## Installation

### Requirements

- Autodesk Revit 2025
- Windows 10 or later
- .NET 8.0 Runtime (included with Revit 2025)

### Installing from Release

1. Download the latest release ZIP file from our [Releases page](https://github.com/yourusername/RevitAddIn2025/releases)
2. Extract the contents of the ZIP file
3. Copy all extracted files to your Revit 2025 Addins folder:
   ```
   C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2025\
   ```
4. Launch Revit 2025
5. Verify that the "RevitAddIn2025" tab appears in the ribbon

### Installing from Source

1. Clone the repository or download the source code
2. Open the solution file (`src/RevitAddIn2025.sln`) in Visual Studio
3. Build the solution using the Release/x64 configuration
4. Copy all files from `src/bin/Release` to your Revit Addins folder
5. Launch Revit 2025

## Using the Add-in

### Accessing the Dashboard

1. In Revit, locate the "RevitAddIn2025" tab in the ribbon
2. Click the "Dashboard" button to open the main dashboard window
3. The dashboard provides an overview of your current project, including:
   - Project information
   - Element counts
   - Recent activity
   - Quick access to tools

### Configuring Settings

1. Click the "Settings" button in the ribbon or from the dashboard
2. Adjust the following settings according to your preferences:
   - Units (Imperial/Metric)
   - Theme (Light/Dark)
   - Font Size
   - Advanced options for data caching and logging

### Using Commands

The add-in provides several commands accessible from the ribbon:

- **Dashboard**: Opens the main dashboard window
- **Settings**: Opens the settings window
- **Help**: Provides access to help documentation
- **About**: Shows information about the add-in

## Troubleshooting

### Common Issues

1. **Add-in doesn't appear in Revit ribbon**
   - Verify that all files were copied to the correct Addins folder
   - Check if the .addin manifest file is present
   - Restart Revit

2. **Icons not displaying correctly**
   - Ensure all resource files were copied properly
   - Verify that the PNG folder is present in the installation directory

3. **Dashboard fails to load**
   - Check Revit's journal files for error messages
   - Verify that you have the required permissions to access project data

### Getting Help

If you encounter any issues not covered in this guide:

1. Check the [FAQs](faqs.md) document
2. Review the [known issues](known-issues.md) list
3. Submit an issue on our GitHub repository
4. Contact <NAME_EMAIL>

## Next Steps

- Explore the [User Guide](user-guide.md) for detailed information on all features
- Learn about advanced features in the [Advanced Topics](advanced-topics.md) guide
- See how to extend the add-in in the [Developer Guide](developer-guide.md)
