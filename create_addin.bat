@echo off
(
echo ^<?xml version="1.0" encoding="utf-8" standalone="no"?^>
echo ^<RevitAddIns^>
echo   ^<AddIn Type="Application"^>
echo     ^<Name^>RevitAddIn2025^</Name^>
echo     ^<Assembly^>RevitAddIn2025.dll^</Assembly^>
echo     ^<AddInId^>B8399B5E-9B39-42E9-9B1D-869C8F59E76E^</AddInId^>
echo     ^<FullClassName^>RevitAddIn2025.App.RevitApplication^</FullClassName^>
echo     ^<VendorId^>Zyeta^</VendorId^>
echo     ^<VendorDescription^>Raghav at Zyeta - Professional Revit Add-in Developer^</VendorDescription^>
echo   ^</AddIn^>
echo ^</RevitAddIns^>
) > "%APPDATA%\Autodesk\Revit\Addins\2025\RevitAddIn2025.addin"

echo .addin file created successfully!
type "%APPDATA%\Autodesk\Revit\Addins\2025\RevitAddIn2025.addin"
