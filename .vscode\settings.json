{
    // ======================================================================
    // ULTIMATE AUTOMATION VS CODE SETTINGS
    // Zero Manual Work Configuration
    // ======================================================================
    // ===== TABNINE AI CONFIGURATION =====
    "tabnine.experimentalAutoImports": true,
    "tabnine.suggestions.enabled": true,
    "tabnine.debounceMs": 100,
    // ===== BLACKBOX AI CONFIGURATION =====
    "blackboxapp.enabled": true,
    "blackboxapp.autoSuggest": true,
    // ===== C# DEVELOPMENT AUTOMATION =====
    "csharp.format.enable": true,
    "csharp.semanticHighlighting.enabled": true,
    // ===== AUTO-SAVE AND FORMATTING =====
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "editor.formatOnSave": true,
    "editor.formatOnType": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },
    // ===== INTELLIGENT CODE COMPLETION =====
    "editor.suggestOnTriggerCharacters": true,
    "editor.acceptSuggestionOnCommitCharacter": true,
    "editor.tabCompletion": "on",
    "editor.quickSuggestions": {
        "other": true,
        "comments": true,
        "strings": true
    },
    "editor.inlineSuggest.enabled": true,
    // ===== BUILD AND TASK AUTOMATION =====
    "task.autoDetect": "on",
    "tasks.saveBeforeRun": "always",
    // ===== PRODUCTIVITY ENHANCEMENTS =====
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "workbench.editor.enablePreview": false,
    "workbench.startupEditor": "none",
    // ===== LANGUAGE-SPECIFIC SETTINGS =====
    "[csharp]": {
        "editor.defaultFormatter": "ms-dotnettools.csharp",
        "editor.tabSize": 4,
        "editor.insertSpaces": true
    },
    "[powershell]": {
        "editor.defaultFormatter": "ms-vscode.powershell",
        "editor.tabSize": 4
    },
    // ===== REVIT-SPECIFIC CONFIGURATIONS =====
    "files.associations": {
        "*.addin": "xml"
    },
    "dotnet.inlayHints.enableInlayHintsForParameters": true,
    "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true
}