{
    // ======================================================================
    // ULTIMATE AUTOMATION VS CODE SETTINGS
    // Zero Manual Work Configuration
    // ======================================================================
    // ===== TABNINE AI CONFIGURATION =====
    "tabnine.experimentalAutoImports": true,
    "tabnine.suggestions.enabled": true,
    "tabnine.debounceMs": 100,
    // ===== BLACKBOX AI CONFIGURATION =====
    "blackboxapp.enabled": true,
    "blackboxapp.autoSuggest": true,
    // ===== C# DEVELOPMENT AUTOMATION =====
    "csharp.format.enable": true,
    "csharp.semanticHighlighting.enabled": true,
    // ===== AUTO-SAVE AND FORMATTING =====
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "editor.formatOnSave": true,
    "editor.formatOnType": true,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "explicit"
    },
    // ===== INTELLIGENT CODE COMPLETION =====
    "editor.suggestOnTriggerCharacters": true,
    "editor.acceptSuggestionOnCommitCharacter": true,
    "editor.tabCompletion": "on",
    "editor.quickSuggestions": {
        "other": true,
        "comments": true,
        "strings": true
    },
    "editor.inlineSuggest.enabled": true,
    // ===== BUILD AND TASK AUTOMATION =====
    "task.autoDetect": "on",
    "tasks.saveBeforeRun": "always",
    // ===== PRODUCTIVITY ENHANCEMENTS =====
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "workbench.editor.enablePreview": false,
    "workbench.startupEditor": "none",
    // ===== LANGUAGE-SPECIFIC SETTINGS =====
    "[csharp]": {
        "editor.defaultFormatter": "ms-dotnettools.csharp",
        "editor.tabSize": 4,
        "editor.insertSpaces": true
    },
    "[powershell]": {
        "editor.defaultFormatter": "ms-vscode.powershell",
        "editor.tabSize": 4
    },
    // ===== REVIT-SPECIFIC CONFIGURATIONS =====
    "files.associations": {
        "*.addin": "xml",
        "*.xaml": "xml"
    },
    "dotnet.inlayHints.enableInlayHintsForParameters": true,
    "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true,
    // ===== CLAUDE DESKTOP INTEGRATION =====
    "claude.apiKey": "",
    "claude.model": "claude-3-sonnet-20240229",
    "claude.maxTokens": 4096,
    "claude.temperature": 0.1,
    // ===== MCP (MODEL CONTEXT PROTOCOL) SETTINGS =====
    "mcp.servers": {
        "filesystem": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-filesystem",
                "src/RevitAddIn2025"
            ],
            "env": {
                "NODE_ENV": "development"
            }
        },
        "git": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-git",
                "--repository",
                "."
            ],
            "env": {
                "NODE_ENV": "development"
            }
        }
    },
    // ===== HOT RELOAD DEVELOPMENT =====
    "revit.addinsPath": "C:\\ProgramData\\Autodesk\\Revit\\Addins\\2025",
    "revit.hotReload.enabled": true,
    "revit.hotReload.watchPattern": "**/*.cs",
    "revit.hotReload.buildCommand": ".\\Development\\HotReloadBuild.ps1",
    // ===== TERMINAL CONFIGURATION =====
    "terminal.integrated.defaultProfile.windows": "PowerShell",
    "terminal.integrated.profiles.windows": {
        "PowerShell": {
            "source": "PowerShell",
            "icon": "terminal-powershell",
            "args": [
                "-NoExit",
                "-Command",
                "Write-Host '🚀 RevitAddIn2025 Development Environment Ready!' -ForegroundColor Cyan"
            ]
        }
    }
}