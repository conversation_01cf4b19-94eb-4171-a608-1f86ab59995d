# FINAL CLEANUP LOCKED FILES - RevitAddIn2025
# Handle remaining locked files with advanced techniques

$ErrorActionPreference = "Continue"

Write-Host ""
Write-Host "FINAL CLEANUP LOCKED FILES - RevitAddIn2025" -ForegroundColor Magenta
Write-Host "===========================================" -ForegroundColor Magenta

# Target the specific locked files
$lockedFiles = @(
    "C:\ProgramData\Autodesk\Revit\Addins\2025\RevitAddIn2025.dll"
)

Write-Host ""
Write-Host "STEP 1: CHECK PROCESS LOCKS" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan

# Check if any processes are using these files
foreach ($file in $lockedFiles) {
    if (Test-Path $file) {
        Write-Host "Checking locks on: $file" -ForegroundColor Yellow
        
        # Try to get file handle information
        try {
            $fileInfo = Get-Item $file
            Write-Host "  File exists: $($fileInfo.Name) ($([math]::Round($fileInfo.Length / 1024, 2)) KB)" -ForegroundColor White
            Write-Host "  Last modified: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
            Write-Host "  Attributes: $($fileInfo.Attributes)" -ForegroundColor Gray
        } catch {
            Write-Host "  Cannot access file info: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "File not found: $file" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "STEP 2: ADVANCED REMOVAL TECHNIQUES" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

foreach ($file in $lockedFiles) {
    if (Test-Path $file) {
        Write-Host "Attempting to remove: $file" -ForegroundColor Yellow
        
        # Technique 1: Change file attributes
        try {
            Set-ItemProperty -Path $file -Name Attributes -Value Normal
            Write-Host "  Cleared file attributes" -ForegroundColor Green
        } catch {
            Write-Host "  Failed to clear attributes: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Technique 2: Take ownership (requires admin)
        try {
            & takeown /f $file /a 2>$null
            Write-Host "  Attempted to take ownership" -ForegroundColor Green
        } catch {
            Write-Host "  Takeown failed (may need admin rights)" -ForegroundColor Yellow
        }
        
        # Technique 3: Grant full permissions
        try {
            & icacls $file /grant Everyone:F 2>$null
            Write-Host "  Granted full permissions" -ForegroundColor Green
        } catch {
            Write-Host "  Permission grant failed" -ForegroundColor Yellow
        }
        
        # Technique 4: Force delete with PowerShell
        try {
            Remove-Item -Path $file -Force -ErrorAction Stop
            Write-Host "  SUCCESS: File removed with PowerShell" -ForegroundColor Green
        } catch {
            Write-Host "  PowerShell removal failed: $($_.Exception.Message)" -ForegroundColor Red
            
            # Technique 5: Try with cmd del command
            try {
                & cmd /c "del /f /q `"$file`"" 2>$null
                if (-not (Test-Path $file)) {
                    Write-Host "  SUCCESS: File removed with CMD" -ForegroundColor Green
                } else {
                    Write-Host "  CMD removal failed" -ForegroundColor Red
                }
            } catch {
                Write-Host "  CMD removal failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""
Write-Host "STEP 3: CLEAN MINIMAL TEST DEPLOYMENT" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# Remove the minimal test deployment since we don't need it anymore
$minimalFolder = "C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2025\RevitAddIn2025_Minimal_20250527_184733"
if (Test-Path $minimalFolder) {
    try {
        Remove-Item -Path $minimalFolder -Recurse -Force
        Write-Host "SUCCESS: Removed minimal test deployment" -ForegroundColor Green
    } catch {
        Write-Host "Failed to remove minimal test deployment: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "Minimal test deployment already removed" -ForegroundColor Green
}

Write-Host ""
Write-Host "STEP 4: VERIFY FINAL CLEAN STATE" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Check what's left
$revitAddinsFolder = "$env:APPDATA\Autodesk\Revit\Addins\2025"
$revitProgramDataFolder = "$env:PROGRAMDATA\Autodesk\Revit\Addins\2025"

Write-Host "CURRENT DEPLOYMENT STATE:" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Check AppData deployment (should exist)
$correctFolder = Join-Path $revitAddinsFolder "RevitAddIn2025"
$correctAddin = Join-Path $revitAddinsFolder "RevitAddIn2025.addin"

if (Test-Path $correctFolder) {
    Write-Host "✅ Correct deployment folder exists" -ForegroundColor Green
    $files = Get-ChildItem -Path $correctFolder
    foreach ($file in $files) {
        $sizeKB = [math]::Round($file.Length / 1024, 2)
        Write-Host "   $($file.Name) ($sizeKB KB)" -ForegroundColor White
    }
} else {
    Write-Host "❌ Correct deployment folder missing!" -ForegroundColor Red
}

if (Test-Path $correctAddin) {
    Write-Host "✅ Correct .addin file exists" -ForegroundColor Green
} else {
    Write-Host "❌ Correct .addin file missing!" -ForegroundColor Red
}

# Check for remaining unwanted files
Write-Host ""
Write-Host "REMAINING UNWANTED FILES:" -ForegroundColor Yellow
$unwantedFound = $false

foreach ($file in $lockedFiles) {
    if (Test-Path $file) {
        Write-Host "❌ Still exists: $file" -ForegroundColor Red
        $unwantedFound = $true
    }
}

if (-not $unwantedFound) {
    Write-Host "✅ No unwanted files found" -ForegroundColor Green
}

Write-Host ""
Write-Host "STEP 5: CREATE DEPLOYMENT VERIFICATION SCRIPT" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Cyan

# Create a quick verification script for future use
$verificationScript = @"
# Quick deployment verification
`$correctDll = "C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2025\RevitAddIn2025\RevitAddIn2025.dll"
`$correctAddin = "C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2025\RevitAddIn2025.addin"

Write-Host "DEPLOYMENT VERIFICATION" -ForegroundColor Cyan
if (Test-Path `$correctDll) {
    `$size = [math]::Round((Get-Item `$correctDll).Length / 1024, 2)
    Write-Host "✅ DLL exists (`$size KB)" -ForegroundColor Green
} else {
    Write-Host "❌ DLL missing" -ForegroundColor Red
}

if (Test-Path `$correctAddin) {
    Write-Host "✅ .addin file exists" -ForegroundColor Green
} else {
    Write-Host "❌ .addin file missing" -ForegroundColor Red
}
"@

Set-Content -Path (Join-Path $PSScriptRoot "VERIFY_DEPLOYMENT.ps1") -Value $verificationScript
Write-Host "Created verification script: VERIFY_DEPLOYMENT.ps1" -ForegroundColor Green

Write-Host ""
Write-Host "FINAL CLEANUP COMPLETED!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

Write-Host ""
Write-Host "SUMMARY:" -ForegroundColor Cyan
Write-Host "- Attempted advanced removal of locked files" -ForegroundColor White
Write-Host "- Cleaned minimal test deployment" -ForegroundColor White
Write-Host "- Verified correct deployment integrity" -ForegroundColor White
Write-Host "- Created verification script for future use" -ForegroundColor White

Write-Host ""
Write-Host "READY FOR TESTING!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host "The plugin deployment is now clean and ready." -ForegroundColor White
Write-Host "You can now start Revit 2025 to test the plugin." -ForegroundColor White

Write-Host ""
Write-Host "EXPECTED BEHAVIOR:" -ForegroundColor Yellow
Write-Host "1. Start Revit 2025" -ForegroundColor White
Write-Host "2. Look for startup dialogs:" -ForegroundColor White
Write-Host "   - '🚨 PLUGIN LOADING TEST 🚨'" -ForegroundColor White
Write-Host "   - '✅ RIBBON CREATED'" -ForegroundColor White
Write-Host "3. Check for 'RevitAddIn2025' tab in ribbon" -ForegroundColor White
Write-Host "4. Test the 7 colored buttons" -ForegroundColor White
