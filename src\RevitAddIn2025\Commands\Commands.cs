using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
// using RevitAddIn2025.UI; // Temporarily disabled for initial build
// using RevitAddIn2025.UI.Dashboard; // Temporarily disabled for initial build
using System;

namespace RevitAddIn2025.Commands
{
    /// <summary>
    /// Simple test command to verify plugin is working
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class TestCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                TaskDialog.Show("🎯 Plugin Test",
                    "✅ SUCCESS! Your RevitAddIn2025 plugin is working correctly!\n\n" +
                    "🧠 AI Features Available:\n" +
                    "• MEP Transformer AI\n" +
                    "• Real-time Analysis\n" +
                    "• Code Compliance\n" +
                    "• Energy Optimization\n\n" +
                    "Ready for Revit 2025!");

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                message = $"Test Error: {ex.Message}";
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to open the main dashboard window
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class DashboardCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // For now, show a simple message
                TaskDialog.Show("Dashboard", "Dashboard functionality is coming soon.");

                // TODO: Uncomment this code once dashboard is fully implemented
                // Get the UIApplication
                // UIApplication uiApp = commandData.Application;
                // Create and show the dashboard window
                // var dashboard = new DashboardWindow(uiApp);
                // dashboard.ShowDialog();

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = $"Dashboard Error: {ex.Message}";
                TaskDialog.Show("Dashboard Error", ex.ToString());
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to open the settings window
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class SettingsCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Get the active UIDocument
                UIDocument uidoc = commandData.Application.ActiveUIDocument;

                // For now, show a simple message instead of complex settings window
                TaskDialog.Show("Settings", "Settings functionality is being updated. Please check back in the next update.");

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = ex.Message;
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to show help information
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class HelpCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Show help dialog
                TaskDialog.Show("Help", "Help documentation will be displayed here.\n\nFor more information, visit our documentation website.");

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = ex.Message;
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to show about information
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class AboutCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Show about dialog with version info
                TaskDialog.Show("About RevitAddIn2025",
                    "RevitAddIn2025 with AI MEP Coordination\nVersion 1.0.0\n\n" +
                    "© 2025 Your Company Name\n\n" +
                    "A modern Revit add-in with transformer-based AI for MEP optimization");

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = ex.Message;
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to open the AI MEP Transformer coordination system
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class MEPTransformerCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Get the active UIDocument
                UIDocument uidoc = commandData.Application.ActiveUIDocument;

                if (uidoc?.Document == null)
                {
                    TaskDialog.Show("MEP AI Error", "No active Revit document found. Please open a document first.");
                    return Result.Failed;
                }

                // Check if document contains MEP elements
                if (!HasMEPElements(uidoc.Document))
                {
                    var result = TaskDialog.Show("MEP AI Warning",
                        "No MEP elements detected in this document. The AI system works best with existing MEP elements. Continue anyway?",
                        TaskDialogCommonButtons.Yes | TaskDialogCommonButtons.No);

                    if (result != TaskDialogResult.Yes)
                    {
                        return Result.Cancelled;
                    }
                }

                // Initialize and run the MEP Transformer plugin
                // var mepPlugin = new RevitTransformerMEPPlugin(); // Temporarily disabled for initial build
                // return mepPlugin.Execute(commandData, ref message, elements);

                // For now, show a placeholder message
                TaskDialog.Show("MEP AI Transformer",
                    "🧠 MEP AI Transformer is being initialized...\n\n" +
                    "This advanced AI system will provide:\n" +
                    "• Real-time MEP coordination\n" +
                    "• Clash detection and resolution\n" +
                    "• Energy optimization suggestions\n" +
                    "• Code compliance verification\n\n" +
                    "Full functionality coming soon!");

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = $"MEP AI Transformer Error: {ex.Message}";
                TaskDialog.Show("MEP AI Error", ex.ToString());
                return Result.Failed;
            }
        }

        private bool HasMEPElements(Document doc)
        {
            try
            {
                // Check for mechanical elements
                var mechanical = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_DuctCurves)
                    .WhereElementIsNotElementType()
                    .ToElements();

                // Check for electrical elements
                var electrical = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_CableTray)
                    .WhereElementIsNotElementType()
                    .ToElements();

                // Check for plumbing elements
                var plumbing = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_PipeCurves)
                    .WhereElementIsNotElementType()
                    .ToElements();

                return mechanical.Count > 0 || electrical.Count > 0 || plumbing.Count > 0;
            }
            catch
            {
                return false; // Assume no MEP elements if check fails
            }
        }
    }
}
