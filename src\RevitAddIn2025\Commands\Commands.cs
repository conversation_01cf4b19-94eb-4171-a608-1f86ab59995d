using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.Utilities;
using System;

namespace RevitAddIn2025.Commands
{
    /// <summary>
    /// Simple test command to verify plugin is working
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class TestCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Test WPF functionality first
                try
                {
                    Logger.Info("Testing WPF functionality");

                    // Create a simple WPF window to test
                    var testWindow = new System.Windows.Window
                    {
                        Title = "WPF Test - RevitAddIn2025",
                        Width = 400,
                        Height = 300,
                        WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen,
                        Content = new System.Windows.Controls.TextBlock
                        {
                            Text = "✅ WPF is working correctly!\n\nThis confirms that our programmatic WPF windows should work.",
                            TextAlignment = System.Windows.TextAlignment.Center,
                            VerticalAlignment = System.Windows.VerticalAlignment.Center,
                            FontSize = 14,
                            Margin = new System.Windows.Thickness(20)
                        }
                    };

                    testWindow.ShowDialog();
                    Logger.Info("WPF test window closed successfully");
                }
                catch (Exception wpfEx)
                {
                    Logger.Error($"WPF test failed: {wpfEx.Message}", wpfEx);
                    TaskDialog.Show("WPF Test Failed",
                        $"WPF functionality test failed:\n{wpfEx.Message}\n\nThis explains why the Dashboard and Settings windows are falling back to TaskDialogs.");
                    return Result.Failed;
                }

                TaskDialog.Show("🎯 Plugin Test - HOT RELOAD READY!",
                    "✅ SUCCESS! Your RevitAddIn2025 plugin is working correctly!\n\n" +
                    "🔥 HOT RELOAD FEATURES:\n" +
                    "• File watcher for automatic builds\n" +
                    "• Manual reload with 🔄 Dev Reload button\n" +
                    "• VSCode integration with tasks\n" +
                    "• Claude Desktop AI assistance\n\n" +
                    "🧠 AI Features Available:\n" +
                    "• MEP Transformer AI\n" +
                    "• Real-time Analysis\n" +
                    "• Code Compliance\n" +
                    "• Energy Optimization\n\n" +
                    "✅ WPF Test Passed - Dashboard and Settings should work!\n\n" +
                    "🎯 Ready for hot reload development!");

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                message = $"Test Error: {ex.Message}";
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to open the main dashboard window
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class DashboardCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Get the UIApplication and document
                UIApplication uiApp = commandData.Application;
                UIDocument uiDoc = uiApp.ActiveUIDocument;

                if (uiDoc == null)
                {
                    TaskDialog.Show("Dashboard Error", "No active document found.\nPlease open a Revit project to view the dashboard.");
                    return Result.Failed;
                }

                // Try to create and show the WPF Dashboard window
                try
                {
                    Logger.Info("Creating Dashboard WPF window");

                    // Ensure we're on the UI thread
                    if (System.Windows.Application.Current?.Dispatcher != null)
                    {
                        var dashboard = new RevitAddIn2025.UI.Dashboard.DashboardWindow(uiDoc);
                        Logger.Info("Dashboard window created successfully");
                        dashboard.ShowDialog();
                        Logger.Info("Dashboard window closed");
                        return Result.Succeeded;
                    }
                    else
                    {
                        // Create WPF application if needed
                        if (System.Windows.Application.Current == null)
                        {
                            new System.Windows.Application();
                        }

                        var dashboard = new RevitAddIn2025.UI.Dashboard.DashboardWindow(uiDoc);
                        Logger.Info("Dashboard window created successfully");
                        dashboard.ShowDialog();
                        Logger.Info("Dashboard window closed");
                        return Result.Succeeded;
                    }
                }
                catch (Exception wpfEx)
                {
                    // Log detailed error information
                    Logger.Error($"WPF Dashboard window failed to load: {wpfEx.Message}", wpfEx);
                    Logger.Error($"WPF Exception Type: {wpfEx.GetType().Name}");
                    Logger.Error($"WPF Stack Trace: {wpfEx.StackTrace}");

                    // Fallback to enhanced TaskDialog if WPF fails
                    Document doc = uiDoc.Document;
                    TaskDialog dashboardDialog = new TaskDialog("RevitAddIn2025 - Dashboard");
                    dashboardDialog.MainInstruction = "📊 Project Dashboard (Fallback Mode)";

                    string projectInfo = "📋 PROJECT INFORMATION:\n";
                    projectInfo += $"• Project: {doc.Title}\n";
                    projectInfo += $"• File: {doc.PathName}\n";
                    projectInfo += $"• Modified: {(doc.IsModified ? "Yes" : "No")}\n\n";

                    // Count elements
                    var allElements = new FilteredElementCollector(doc).WhereElementIsNotElementType().ToElements();
                    projectInfo += $"📈 ELEMENT STATISTICS:\n";
                    projectInfo += $"• Total Elements: {allElements.Count}\n";

                    // Count MEP elements
                    var mepElements = new FilteredElementCollector(doc)
                        .OfCategory(BuiltInCategory.OST_DuctCurves)
                        .WhereElementIsNotElementType().ToElements().Count +
                        new FilteredElementCollector(doc)
                        .OfCategory(BuiltInCategory.OST_PipeCurves)
                        .WhereElementIsNotElementType().ToElements().Count;

                    projectInfo += $"• MEP Elements: {mepElements}\n\n";
                    projectInfo += $"⚠️ WPF UI Error: {wpfEx.Message}\n";
                    projectInfo += $"⚠️ Error Type: {wpfEx.GetType().Name}\n";
                    projectInfo += "Using fallback dialog mode.\n\n";
                    projectInfo += "💡 Check the log file for detailed error information.";

                    dashboardDialog.MainContent = projectInfo;
                    dashboardDialog.Show();
                }

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = $"Dashboard Error: {ex.Message}";
                TaskDialog.Show("Dashboard Error", ex.ToString());
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to open the settings window
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class SettingsCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Get the UIApplication and document
                UIApplication uiApp = commandData.Application;
                UIDocument uiDoc = uiApp.ActiveUIDocument;

                if (uiDoc == null)
                {
                    TaskDialog.Show("Settings Error", "No active document found.\nPlease open a Revit project to access settings.");
                    return Result.Failed;
                }

                // Try to create and show the WPF Settings window
                try
                {
                    Logger.Info("Creating Settings WPF window");

                    // Ensure we're on the UI thread
                    if (System.Windows.Application.Current?.Dispatcher != null)
                    {
                        var settings = new RevitAddIn2025.UI.Settings.SettingsWindow(uiDoc);
                        Logger.Info("Settings window created successfully");
                        settings.ShowDialog();
                        Logger.Info("Settings window closed");
                        return Result.Succeeded;
                    }
                    else
                    {
                        // Create WPF application if needed
                        if (System.Windows.Application.Current == null)
                        {
                            new System.Windows.Application();
                        }

                        var settings = new RevitAddIn2025.UI.Settings.SettingsWindow(uiDoc);
                        Logger.Info("Settings window created successfully");
                        settings.ShowDialog();
                        Logger.Info("Settings window closed");
                        return Result.Succeeded;
                    }
                }
                catch (Exception wpfEx)
                {
                    // Log detailed error information
                    Logger.Error($"WPF Settings window failed to load: {wpfEx.Message}", wpfEx);
                    Logger.Error($"WPF Exception Type: {wpfEx.GetType().Name}");
                    Logger.Error($"WPF Stack Trace: {wpfEx.StackTrace}");

                    // Fallback to enhanced TaskDialog if WPF fails
                    TaskDialog settingsDialog = new TaskDialog("RevitAddIn2025 - Settings");
                    settingsDialog.MainInstruction = "⚙️ Plugin Settings & Configuration (Fallback Mode)";
                    settingsDialog.MainContent =
                        "🔧 CURRENT SETTINGS:\n" +
                        "• Hot Reload: Enabled\n" +
                        "• Auto-refresh: Enabled\n" +
                        "• Theme: Apple-inspired\n" +
                        "• AI Features: Active\n\n" +
                        "🎯 AVAILABLE CONFIGURATIONS:\n" +
                        "• Dashboard refresh interval\n" +
                        "• MEP analysis parameters\n" +
                        "• UI theme preferences\n" +
                        "• Development mode settings\n\n" +
                        $"⚠️ WPF UI Error: {wpfEx.Message}\n" +
                        $"⚠️ Error Type: {wpfEx.GetType().Name}\n" +
                        "Using fallback dialog mode.\n\n" +
                        "💡 Check the log file for detailed error information.";

                    settingsDialog.Show();
                }

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = ex.Message;
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to show help information
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class HelpCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Show comprehensive help dialog
                TaskDialog helpDialog = new TaskDialog("RevitAddIn2025 - Help");
                helpDialog.MainInstruction = "🎯 RevitAddIn2025 Help & Documentation";
                helpDialog.MainContent =
                    "📊 DASHBOARD: View project analytics and MEP system overview\n" +
                    "⚙️ SETTINGS: Configure plugin preferences and AI parameters\n" +
                    "🤖 MEP AI: AI-powered MEP coordination and optimization\n" +
                    "🔄 DEV RELOAD: Hot reload for development (updates code without restart)\n\n" +
                    "🔥 HOT RELOAD WORKFLOW:\n" +
                    "1. Make code changes in VSCode\n" +
                    "2. Build: Ctrl+Shift+P → 'Tasks: Run Task' → 'Build'\n" +
                    "3. Deploy: Run 'Deploy' task\n" +
                    "4. Click 🔄 Dev Reload button in Revit\n\n" +
                    "🧠 AI FEATURES:\n" +
                    "• Real-time MEP analysis\n" +
                    "• Clash detection & resolution\n" +
                    "• Energy optimization\n" +
                    "• Code compliance checking\n\n" +
                    "For detailed documentation, visit our website or check the project README.";

                helpDialog.Show();

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = ex.Message;
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to show about information
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class AboutCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Show enhanced about dialog with detailed version info
                TaskDialog aboutDialog = new TaskDialog("About RevitAddIn2025");
                aboutDialog.MainInstruction = "🚀 RevitAddIn2025 with AI MEP Coordination";
                aboutDialog.MainContent =
                    "Version: 1.0.0 (Hot Reload Edition)\n" +
                    "Build: " + DateTime.Now.ToString("yyyy.MM.dd") + "\n" +
                    "Revit API: 2025\n\n" +
                    "🔥 FEATURES:\n" +
                    "• Hot Reload Development Workflow\n" +
                    "• AI-Powered MEP Analysis\n" +
                    "• Real-time Project Dashboard\n" +
                    "• Apple-Inspired Modern UI\n" +
                    "• Claude Desktop Integration\n\n" +
                    "🧠 AI CAPABILITIES:\n" +
                    "• Transformer Neural Networks\n" +
                    "• MEP Clash Detection\n" +
                    "• Energy Optimization\n" +
                    "• Code Compliance Verification\n\n" +
                    "© 2025 Raghav at Zyeta - Professional Revit Add-in Developer\n\n" +
                    "Built with ❤️ for the AEC industry";

                aboutDialog.Show();

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = ex.Message;
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to open the AI MEP Transformer coordination system
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class MEPTransformerCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Get the active UIDocument
                UIDocument uidoc = commandData.Application.ActiveUIDocument;

                if (uidoc?.Document == null)
                {
                    TaskDialog.Show("MEP AI Error", "No active Revit document found. Please open a document first.");
                    return Result.Failed;
                }

                // Check if document contains MEP elements
                if (!HasMEPElements(uidoc.Document))
                {
                    var result = TaskDialog.Show("MEP AI Warning",
                        "No MEP elements detected in this document. The AI system works best with existing MEP elements. Continue anyway?",
                        TaskDialogCommonButtons.Yes | TaskDialogCommonButtons.No);

                    if (result != TaskDialogResult.Yes)
                    {
                        return Result.Cancelled;
                    }
                }

                // Initialize and run the MEP Transformer plugin
                // var mepPlugin = new RevitTransformerMEPPlugin(); // Temporarily disabled for initial build
                // return mepPlugin.Execute(commandData, ref message, elements);

                // For now, show a placeholder message
                TaskDialog.Show("MEP AI Transformer",
                    "🧠 MEP AI Transformer is being initialized...\n\n" +
                    "This advanced AI system will provide:\n" +
                    "• Real-time MEP coordination\n" +
                    "• Clash detection and resolution\n" +
                    "• Energy optimization suggestions\n" +
                    "• Code compliance verification\n\n" +
                    "Full functionality coming soon!");

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                // Display error message
                message = $"MEP AI Transformer Error: {ex.Message}";
                TaskDialog.Show("MEP AI Error", ex.ToString());
                return Result.Failed;
            }
        }

        private bool HasMEPElements(Document doc)
        {
            try
            {
                // Check for mechanical elements
                var mechanical = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_DuctCurves)
                    .WhereElementIsNotElementType()
                    .ToElements();

                // Check for electrical elements
                var electrical = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_CableTray)
                    .WhereElementIsNotElementType()
                    .ToElements();

                // Check for plumbing elements
                var plumbing = new FilteredElementCollector(doc)
                    .OfCategory(BuiltInCategory.OST_PipeCurves)
                    .WhereElementIsNotElementType()
                    .ToElements();

                return mechanical.Count > 0 || electrical.Count > 0 || plumbing.Count > 0;
            }
            catch
            {
                return false; // Assume no MEP elements if check fails
            }
        }
    }
}
