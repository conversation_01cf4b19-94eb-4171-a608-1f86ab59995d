# COMPLETE CLEANUP AND FIX - RevitAddIn2025
# Comprehensive cleanup of all RevitAddIn2025 references and proper deployment

$ErrorActionPreference = "Stop"

Write-Host ""
Write-Host "COMPLETE CLEANUP AND FIX - RevitAddIn2025" -ForegroundColor Magenta
Write-Host "=========================================" -ForegroundColor Magenta

# Configuration
$revitAddinsFolder = "$env:APPDATA\Autodesk\Revit\Addins\2025"
$revitProgramDataFolder = "$env:PROGRAMDATA\Autodesk\Revit\Addins\2025"
$scriptDir = $PSScriptRoot
$projectRoot = Split-Path -Parent $scriptDir
$buildOutput = Join-Path $projectRoot "bin\Release"

Write-Host ""
Write-Host "STEP 1: COMPREHENSIVE CLEANUP" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

# Search locations for cleanup
$searchLocations = @(
    $revitAddinsFolder,
    $revitProgramDataFolder
)

$totalCleaned = 0

foreach ($location in $searchLocations) {
    if (Test-Path $location) {
        Write-Host "Cleaning location: $location" -ForegroundColor Yellow
        
        # Find ALL files related to RevitAddIn2025
        $patterns = @(
            "*RevitAddIn2025*",
            "*RevitAddIn*",
            "RevitAddIn2025*"
        )
        
        foreach ($pattern in $patterns) {
            $items = Get-ChildItem -Path $location -Filter $pattern -ErrorAction SilentlyContinue
            foreach ($item in $items) {
                try {
                    if ($item.PSIsContainer) {
                        Remove-Item -Path $item.FullName -Recurse -Force
                        Write-Host "  REMOVED FOLDER: $($item.Name)" -ForegroundColor Green
                    } else {
                        Remove-Item -Path $item.FullName -Force
                        Write-Host "  REMOVED FILE: $($item.Name)" -ForegroundColor Green
                    }
                    $totalCleaned++
                } catch {
                    Write-Host "  FAILED TO REMOVE: $($item.Name) - $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
        
        # Also check for .addin files that might reference RevitAddIn2025
        $addinFiles = Get-ChildItem -Path $location -Filter "*.addin" -ErrorAction SilentlyContinue
        foreach ($addinFile in $addinFiles) {
            try {
                $content = Get-Content -Path $addinFile.FullName -ErrorAction SilentlyContinue
                if ($content -and ($content -match "RevitAddIn2025")) {
                    Remove-Item -Path $addinFile.FullName -Force
                    Write-Host "  REMOVED ADDIN: $($addinFile.Name) (contained RevitAddIn2025 reference)" -ForegroundColor Green
                    $totalCleaned++
                }
            } catch {
                Write-Host "  FAILED TO CHECK ADDIN: $($addinFile.Name)" -ForegroundColor Yellow
            }
        }
    }
}

Write-Host "Total items cleaned: $totalCleaned" -ForegroundColor Cyan

Write-Host ""
Write-Host "STEP 2: VERIFY BUILD OUTPUT" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan

$dllPath = Join-Path $buildOutput "RevitAddIn2025.dll"
if (-not (Test-Path $dllPath)) {
    Write-Host "Building project..." -ForegroundColor Yellow
    $projectFile = Join-Path $scriptDir "RevitAddIn2025.csproj"
    & dotnet build $projectFile -c Release -p:Platform=x64 --verbosity minimal
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Build failed" -ForegroundColor Red
        exit 1
    }
}

if (Test-Path $dllPath) {
    $dllInfo = Get-Item $dllPath
    $sizeKB = [math]::Round($dllInfo.Length / 1024, 2)
    Write-Host "SUCCESS: DLL found - Size: $sizeKB KB" -ForegroundColor Green
    Write-Host "  Path: $dllPath" -ForegroundColor Gray
    Write-Host "  Modified: $($dllInfo.LastWriteTime)" -ForegroundColor Gray
} else {
    Write-Host "ERROR: DLL not found after build" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "STEP 3: CREATE CLEAN DEPLOYMENT" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# Create a clean deployment with the EXACT name expected
$deploymentFolder = Join-Path $revitAddinsFolder "RevitAddIn2025"
$addinFilePath = Join-Path $revitAddinsFolder "RevitAddIn2025.addin"

# Create deployment directory
New-Item -ItemType Directory -Path $deploymentFolder -Force | Out-Null
Write-Host "Created deployment folder: RevitAddIn2025" -ForegroundColor Green

# Copy DLL with exact expected name
$targetDllPath = Join-Path $deploymentFolder "RevitAddIn2025.dll"
Copy-Item -Path $dllPath -Destination $targetDllPath -Force
Write-Host "Copied DLL to: $targetDllPath" -ForegroundColor Green

# Copy PDB if exists
$pdbPath = Join-Path $buildOutput "RevitAddIn2025.pdb"
if (Test-Path $pdbPath) {
    Copy-Item -Path $pdbPath -Destination $deploymentFolder -Force
    Write-Host "Copied PDB file" -ForegroundColor Green
}

Write-Host ""
Write-Host "STEP 4: CREATE CORRECT .ADDIN FILE" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# Create .addin file with EXACT expected paths
$addinContent = @"
<?xml version="1.0" encoding="utf-8"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>RevitAddIn2025</Name>
    <Assembly>$targetDllPath</Assembly>
    <AddInId>B8399B5E-9B39-42E9-9B1D-869C8F59E76E</AddInId>
    <FullClassName>RevitAddIn2025.App.RevitApplication</FullClassName>
    <VendorId>REVIT2025</VendorId>
    <VendorDescription>Revit 2025 Add-in with Apple-inspired UI</VendorDescription>
  </AddIn>
</RevitAddIns>
"@

Set-Content -Path $addinFilePath -Value $addinContent -Encoding UTF8
Write-Host "Created .addin file: RevitAddIn2025.addin" -ForegroundColor Green
Write-Host "  Assembly path: $targetDllPath" -ForegroundColor Gray

Write-Host ""
Write-Host "STEP 5: VERIFICATION" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan

# Verify all files exist
if (Test-Path $targetDllPath) {
    $deployedInfo = Get-Item $targetDllPath
    $deployedSizeKB = [math]::Round($deployedInfo.Length / 1024, 2)
    Write-Host "SUCCESS: DLL deployed correctly ($deployedSizeKB KB)" -ForegroundColor Green
} else {
    Write-Host "ERROR: DLL deployment failed" -ForegroundColor Red
    exit 1
}

if (Test-Path $addinFilePath) {
    Write-Host "SUCCESS: .addin file created correctly" -ForegroundColor Green
} else {
    Write-Host "ERROR: .addin file creation failed" -ForegroundColor Red
    exit 1
}

# Test assembly loading
try {
    $assembly = [System.Reflection.Assembly]::LoadFrom($targetDllPath)
    Write-Host "SUCCESS: Assembly loads without errors" -ForegroundColor Green
    Write-Host "  Assembly: $($assembly.FullName)" -ForegroundColor Gray
} catch {
    Write-Host "WARNING: Assembly loading test failed: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "  This is expected outside Revit context" -ForegroundColor Gray
}

Write-Host ""
Write-Host "STEP 6: FINAL VERIFICATION" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan

# List all files in the deployment
Write-Host "Deployment contents:" -ForegroundColor White
$deployedFiles = Get-ChildItem -Path $deploymentFolder
foreach ($file in $deployedFiles) {
    Write-Host "  $($file.Name) ($([math]::Round($file.Length / 1024, 2)) KB)" -ForegroundColor Gray
}

Write-Host ""
Write-Host "DEPLOYMENT COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green

Write-Host ""
Write-Host "DEPLOYMENT SUMMARY:" -ForegroundColor Cyan
Write-Host "Folder: $deploymentFolder" -ForegroundColor White
Write-Host "DLL: $targetDllPath" -ForegroundColor White
Write-Host ".addin: $addinFilePath" -ForegroundColor White
Write-Host "Assembly Name: RevitAddIn2025.dll (EXACT MATCH)" -ForegroundColor White

Write-Host ""
Write-Host "CRITICAL INSTRUCTIONS:" -ForegroundColor Yellow
Write-Host "1. Close Revit COMPLETELY (wait 15 seconds)" -ForegroundColor White
Write-Host "2. Start Revit 2025" -ForegroundColor White
Write-Host "3. Look for startup dialogs" -ForegroundColor White
Write-Host "4. Check for RevitAddIn2025 tab in ribbon" -ForegroundColor White

Write-Host ""
Write-Host "This deployment uses the EXACT assembly name that Revit is looking for." -ForegroundColor Green
Write-Host "The error should now be resolved." -ForegroundColor Green
