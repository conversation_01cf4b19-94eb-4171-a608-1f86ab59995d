using System;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Autodesk.Revit.UI;
using Autodesk.Revit.ApplicationServices;
using RevitAddIn2025.Commands;

namespace RevitAddIn2025.Development
{
    /// <summary>
    /// Hot reload manager for development workflow
    /// Allows reloading commands without restarting Revit
    /// </summary>
    public static class HotReloadManager
    {
        private static FileSystemWatcher _watcher;
        private static string _assemblyPath;
        private static UIControlledApplication _uiApplication;
        private static RibbonPanel _panel;
        private static bool _isWatching = false;
        private static readonly object _lock = new object();

        /// <summary>
        /// Initialize hot reload system
        /// </summary>
        public static void Initialize(UIControlledApplication application, RibbonPanel panel)
        {
            _uiApplication = application;
            _panel = panel;
            _assemblyPath = Assembly.GetExecutingAssembly().Location;

            StartWatching();
        }

        /// <summary>
        /// Start watching for file changes
        /// </summary>
        private static void StartWatching()
        {
            if (_isWatching) return;

            try
            {
                string watchPath = Path.GetDirectoryName(_assemblyPath);
                string fileName = Path.GetFileName(_assemblyPath);

                _watcher = new FileSystemWatcher(watchPath, fileName)
                {
                    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                    EnableRaisingEvents = true
                };

                _watcher.Changed += OnAssemblyChanged;
                _isWatching = true;

                TaskDialog.Show("🔥 Hot Reload Active",
                    "Hot reload is now active!\n\n" +
                    "✅ Watching: " + fileName + "\n" +
                    "📁 Path: " + watchPath + "\n\n" +
                    "Make changes to your code and rebuild - commands will update automatically!");
            }
            catch (Exception ex)
            {
                TaskDialog.Show("Hot Reload Error", $"Failed to start hot reload: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle assembly file changes
        /// </summary>
        private static void OnAssemblyChanged(object sender, FileSystemEventArgs e)
        {
            lock (_lock)
            {
                try
                {
                    // Debounce multiple events
                    Thread.Sleep(500);

                    // Reload commands
                    Task.Run(() => ReloadCommands());
                }
                catch (Exception ex)
                {
                    // Log error but don't crash
                    System.Diagnostics.Debug.WriteLine($"Hot reload error: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Reload all commands with new assembly
        /// </summary>
        private static void ReloadCommands()
        {
            try
            {
                // Show reload notification in debug output
                System.Diagnostics.Debug.WriteLine("🔄 Hot reloading commands...");

                // Create new command instances (this will use the updated assembly)
                var testCommand = new TestCommand();
                var dashboardCommand = new DashboardCommand();
                var settingsCommand = new SettingsCommand();
                var helpCommand = new HelpCommand();
                var aboutCommand = new AboutCommand();
                var mepCommand = new MEPTransformerCommand();

                // Show success notification
                System.Diagnostics.Debug.WriteLine("✅ Commands reloaded successfully!");

                // Show notification via TaskDialog on next idle
                ShowReloadNotificationAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Hot reload failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Show reload notification asynchronously
        /// </summary>
        private static void ShowReloadNotificationAsync()
        {
            Task.Run(() =>
            {
                Thread.Sleep(1000); // Wait a bit for the reload to complete

                // This will be shown in the debug output
                System.Diagnostics.Debug.WriteLine("🔄 Hot Reload Complete - Commands have been reloaded!");
            });
        }

        /// <summary>
        /// Stop watching for changes
        /// </summary>
        public static void Stop()
        {
            if (_watcher != null)
            {
                _watcher.EnableRaisingEvents = false;
                _watcher.Dispose();
                _watcher = null;
                _isWatching = false;
            }
        }

        /// <summary>
        /// Manual reload trigger for development
        /// </summary>
        public static void ManualReload()
        {
            ReloadCommands();
        }
    }
}
