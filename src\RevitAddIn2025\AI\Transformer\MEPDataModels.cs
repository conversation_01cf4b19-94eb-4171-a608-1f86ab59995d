using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using RevitAddIn2025.Models;

namespace RevitAddIn2025.AI.Transformer
{
    /// <summary>
    /// Represents a 3D position in space
    /// </summary>
    public class Position
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }

        public Position(float x, float y, float z)
        {
            X = x;
            Y = y;
            Z = z;
        }

        public float DistanceTo(Position other)
        {
            float dx = X - other.X;
            float dy = Y - other.Y;
            float dz = Z - other.Z;
            return (float)Math.Sqrt(dx * dx + dy * dy + dz * dz);
        }
    }

    /// <summary>
    /// Represents 3D rotation in degrees
    /// </summary>
    public class Rotation
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }

        public Rotation(float x, float y, float z)
        {
            X = x;
            Y = y;
            Z = z;
        }
    }

    /// <summary>
    /// Represents 3D dimensions (width, height, depth)
    /// </summary>
    public class Dimensions
    {
        public float Width { get; set; }
        public float Height { get; set; }
        public float Depth { get; set; }

        public Dimensions(float width, float height, float depth)
        {
            Width = width;
            Height = height;
            Depth = depth;
        }

        public float Volume => Width * Height * Depth;
    }

    /// <summary>
    /// Types of MEP elements
    /// </summary>
    public enum MEPElementType
    {
        DuctworkStraight,
        DuctworkElbow,
        DuctworkTee,
        AirTerminal,
        MechanicalEquipment,
        PipingStraight,
        PipingElbow,
        PipingTee,
        PipingValve,
        PlumbingFixture,
        ElectricalFixture,
        ElectricalPanel,
        ElectricalConduit,
        CableTray,
        FireAlarmDevice,
        SprinklerHead
    }

    /// <summary>
    /// Base class for MEP elements
    /// </summary>
    public class MEPElement
    {
        public Guid Id { get; set; }
        public MEPElementType ElementType { get; set; }
        public Position Position { get; set; }
        public Rotation Rotation { get; set; }
        public Dimensions Dimensions { get; set; }
        public Dictionary<string, object> Properties { get; set; }

        // Additional properties for transformer compatibility
        public Position Location => Position; // Alias for Position
        public MEPSystemType SystemType { get; set; }
        public ElementId ElementId { get; set; }
        public float Diameter { get; set; }
        public float FlowRate { get; set; }
        public float Pressure { get; set; }
        public float Temperature { get; set; } = 70.0f; // Default room temperature
        public float Velocity { get; set; }
        public float Efficiency { get; set; } = 0.8f; // Default efficiency

        public MEPElement()
        {
            Id = Guid.NewGuid();
            Properties = new Dictionary<string, object>();
        }

        public MEPElement Clone()
        {
            var clone = new MEPElement
            {
                Id = this.Id,
                ElementType = this.ElementType,
                Position = new Position(this.Position.X, this.Position.Y, this.Position.Z),
                Rotation = new Rotation(this.Rotation.X, this.Rotation.Y, this.Rotation.Z),
                Dimensions = new Dimensions(this.Dimensions.Width, this.Dimensions.Height, this.Dimensions.Depth)
            };

            // Copy properties
            foreach (var prop in this.Properties)
            {
                clone.Properties[prop.Key] = prop.Value;
            }

            return clone;
        }

        /// <summary>
        /// Checks if this element clashes with another element
        /// </summary>
        public bool ClashesWith(MEPElement other)
        {
            // Implementation would check for geometric intersection
            // This is a simplified placeholder - actual clash detection would be more complex

            // Calculate centers
            Position thisCenter = Position;
            Position otherCenter = other.Position;

            // Calculate half extents
            float thisHalfWidth = Dimensions.Width / 2;
            float thisHalfHeight = Dimensions.Height / 2;
            float thisHalfDepth = Dimensions.Depth / 2;

            float otherHalfWidth = other.Dimensions.Width / 2;
            float otherHalfHeight = other.Dimensions.Height / 2;
            float otherHalfDepth = other.Dimensions.Depth / 2;

            // Check for overlap in each dimension
            bool xOverlap = Math.Abs(thisCenter.X - otherCenter.X) < (thisHalfWidth + otherHalfWidth);
            bool yOverlap = Math.Abs(thisCenter.Y - otherCenter.Y) < (thisHalfHeight + otherHalfHeight);
            bool zOverlap = Math.Abs(thisCenter.Z - otherCenter.Z) < (thisHalfDepth + otherHalfDepth);

            return xOverlap && yOverlap && zOverlap;
        }
    }

    /// <summary>
    /// Represents constraints for MEP optimization
    /// </summary>
    public class MEPConstraints
    {
        public List<MEPElement> FixedElements { get; set; } = new List<MEPElement>();
        public List<MEPElement> BuildingElements { get; set; } = new List<MEPElement>();
        public Dictionary<MEPElementType, float> MinimumClearances { get; set; } = new Dictionary<MEPElementType, float>();
        public Dictionary<string, float> CodeRequirements { get; set; } = new Dictionary<string, float>();
        public List<SpatialZone> AccessZones { get; set; } = new List<SpatialZone>();
        public List<SpatialZone> ExclusionZones { get; set; } = new List<SpatialZone>();

        /// <summary>
        /// Validates if an element satisfies all constraints
        /// </summary>
        public bool ValidateElement(MEPElement element)
        {
            // Check for clashes with fixed elements
            foreach (var fixedElement in FixedElements)
            {
                if (element.ClashesWith(fixedElement))
                {
                    return false;
                }
            }

            // Check for clashes with building elements
            foreach (var buildingElement in BuildingElements)
            {
                if (element.ClashesWith(buildingElement))
                {
                    return false;
                }
            }

            // Check minimum clearances
            if (MinimumClearances.ContainsKey(element.ElementType))
            {
                float requiredClearance = MinimumClearances[element.ElementType];

                foreach (var otherElement in FixedElements.Concat(BuildingElements))
                {
                    float distance = element.Position.DistanceTo(otherElement.Position);
                    if (distance < requiredClearance)
                    {
                        return false;
                    }
                }
            }

            // Check exclusion zones
            foreach (var zone in ExclusionZones)
            {
                if (zone.ContainsElement(element))
                {
                    return false;
                }
            }

            // Further checks for code compliance would be implemented here

            return true;
        }
    }

    /// <summary>
    /// Represents a spatial zone (for access or exclusion areas)
    /// </summary>
    public class SpatialZone
    {
        public Position Center { get; set; }
        public Dimensions Size { get; set; }

        public SpatialZone(Position center, Dimensions size)
        {
            Center = center;
            Size = size;
        }

        public bool ContainsElement(MEPElement element)
        {
            // Check if element is inside this zone
            float halfWidth = Size.Width / 2;
            float halfHeight = Size.Height / 2;
            float halfDepth = Size.Depth / 2;

            bool xInside = Math.Abs(element.Position.X - Center.X) < halfWidth;
            bool yInside = Math.Abs(element.Position.Y - Center.Y) < halfHeight;
            bool zInside = Math.Abs(element.Position.Z - Center.Z) < halfDepth;

            return xInside && yInside && zInside;
        }
    }

    /// <summary>
    /// Training data for the MEP transformer model
    /// </summary>
    public class MEPTrainingData
    {
        public List<MEPElement> InputLayout { get; set; }
        public List<MEPElement> OptimalLayout { get; set; }
        public MEPConstraints Constraints { get; set; }
        public Dictionary<string, float> PerformanceMetrics { get; set; }

        public MEPTrainingData()
        {
            InputLayout = new List<MEPElement>();
            OptimalLayout = new List<MEPElement>();
            Constraints = new MEPConstraints();
            PerformanceMetrics = new Dictionary<string, float>();
        }
    }
}
