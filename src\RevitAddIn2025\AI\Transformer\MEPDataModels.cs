using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using RevitAddIn2025.Models;

namespace RevitAddIn2025.AI.Transformer
{
    /// <summary>
    /// Represents a 3D position in space
    /// </summary>
    public class Position
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }

        public Position(float x, float y, float z)
        {
            X = x;
            Y = y;
            Z = z;
        }

        public float DistanceTo(Position other)
        {
            float dx = X - other.X;
            float dy = Y - other.Y;
            float dz = Z - other.Z;
            return (float)Math.Sqrt(dx * dx + dy * dy + dz * dz);
        }
    }

    /// <summary>
    /// Represents 3D rotation in degrees
    /// </summary>
    public class Rotation
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }

        public Rotation(float x, float y, float z)
        {
            X = x;
            Y = y;
            Z = z;
        }
    }

    /// <summary>
    /// Represents 3D dimensions (width, height, depth)
    /// </summary>
    public class Dimensions
    {
        public float Width { get; set; }
        public float Height { get; set; }
        public float Depth { get; set; }

        public Dimensions(float width, float height, float depth)
        {
            Width = width;
            Height = height;
            Depth = depth;
        }

        public float Volume => Width * Height * Depth;
    }

    /// <summary>
    /// Types of MEP elements
    /// </summary>
    public enum MEPElementType
    {
        DuctworkStraight,
        DuctworkElbow,
        DuctworkTee,
        AirTerminal,
        MechanicalEquipment,
        PipingStraight,
        PipingElbow,
        PipingTee,
        PipingValve,
        PlumbingFixture,
        ElectricalFixture,
        ElectricalPanel,
        ElectricalConduit,
        CableTray,
        FireAlarmDevice,
        SprinklerHead
    }

    /// <summary>
    /// Base class for MEP elements
    /// </summary>
    public class MEPElement
    {
        public Guid Id { get; set; }
        public MEPElementType ElementType { get; set; }
        public Position Position { get; set; }
        public Rotation Rotation { get; set; }
        public Dimensions Dimensions { get; set; }
        public Dictionary<string, object> Properties { get; set; }

        // Additional properties for transformer compatibility
        public Position Location => Position; // Alias for Position
        public MEPSystemType SystemType { get; set; }
        public ElementId ElementId { get; set; }
        public float Diameter { get; set; }
        public float FlowRate { get; set; }
        public float Pressure { get; set; }
        public float Temperature { get; set; } = 70.0f; // Default room temperature
        public float Velocity { get; set; }
        public float Efficiency { get; set; } = 0.8f; // Default efficiency

        public MEPElement()
        {
            Id = Guid.NewGuid();
            Properties = new Dictionary<string, object>();
        }

        public MEPElement Clone()
        {
            var clone = new MEPElement
            {
                Id = this.Id,
                ElementType = this.ElementType,
                Position = new Position(this.Position.X, this.Position.Y, this.Position.Z),
                Rotation = new Rotation(this.Rotation.X, this.Rotation.Y, this.Rotation.Z),
                Dimensions = new Dimensions(this.Dimensions.Width, this.Dimensions.Height, this.Dimensions.Depth)
            };

            // Copy properties
            foreach (var prop in this.Properties)
            {
                clone.Properties[prop.Key] = prop.Value;
            }

            return clone;
        }

        /// <summary>
        /// Checks if this element clashes with another element
        /// </summary>
        public bool ClashesWith(MEPElement other)
        {
            // Implementation would check for geometric intersection
            // This is a simplified placeholder - actual clash detection would be more complex

            // Calculate centers
            Position thisCenter = Position;
            Position otherCenter = other.Position;

            // Calculate half extents
            float thisHalfWidth = Dimensions.Width / 2;
            float thisHalfHeight = Dimensions.Height / 2;
            float thisHalfDepth = Dimensions.Depth / 2;

            float otherHalfWidth = other.Dimensions.Width / 2;
            float otherHalfHeight = other.Dimensions.Height / 2;
            float otherHalfDepth = other.Dimensions.Depth / 2;

            // Check for overlap in each dimension
            bool xOverlap = Math.Abs(thisCenter.X - otherCenter.X) < (thisHalfWidth + otherHalfWidth);
            bool yOverlap = Math.Abs(thisCenter.Y - otherCenter.Y) < (thisHalfHeight + otherHalfHeight);
            bool zOverlap = Math.Abs(thisCenter.Z - otherCenter.Z) < (thisHalfDepth + otherHalfDepth);

            return xOverlap && yOverlap && zOverlap;
        }
    }

    /// <summary>
    /// Represents constraints for MEP optimization
    /// </summary>
    public class MEPConstraints
    {
        public List<MEPElement> FixedElements { get; set; } = new List<MEPElement>();
        public List<MEPElement> BuildingElements { get; set; } = new List<MEPElement>();
        public Dictionary<MEPElementType, float> MinimumClearances { get; set; } = new Dictionary<MEPElementType, float>();
        public Dictionary<string, float> CodeRequirements { get; set; } = new Dictionary<string, float>();
        public List<SpatialZone> AccessZones { get; set; } = new List<SpatialZone>();
        public List<SpatialZone> ExclusionZones { get; set; } = new List<SpatialZone>();

        /// <summary>
        /// Validates if an element satisfies all constraints
        /// </summary>
        public bool ValidateElement(MEPElement element)
        {
            // Check for clashes with fixed elements
            foreach (var fixedElement in FixedElements)
            {
                if (element.ClashesWith(fixedElement))
                {
                    return false;
                }
            }

            // Check for clashes with building elements
            foreach (var buildingElement in BuildingElements)
            {
                if (element.ClashesWith(buildingElement))
                {
                    return false;
                }
            }

            // Check minimum clearances
            if (MinimumClearances.ContainsKey(element.ElementType))
            {
                float requiredClearance = MinimumClearances[element.ElementType];

                foreach (var otherElement in FixedElements.Concat(BuildingElements))
                {
                    float distance = element.Position.DistanceTo(otherElement.Position);
                    if (distance < requiredClearance)
                    {
                        return false;
                    }
                }
            }

            // Check exclusion zones
            foreach (var zone in ExclusionZones)
            {
                if (zone.ContainsElement(element))
                {
                    return false;
                }
            }

            // Further checks for code compliance would be implemented here

            return true;
        }
    }

    /// <summary>
    /// Represents a spatial zone (for access or exclusion areas)
    /// </summary>
    public class SpatialZone
    {
        public Position Center { get; set; }
        public Dimensions Size { get; set; }

        public SpatialZone(Position center, Dimensions size)
        {
            Center = center;
            Size = size;
        }

        public bool ContainsElement(MEPElement element)
        {
            // Check if element is inside this zone
            float halfWidth = Size.Width / 2;
            float halfHeight = Size.Height / 2;
            float halfDepth = Size.Depth / 2;

            bool xInside = Math.Abs(element.Position.X - Center.X) < halfWidth;
            bool yInside = Math.Abs(element.Position.Y - Center.Y) < halfHeight;
            bool zInside = Math.Abs(element.Position.Z - Center.Z) < halfDepth;

            return xInside && yInside && zInside;
        }
    }

    /// <summary>
    /// Training data for the MEP transformer model
    /// </summary>
    public class MEPTrainingData
    {
        public List<MEPElement> InputLayout { get; set; }
        public List<MEPElement> OptimalLayout { get; set; }
        public MEPConstraints Constraints { get; set; }
        public Dictionary<string, float> PerformanceMetrics { get; set; }

        public MEPTrainingData()
        {
            InputLayout = new List<MEPElement>();
            OptimalLayout = new List<MEPElement>();
            Constraints = new MEPConstraints();
            PerformanceMetrics = new Dictionary<string, float>();
        }
    }

    /// <summary>
    /// Positional encoding for transformer model
    /// </summary>
    public class MEPPositionalEncoding
    {
        private readonly int _dimension;

        public MEPPositionalEncoding(int dimension)
        {
            _dimension = dimension;
        }

        public float[][] AddPositionalEncoding(float[][] embeddings, List<MEPElement> elements)
        {
            // Add 3D positional encoding to embeddings
            for (int i = 0; i < embeddings.Length; i++)
            {
                var element = elements[i];

                // Add position-based encoding
                for (int d = 0; d < _dimension; d += 6)
                {
                    if (d + 5 < _dimension)
                    {
                        embeddings[i][d] += (float)Math.Sin(element.Position.X / Math.Pow(10000, (2.0 * d) / _dimension));
                        embeddings[i][d + 1] += (float)Math.Cos(element.Position.X / Math.Pow(10000, (2.0 * d) / _dimension));
                        embeddings[i][d + 2] += (float)Math.Sin(element.Position.Y / Math.Pow(10000, (2.0 * (d + 2)) / _dimension));
                        embeddings[i][d + 3] += (float)Math.Cos(element.Position.Y / Math.Pow(10000, (2.0 * (d + 2)) / _dimension));
                        embeddings[i][d + 4] += (float)Math.Sin(element.Position.Z / Math.Pow(10000, (2.0 * (d + 4)) / _dimension));
                        embeddings[i][d + 5] += (float)Math.Cos(element.Position.Z / Math.Pow(10000, (2.0 * (d + 4)) / _dimension));
                    }
                }
            }

            return embeddings;
        }
    }

    /// <summary>
    /// Transformer encoder layer
    /// </summary>
    public class TransformerEncoderLayer
    {
        private readonly int _embeddingDim;
        private readonly int _numHeads;
        private readonly int _feedForwardDim;
        private readonly float _dropoutRate;

        public TransformerEncoderLayer(int embeddingDim, int numHeads, int feedForwardDim, float dropoutRate)
        {
            _embeddingDim = embeddingDim;
            _numHeads = numHeads;
            _feedForwardDim = feedForwardDim;
            _dropoutRate = dropoutRate;
        }

        public float[][] Forward(float[][] input)
        {
            // Simplified transformer encoder layer
            // In a real implementation, this would include:
            // 1. Multi-head self-attention
            // 2. Add & norm
            // 3. Feed-forward network
            // 4. Add & norm

            // For now, return input with slight modifications to simulate processing
            var output = new float[input.Length][];
            for (int i = 0; i < input.Length; i++)
            {
                output[i] = new float[input[i].Length];
                for (int j = 0; j < input[i].Length; j++)
                {
                    // Apply a simple transformation
                    output[i][j] = input[i][j] * 0.9f + 0.1f;
                }
            }

            return output;
        }
    }

    /// <summary>
    /// Transformer decoder layer
    /// </summary>
    public class TransformerDecoderLayer
    {
        private readonly int _embeddingDim;
        private readonly int _numHeads;
        private readonly int _feedForwardDim;
        private readonly float _dropoutRate;

        public TransformerDecoderLayer(int embeddingDim, int numHeads, int feedForwardDim, float dropoutRate)
        {
            _embeddingDim = embeddingDim;
            _numHeads = numHeads;
            _feedForwardDim = feedForwardDim;
            _dropoutRate = dropoutRate;
        }

        public float[][] Forward(float[][] decoderInput, float[][] encoderOutput)
        {
            // Simplified transformer decoder layer
            // In a real implementation, this would include:
            // 1. Masked multi-head self-attention
            // 2. Add & norm
            // 3. Multi-head cross-attention with encoder output
            // 4. Add & norm
            // 5. Feed-forward network
            // 6. Add & norm

            // For now, combine decoder input and encoder output
            var output = new float[decoderInput.Length][];
            for (int i = 0; i < decoderInput.Length; i++)
            {
                output[i] = new float[decoderInput[i].Length];
                for (int j = 0; j < decoderInput[i].Length; j++)
                {
                    // Simple combination of decoder input and encoder output
                    float decoderValue = decoderInput[i][j];
                    float encoderValue = i < encoderOutput.Length && j < encoderOutput[i].Length ? encoderOutput[i][j] : 0f;
                    output[i][j] = (decoderValue + encoderValue) * 0.5f;
                }
            }

            return output;
        }
    }

    /// <summary>
    /// Result of MEP optimization analysis
    /// </summary>
    public class MEPOptimizationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; }
        public int ElementCount { get; set; }
        public List<OptimizationIssue> Issues { get; set; } = new List<OptimizationIssue>();
        public List<OptimizationRecommendation> Recommendations { get; set; } = new List<OptimizationRecommendation>();
        public bool HasErrors { get; set; }
        public double OverallEnergyScore { get; set; }
        public double OverallComplianceScore { get; set; }
        public double OptimizationPotential { get; set; }
    }

    /// <summary>
    /// Optimization issue detected by the transformer
    /// </summary>
    public class OptimizationIssue
    {
        public OptimizationIssueType Type { get; set; }
        public ElementId ElementId { get; set; }
        public OptimizationSeverity Severity { get; set; }
        public string Description { get; set; }
        public XYZ Location { get; set; }
        public float Confidence { get; set; }
    }

    /// <summary>
    /// Optimization recommendation from the transformer
    /// </summary>
    public class OptimizationRecommendation
    {
        public OptimizationRecommendationType Type { get; set; }
        public ElementId ElementId { get; set; }
        public OptimizationPriority Priority { get; set; }
        public string Description { get; set; }
        public float ExpectedImprovement { get; set; }
        public OptimizationCost ImplementationCost { get; set; }
    }

    /// <summary>
    /// Types of optimization issues
    /// </summary>
    public enum OptimizationIssueType
    {
        Clash,
        CodeViolation,
        EnergyInefficiency,
        SystemFailure
    }

    /// <summary>
    /// Severity levels for optimization issues
    /// </summary>
    public enum OptimizationSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Types of optimization recommendations
    /// </summary>
    public enum OptimizationRecommendationType
    {
        EnergyOptimization,
        SystemOptimization,
        CodeCompliance,
        MaintenanceImprovement
    }

    /// <summary>
    /// Priority levels for optimization recommendations
    /// </summary>
    public enum OptimizationPriority
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Cost levels for implementing optimizations
    /// </summary>
    public enum OptimizationCost
    {
        Low,
        Medium,
        High
    }

    /// <summary>
    /// 3D position for MEP elements
    /// </summary>
    public class MEPPosition3D
    {
        public float X { get; set; }
        public float Y { get; set; }
        public float Z { get; set; }

        public MEPPosition3D(float x, float y, float z)
        {
            X = x;
            Y = y;
            Z = z;
        }
    }

    /// <summary>
    /// Dimensions for MEP elements
    /// </summary>
    public class MEPDimensions
    {
        public float Width { get; set; }
        public float Height { get; set; }
        public float Depth { get; set; }

        public MEPDimensions(float width, float height, float depth)
        {
            Width = width;
            Height = height;
            Depth = depth;
        }
    }

    /// <summary>
    /// MEP system types
    /// </summary>
    public enum MEPSystemType
    {
        Mechanical,
        Electrical,
        Plumbing,
        FireProtection
    }

    /// <summary>
    /// MEP element data for transformer processing
    /// </summary>
    public class MEPElementData
    {
        public Element Element { get; set; }
        public MEPSystemType SystemType { get; set; }
        public MEPPosition3D Position { get; set; }
        public MEPDimensions Dimensions { get; set; }
        public Dictionary<string, object> Properties { get; set; } = new Dictionary<string, object>();
        public double FlowRate { get; set; }
        public double Pressure { get; set; }
        public double Temperature { get; set; }
        public string MaterialType { get; set; }
        public double EnergyConsumption { get; set; }

        public MEPElementData(Element element)
        {
            Element = element ?? throw new ArgumentNullException(nameof(element));

            // Extract basic properties from Revit element
            var location = element.Location;
            if (location is LocationPoint locationPoint)
            {
                var point = locationPoint.Point;
                Position = new MEPPosition3D((float)point.X, (float)point.Y, (float)point.Z);
            }
            else if (location is LocationCurve locationCurve)
            {
                var startPoint = locationCurve.Curve.GetEndPoint(0);
                Position = new MEPPosition3D((float)startPoint.X, (float)startPoint.Y, (float)startPoint.Z);
            }
            else
            {
                Position = new MEPPosition3D(0, 0, 0);
            }

            // Get bounding box for dimensions
            var bbox = element.get_BoundingBox(null);
            if (bbox != null)
            {
                var size = bbox.Max - bbox.Min;
                Dimensions = new MEPDimensions((float)size.X, (float)size.Y, (float)size.Z);
            }
            else
            {
                Dimensions = new MEPDimensions(1, 1, 1);
            }

            // Determine system type based on element category
            SystemType = DetermineSystemType(element);

            // Extract additional properties
            ExtractElementProperties(element);
        }

        private MEPSystemType DetermineSystemType(Element element)
        {
            var category = element.Category;
            if (category == null) return MEPSystemType.Mechanical;

            var categoryName = category.Name.ToLower();

            if (categoryName.Contains("duct") || categoryName.Contains("air"))
                return MEPSystemType.Mechanical;
            else if (categoryName.Contains("pipe") || categoryName.Contains("plumb"))
                return MEPSystemType.Plumbing;
            else if (categoryName.Contains("electrical") || categoryName.Contains("conduit") || categoryName.Contains("cable"))
                return MEPSystemType.Electrical;
            else if (categoryName.Contains("fire") || categoryName.Contains("sprinkler"))
                return MEPSystemType.FireProtection;

            return MEPSystemType.Mechanical;
        }

        private void ExtractElementProperties(Element element)
        {
            // Extract flow rate
            var flowParam = element.get_Parameter(BuiltInParameter.RBS_PIPE_FLOW_PARAM);
            if (flowParam != null && flowParam.HasValue)
                FlowRate = flowParam.AsDouble();

            // Extract pressure
            var pressureParam = element.get_Parameter(BuiltInParameter.RBS_PIPE_STATIC_PRESSURE);
            if (pressureParam != null && pressureParam.HasValue)
                Pressure = pressureParam.AsDouble();

            // Extract temperature (using a valid parameter)
            var tempParam = element.LookupParameter("Temperature");
            if (tempParam != null && tempParam.HasValue)
                Temperature = tempParam.AsDouble();

            // Extract material
            var materialParam = element.get_Parameter(BuiltInParameter.STRUCTURAL_MATERIAL_PARAM);
            if (materialParam != null && materialParam.HasValue)
            {
                var materialId = materialParam.AsElementId();
                if (materialId != ElementId.InvalidElementId)
                {
                    var material = element.Document.GetElement(materialId);
                    MaterialType = material?.Name ?? "Unknown";
                }
            }

            // Store additional properties
            Properties["ElementId"] = element.Id;
            Properties["Category"] = element.Category?.Name ?? "Unknown";
            Properties["FamilyName"] = element.get_Parameter(BuiltInParameter.ELEM_FAMILY_PARAM)?.AsValueString() ?? "Unknown";
            Properties["TypeName"] = element.get_Parameter(BuiltInParameter.ELEM_TYPE_PARAM)?.AsValueString() ?? "Unknown";
        }
    }
}
