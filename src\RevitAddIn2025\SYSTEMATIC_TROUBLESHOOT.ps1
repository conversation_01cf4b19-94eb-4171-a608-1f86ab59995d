# SYSTEMATIC TROUBLESHOOT - RevitAddIn2025
# Resolves "Assembly with same name is already loaded" error

$ErrorActionPreference = "Stop"

Write-Host ""
Write-Host "SYSTEMATIC TROUBLESHOOT - RevitAddIn2025" -ForegroundColor Magenta
Write-Host "=========================================" -ForegroundColor Magenta
Write-Host "Resolving: Assembly with same name is already loaded" -ForegroundColor Yellow

# Configuration
$revitAddinsFolder = "$env:APPDATA\Autodesk\Revit\Addins\2025"
$revitProgramDataFolder = "$env:PROGRAMDATA\Autodesk\Revit\Addins\2025"
$scriptDir = $PSScriptRoot
$projectRoot = Split-Path -Parent $scriptDir
$buildOutput = Join-Path $projectRoot "bin\Release"

Write-Host ""
Write-Host "STEP 1: COMPREHENSIVE CLEANUP" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

# Find ALL instances of RevitAddIn2025 files
$searchLocations = @(
    $revitAddinsFolder,
    $revitProgramDataFolder,
    "$env:APPDATA\Autodesk\Revit\Addins",
    "$env:PROGRAMDATA\Autodesk\Revit\Addins"
)

$foundFiles = @()
foreach ($location in $searchLocations) {
    if (Test-Path $location) {
        Write-Host "Searching: $location" -ForegroundColor Gray
        
        # Find .addin files
        $addinFiles = Get-ChildItem -Path $location -Recurse -Filter "*.addin" -ErrorAction SilentlyContinue | 
                     Where-Object { $_.Name -like "*RevitAddIn2025*" -or (Get-Content $_.FullName -ErrorAction SilentlyContinue) -match "RevitAddIn2025" }
        
        # Find DLL files
        $dllFiles = Get-ChildItem -Path $location -Recurse -Filter "RevitAddIn2025.dll" -ErrorAction SilentlyContinue
        
        $foundFiles += $addinFiles + $dllFiles
    }
}

Write-Host ""
Write-Host "FOUND FILES TO CLEAN:" -ForegroundColor Yellow
if ($foundFiles.Count -eq 0) {
    Write-Host "No existing files found" -ForegroundColor Green
} else {
    foreach ($file in $foundFiles) {
        Write-Host "  $($file.FullName)" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "REMOVING ALL EXISTING FILES..." -ForegroundColor Yellow
    foreach ($file in $foundFiles) {
        try {
            Remove-Item -Path $file.FullName -Force
            Write-Host "  REMOVED: $($file.Name)" -ForegroundColor Green
        } catch {
            Write-Host "  FAILED TO REMOVE: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "STEP 2: VERIFY BUILD OUTPUT" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan

$dllPath = Join-Path $buildOutput "RevitAddIn2025.dll"
if (Test-Path $dllPath) {
    $dllInfo = Get-Item $dllPath
    $sizeKB = [math]::Round($dllInfo.Length / 1024, 2)
    Write-Host "SUCCESS: DLL found - Size: $sizeKB KB" -ForegroundColor Green
    Write-Host "  Path: $dllPath" -ForegroundColor Gray
    Write-Host "  Modified: $($dllInfo.LastWriteTime)" -ForegroundColor Gray
} else {
    Write-Host "ERROR: DLL not found at $dllPath" -ForegroundColor Red
    Write-Host "Building project..." -ForegroundColor Yellow
    
    $projectFile = Join-Path $scriptDir "RevitAddIn2025.csproj"
    & dotnet build $projectFile -c Release -p:Platform=x64 --verbosity minimal
    
    if (Test-Path $dllPath) {
        Write-Host "SUCCESS: Build completed" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Build failed" -ForegroundColor Red
        exit 1
    }
}

Write-Host ""
Write-Host "STEP 3: CREATE UNIQUE DEPLOYMENT" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Create unique folder name to avoid conflicts
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$uniqueFolderName = "RevitAddIn2025_$timestamp"
$deploymentFolder = Join-Path $revitAddinsFolder $uniqueFolderName
$addinFileName = "$uniqueFolderName.addin"
$addinFilePath = Join-Path $revitAddinsFolder $addinFileName

Write-Host "Creating unique deployment folder: $uniqueFolderName" -ForegroundColor Yellow

# Create deployment directory
New-Item -ItemType Directory -Path $deploymentFolder -Force | Out-Null
Write-Host "SUCCESS: Created deployment directory" -ForegroundColor Green

# Copy DLL and dependencies
Copy-Item -Path $dllPath -Destination $deploymentFolder -Force
Write-Host "SUCCESS: Copied main DLL" -ForegroundColor Green

$pdbPath = Join-Path $buildOutput "RevitAddIn2025.pdb"
if (Test-Path $pdbPath) {
    Copy-Item -Path $pdbPath -Destination $deploymentFolder -Force
    Write-Host "SUCCESS: Copied PDB file" -ForegroundColor Green
}

Write-Host ""
Write-Host "STEP 4: CREATE UNIQUE .ADDIN FILE" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

$absoluteDllPath = Join-Path $deploymentFolder "RevitAddIn2025.dll"
$uniqueGuid = [System.Guid]::NewGuid().ToString().ToUpper()

$addinContent = @"
<?xml version="1.0" encoding="utf-8"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>$uniqueFolderName</Name>
    <Assembly>$absoluteDllPath</Assembly>
    <AddInId>$uniqueGuid</AddInId>
    <FullClassName>RevitAddIn2025.App.RevitApplication</FullClassName>
    <VendorId>REVIT2025_$timestamp</VendorId>
    <VendorDescription>Revit 2025 Add-in with Apple-inspired UI (Unique Instance)</VendorDescription>
  </AddIn>
</RevitAddIns>
"@

Set-Content -Path $addinFilePath -Value $addinContent -Encoding UTF8
Write-Host "SUCCESS: Created unique .addin file" -ForegroundColor Green
Write-Host "  File: $addinFileName" -ForegroundColor Gray
Write-Host "  GUID: $uniqueGuid" -ForegroundColor Gray
Write-Host "  Assembly: $absoluteDllPath" -ForegroundColor Gray

Write-Host ""
Write-Host "STEP 5: VERIFICATION" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan

# Verify deployment
if (Test-Path $absoluteDllPath) {
    Write-Host "SUCCESS: Deployed DLL verified" -ForegroundColor Green
} else {
    Write-Host "ERROR: Deployed DLL not found!" -ForegroundColor Red
    exit 1
}

if (Test-Path $addinFilePath) {
    Write-Host "SUCCESS: .addin file verified" -ForegroundColor Green
} else {
    Write-Host "ERROR: .addin file not found!" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "STEP 6: ASSEMBLY VERIFICATION" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

try {
    # Load assembly to check for issues
    $assembly = [System.Reflection.Assembly]::LoadFrom($absoluteDllPath)
    Write-Host "SUCCESS: Assembly loads correctly" -ForegroundColor Green
    Write-Host "  Full Name: $($assembly.FullName)" -ForegroundColor Gray
    Write-Host "  Location: $($assembly.Location)" -ForegroundColor Gray
    
    # Check for the main class
    $mainClass = $assembly.GetType("RevitAddIn2025.App.RevitApplication")
    if ($mainClass) {
        Write-Host "SUCCESS: Main application class found" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Main application class not found" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: Assembly verification failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "DEPLOYMENT COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host ""
Write-Host "UNIQUE DEPLOYMENT DETAILS:" -ForegroundColor Cyan
Write-Host "Folder: $uniqueFolderName" -ForegroundColor White
Write-Host "DLL: $absoluteDllPath" -ForegroundColor White
Write-Host ".addin: $addinFilePath" -ForegroundColor White
Write-Host "GUID: $uniqueGuid" -ForegroundColor White
Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Close Revit completely" -ForegroundColor White
Write-Host "2. Wait 10 seconds" -ForegroundColor White
Write-Host "3. Start Revit 2025" -ForegroundColor White
Write-Host "4. Look for startup dialogs" -ForegroundColor White
Write-Host "5. Check for ribbon tab" -ForegroundColor White
