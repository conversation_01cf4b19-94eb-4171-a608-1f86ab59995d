# RevitAddIn2025 Keyboard Shortcuts Guide

This document provides a reference for all keyboard shortcuts available in the RevitAddIn2025 add-in.

## Global Shortcuts
These shortcuts work throughout the entire application, regardless of which window is active:

| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl+Shift+D` | Toggle Theme | Switch between light and dark themes instantly |

## Dashboard Window Shortcuts
These shortcuts only work when the Dashboard window is in focus:

| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl+R` | Refresh Data | Refresh all dashboard data, bypassing cache |
| `Esc` | Close | Close the dashboard window |

## Settings Window Shortcuts
These shortcuts only work when the Settings window is in focus:

| Shortcut | Action | Description |
|----------|--------|-------------|
| `Ctrl+S` | Save Settings | Save current settings |
| `Esc` | Cancel | Close the settings window without saving |

## Report Generation Shortcuts
These shortcuts work in report-related dialogs:

| Shortcut | Action | Description |
|----------|--------|-------------|
| `Alt+G` | Generate | Generate the selected report |
| `Alt+C` | Cancel | Cancel report generation |

## Benefits of Using Keyboard Shortcuts

Using keyboard shortcuts can significantly improve your workflow efficiency when working with the RevitAddIn2025:

1. **Speed**: Execute common commands instantly without moving your hands from the keyboard
2. **Efficiency**: Reduce the time needed to navigate menus and click buttons
3. **Productivity**: Perform complex actions with minimal effort
4. **Accessibility**: Provide alternative means of interaction for users with different needs

## Customizing Shortcuts (Future Feature)

In a future update, we plan to add support for customizable keyboard shortcuts. This will allow you to:

- Modify existing shortcuts to match your preferences
- Create new shortcuts for frequently used actions
- Import/export shortcut configurations
- Create shortcut profiles for different workflows

Stay tuned for updates on this feature!
