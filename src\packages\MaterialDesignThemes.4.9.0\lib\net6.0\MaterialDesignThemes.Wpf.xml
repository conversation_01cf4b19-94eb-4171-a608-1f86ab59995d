<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MaterialDesignThemes.Wpf</name>
    </assembly>
    <members>
        <member name="F:MaterialDesignThemes.Wpf.ButtonAssist.CornerRadiusProperty">
            <summary>
            Controls the corner radius of the surrounding box.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.CalendarFormatInfo">
            <summary>
            Provides culture-specific information about the format of calendar.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.CalendarFormatInfo.YearMonthPattern">
            <summary>
            Gets the custom format string for a year and month value.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.CalendarFormatInfo.ComponentOnePattern">
            <summary>
            Gets the custom format string for a component one value.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.CalendarFormatInfo.ComponentTwoPattern">
            <summary>
            Gets the custom format string for a component two value.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.CalendarFormatInfo.ComponentThreePattern">
            <summary>
            Gets the custom format string for a component three value.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.SetYearPattern(System.String[],System.String)">
            <summary>
            Sets the culture-specific custom format string for a year value. 
            </summary>
            <param name="cultureNames">An array of string that specify the name of culture to set the <paramref name="yearPattern"/> for.</param>
            <param name="yearPattern">The custom format string for a year value. If null, culture-specific custom format string for a year value is removed.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="cultureNames"/> is null.</exception>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.SetYearPattern(System.String,System.String)">
            <summary>
            Sets the culture-specific custom format string for a year value.
            </summary>
            <param name="cultureName">A string that specify the name of culture to set the <paramref name="yearPattern"/> for.</param>
            <param name="yearPattern">The custom format string for a year value. If null, culture-specific custom format string for a year value is removed.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="cultureName"/> is null.</exception>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.SetDayOfWeekStyle(System.String[],MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle)">
            <summary>
            Sets the culture-specific day of week style.
            </summary>
            <param name="cultureNames">An array of string that specify the name of culture to set the <paramref name="dayOfWeekStyle"/> for.</param>
            <param name="dayOfWeekStyle">A <see cref="T:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle"/> to be set.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="cultureNames"/> is null.</exception>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.SetDayOfWeekStyle(System.String,MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle)">
            <summary>
            Sets the culture-specific day of week style.
            </summary>
            <param name="cultureName">A string that specify the name of culture to set the <paramref name="dayOfWeekStyle"/> for.</param>
            <param name="dayOfWeekStyle">A <see cref="T:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle"/> to be set.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="cultureName"/> is null.</exception>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.ResetDayOfWeekStyle(System.String[])">
            <summary>
            Resets the culture-specific day of week style to default value.
            </summary>
            <param name="cultureNames">An array of string that specify the name of culture to reset.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="cultureNames"/> is null.</exception>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.ResetDayOfWeekStyle(System.String)">
            <summary>
            Resets the culture-specific day of week style to default value.
            </summary>
            <param name="cultureName">A string that specify the name of culture to reset.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="cultureName"/> is null.</exception>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.FromCultureInfo(System.Globalization.CultureInfo)">
            <summary>
            Creates a <see cref="T:MaterialDesignThemes.Wpf.CalendarFormatInfo"/> from the <see cref="T:System.Globalization.CultureInfo"/>.
            </summary>
            <param name="cultureInfo">A <see cref="T:System.Globalization.CultureInfo"/> that specifies the culture to get the date format.</param>
            <returns>The <see cref="T:MaterialDesignThemes.Wpf.CalendarFormatInfo"/> object that this method creates.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="cultureInfo"/> is null.</exception>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle">
            <summary>
            Represents a day of week style.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle.Pattern">
            <summary>
            Gets the custom format string for a day of week value.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle.Separator">
            <summary>
            Gets the string that separates MonthDay and DayOfWeek.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle.IsFirst">
            <summary>
            Gets a value indicating whether DayOfWeek is before MonthDay.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle.#ctor(System.String,System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle"/> struct.
            </summary>
            <param name="pattern">A custom format string for a day of week value.</param>
            <param name="separator">A string that separates MonthDay and DayOfWeek.</param>
            <param name="isFirst">A value indicating whether DayOfWeek is before MonthDay.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle.Parse(System.String)">
            <summary>
            Extracts the <see cref="T:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle"/> from the date format string.
            </summary>
            <param name="s">the date format string.</param>
            <returns>The <see cref="T:MaterialDesignThemes.Wpf.CalendarFormatInfo.DayOfWeekStyle"/> struct.</returns>
            <exception cref="T:System.ArgumentNullException"><paramref name="s"/> is null.</exception>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Chip.IsDeletable">
            <summary>
            Indicates if the delete button should be visible.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ClockItemButton.OnClick">
            <summary> 
            This override method is called when the control is clicked by mouse or keyboard
            </summary> 
        </member>
        <member name="T:MaterialDesignThemes.Wpf.ColorZone">
            <summary>
            User a colour zone to easily switch the background and foreground colours, from selected Material Design palette or custom ones.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ComboBoxAssist.ClassicModeProperty">
            <summary>
            By default ComboBox uses the wrapper popup. Popup can be switched to classic Windows desktop view by means of this attached property.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ComboBoxAssist.ShowSelectedItemProperty">
            <summary>
            By default the selected item is displayed in the drop down list, as per Material Design specifications.
            To change this to a behavior of hiding the selected item from the drop down list, set this attached property to false.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ComboBoxAssist.MaxLengthProperty">
            <summary>
            Gets or sets the maximum number of characters that can be manually entered into the text box. <br />
            <remarks>
            <see cref="P:System.Windows.Controls.TextBox.MaxLength"/> cannot be set for an editable ComboBox. That's why this attached property exists.
            </remarks>
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Converters.CalendarDateCoalesceConverter">
            <summary>
            Help us format the content of a header button in a calendar.
            </summary>
            <remarks>
            Expected items, in the following order:
                1) DateTime Calendar.DisplayDate
                2) DateTime? Calendar.SelectedDate
            </remarks>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.Converters.CircularProgressBar.ArcEndPointConverter.ParameterMidPoint">
            <summary>
            CircularProgressBar draws two arcs to support a full circle at 100 %.
            With one arc at 100 % the start point is identical the end point, so nothing is drawn.
            Midpoint at half of current percentage is the endpoint of the first arc
            and the start point of the second arc.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Converters.CursorConverter">
            <summary>
            Value converter that uses the Cursor from the bound property if set, otherwise it returns the <see cref="P:MaterialDesignThemes.Wpf.Converters.CursorConverter.FallbackCursor"/>.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Converters.HintProxyFabricConverter">
            <summary>
            Converter for <see cref="T:MaterialDesignThemes.Wpf.SmartHint"/> control. Can be extended by <see cref="M:MaterialDesignThemes.Wpf.HintProxyFabric.RegisterBuilder(System.Func{System.Windows.Controls.Control,System.Boolean},System.Func{System.Windows.Controls.Control,MaterialDesignThemes.Wpf.IHintProxy})"/> method.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Converters.ListViewGridViewConverter">
            <summary>
            Helps coerce the correct item container style for a <see cref="T:System.Windows.Controls.ListView"/>, according to whether the list is displaying in standard mode, or using a <see cref="P:System.Windows.Controls.ListView.View"/>, such as a <see cref="T:System.Windows.Controls.GridView"/>.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Converters.ListViewGridViewConverter.DefaultValue">
            <summary>
            Item container style to use when <see cref="P:System.Windows.Controls.ListView.View"/> is <c>null</c>.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Converters.ListViewGridViewConverter.ViewValue">
            <summary>
            Item container style to use when <see cref="P:System.Windows.Controls.ListView.View"/> is not <c>null</c>, typically when a <see cref="T:System.Windows.Controls.GridView"/> is applied.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Converters.ListViewGridViewConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Returns the item container <see cref="T:System.Windows.Style"/> to use for a <see cref="T:System.Windows.Controls.ListView"/>.
            </summary>
            <param name="value">Should be a <see cref="T:System.Windows.Controls.ListView"/> or <see cref="T:System.Windows.Controls.ViewBase"/> instance.</param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Converters.PickerInnerPaddingConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Sets the left padding for the inner picker button to zero
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Converters.PickerInnerPaddingConverter.Convert(System.Object[],System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Adds the width of the inner picker button to the right of inner padding 
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Converters.CardClipConverter.Convert(System.Object[],System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            1 - Content presenter render size,
            2 - Clipping border padding (main control padding)
            </summary>
            <param name="values"></param>
            <param name="targetType"></param>
            <param name="parameter"></param>
            <param name="culture"></param>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DataGridAssist.AllowDirectEditWithoutFocus(System.Object,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Allows editing of components inside of a data grid cell with a single left click.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DataGridTextColumn.MaxLength">
            <summary>
            Set the maximum length for the text field.
            </summary>
            <remarks>Not a dependency property, as is only applied once.</remarks>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogClosedEventArgs.Parameter">
            <summary>
            Gets the parameter originally provided to <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/>/
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogClosedEventArgs.Session">
            <summary>
            Allows interaction with the current dialog session.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogClosingEventArgs.Cancel">
            <summary>
            Cancel the close.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogClosingEventArgs.IsCancelled">
            <summary>
            Indicates if the close has already been cancelled.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogClosingEventArgs.Parameter">
            <summary>
            Gets the parameter originally provided to <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/>/
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogClosingEventArgs.Session">
            <summary>
            Allows interaction with the current dialog session.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.DialogHostOpenDialogCommandDataContextSource">
            <summary>
            Defines how a data context is sourced for a dialog if a <see cref="T:System.Windows.FrameworkElement"/>
            is passed as the command parameter when using <see cref="F:MaterialDesignThemes.Wpf.DialogHost.OpenDialogCommand"/>.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.DialogHostOpenDialogCommandDataContextSource.SenderElement">
            <summary>
            The data context from the sender element (typically a <see cref="T:System.Windows.Controls.Button"/>) 
            is applied to the content.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.DialogHostOpenDialogCommandDataContextSource.DialogHostInstance">
            <summary>
            The data context from the <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is applied to the content.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.DialogHostOpenDialogCommandDataContextSource.None">
            <summary>
            The data context is explicitly set to <c>null</c>.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.DialogHost.OpenDialogCommand">
            <summary>
            Routed command to be used somewhere inside an instance to trigger showing of the dialog. Content can be passed to the dialog via a <see cref="!:Button.CommandParameter"/>.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand">
            <summary>
            Routed command to be used inside dialog content to close a dialog. Use a <see cref="!:Button.CommandParameter"/> to indicate the result of the parameter.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>        
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>        
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,MaterialDesignThemes.Wpf.DialogClosingEventHandler)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler,MaterialDesignThemes.Wpf.DialogClosingEventHandler)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>        
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler,MaterialDesignThemes.Wpf.DialogClosingEventHandler,MaterialDesignThemes.Wpf.DialogClosedEventHandler)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>        
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <param name="closedEventHandler">Allows access to closed event which would otherwise have been subscribed to on a instance.</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,System.Object)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="dialogIdentifier"><see cref="P:MaterialDesignThemes.Wpf.DialogHost.Identifier"/> of the instance where the dialog should be shown. Typically this will match an identifier set in XAML. <c>null</c> is allowed.</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="dialogIdentifier"><see cref="P:MaterialDesignThemes.Wpf.DialogHost.Identifier"/> of the instance where the dialog should be shown. Typically this will match an identifier set in XAML. <c>null</c> is allowed.</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,System.Object,MaterialDesignThemes.Wpf.DialogClosingEventHandler)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="dialogIdentifier"><see cref="P:MaterialDesignThemes.Wpf.DialogHost.Identifier"/> of the instance where the dialog should be shown. Typically this will match an identifier set in XAML. <c>null</c> is allowed.</param>        
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler,MaterialDesignThemes.Wpf.DialogClosingEventHandler)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="dialogIdentifier"><see cref="P:MaterialDesignThemes.Wpf.DialogHost.Identifier"/> of the instance where the dialog should be shown. Typically this will match an identifier set in XAML. <c>null</c> is allowed.</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler,MaterialDesignThemes.Wpf.DialogClosingEventHandler,MaterialDesignThemes.Wpf.DialogClosedEventHandler)">
            <summary>
            Shows a modal dialog. To use, a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> instance must be in a visual tree (typically this may be specified towards the root of a Window's XAML).
            </summary>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="dialogIdentifier"><see cref="P:MaterialDesignThemes.Wpf.DialogHost.Identifier"/> of the instance where the dialog should be shown. Typically this will match an identifier set in XAML. <c>null</c> is allowed.</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <param name="closedEventHandler">Allows access to closed event which would otherwise have been subscribed to on a instance.</param>
            <returns>Task result is the parameter used to close the dialog, typically what is passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand"/> command.</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Close(System.Object)">
            <summary>
             Close a modal dialog.
            </summary>
            <param name="dialogIdentifier"> of the instance where the dialog should be closed. Typically this will match an identifier set in XAML. </param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.Close(System.Object,System.Object)">
            <summary>
             Close a modal dialog.
            </summary>
            <param name="dialogIdentifier"> of the instance where the dialog should be closed. Typically this will match an identifier set in XAML. </param>
            <param name="parameter"> to provide to close handler</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.GetDialogSession(System.Object)">
            <summary>
            Retrieve the current dialog session for a DialogHost
            </summary>
            <param name="dialogIdentifier">The identifier to use to retrieve the DialogHost</param>
            <returns>The DialogSession if one is in process, or null</returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.IsDialogOpen(System.Object)">
            <summary>
            dialog instance exists
            </summary>
            <param name="dialogIdentifier">of the instance where the dialog should be closed. Typically this will match an identifier set in XAML.</param>
            <returns></returns>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.Identifier">
            <summary>
            Identifier which is used in conjunction with <see cref="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object)"/> to determine where a dialog should be shown.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.CurrentSession">
            <summary>
            Returns a DialogSession for the currently open dialog for managing it programmatically. If no dialog is open, CurrentSession will return null
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.OpenDialogCommandDataContextSource">
            <summary>
            Defines how a data context is sourced for a dialog if a <see cref="T:System.Windows.FrameworkElement"/>
            is passed as the command parameter when using <see cref="F:MaterialDesignThemes.Wpf.DialogHost.OpenDialogCommand"/>.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.CloseOnClickAway">
            <summary>
            Indicates whether the dialog will close if the user clicks off the dialog, on the obscured background.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.CloseOnClickAwayParameter">
            <summary>
            Parameter to provide to close handlers if an close due to click away is instigated.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.SnackbarMessageQueue">
            <summary>
            Allows association of a snackbar, so that notifications can be paused whilst a dialog is being displayed.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.DialogTheme">
            <summary>
            Set the theme (light/dark) for the dialog.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.OverlayBackground">
            <summary>
            Represents the overlay brush that is used to dim the background behind the dialog
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.DialogBackground">
            <summary>
            Represents the brush for the Dialog's background
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.DialogHost.DialogOpened">
            <summary>
            Raised when a dialog is opened.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.DialogHost.DialogOpenedAttachedProperty">
            <summary>
            Attached property which can be used on the <see cref="T:System.Windows.Controls.Button"/> which instigated the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.OpenDialogCommand"/> to process the event.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.DialogOpenedCallback">
            <summary>
            Callback fired when the <see cref="E:MaterialDesignThemes.Wpf.DialogHost.DialogOpened"/> event is fired, allowing the event to be processed from a binding/view model.
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.DialogHost.DialogClosing">
            <summary>
            Raised just before a dialog is closed.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.DialogHost.DialogClosingAttachedProperty">
            <summary>
            Attached property which can be used on the <see cref="T:System.Windows.Controls.Button"/> which instigated the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.OpenDialogCommand"/> to process the closing event.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.DialogClosingCallback">
            <summary>
            Callback fired when the <see cref="E:MaterialDesignThemes.Wpf.DialogHost.DialogClosing"/> event is fired, allowing the event to be processed from a binding/view model.
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.DialogHost.DialogClosed">
            <summary>
            Raised when a dialog is closed.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.DialogHost.DialogClosedAttachedProperty">
            <summary>
            Attached property which can be used on the <see cref="T:System.Windows.Controls.Button"/> which instigated the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.OpenDialogCommand"/> to process the closed event.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogHost.DialogClosedCallback">
            <summary>
            Callback fired when the <see cref="E:MaterialDesignThemes.Wpf.DialogHost.DialogClosed"/> event is fired, allowing the event to be processed from a binding/view model.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHost.FocusPopup">
            <summary>
            Attempts to focus the content of a popup.
            </summary>
            <returns>The popup content.</returns>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.DialogHostEx">
            <summary>
            Helper extensions for showing dialogs.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.Window,System.Object)">
            <summary>
            Shows a dialog using the first found <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> in a given <see cref="T:System.Windows.Window"/>.
            </summary>
            <param name="window">Window on which the modal dialog should be displayed. Must contain a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>.</param>
            <param name="content"></param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <remarks>
            As a depth first traversal of the window's visual tree is performed, it is not safe to use this method in a situation where a screen has multiple <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>s.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.Window,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler)">
            <summary>
            Shows a dialog using the first found <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> in a given <see cref="T:System.Windows.Window"/>.
            </summary>
            <param name="window">Window on which the modal dialog should be displayed. Must contain a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>.</param>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <remarks>
            As a depth first traversal of the window's visual tree is performed, it is not safe to use this method in a situation where a screen has multiple <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>s.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.Window,System.Object,MaterialDesignThemes.Wpf.DialogClosingEventHandler)">
            <summary>
            Shows a dialog using the first found <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> in a given <see cref="T:System.Windows.Window"/>.
            </summary>
            <param name="window">Window on which the modal dialog should be displayed. Must contain a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>.</param>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <remarks>
            As a depth first traversal of the window's visual tree is performed, it is not safe to use this method in a situation where a screen has multiple <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>s.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.Window,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler,MaterialDesignThemes.Wpf.DialogClosingEventHandler)">
            <summary>
            Shows a dialog using the first found <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> in a given <see cref="T:System.Windows.Window"/>.
            </summary>
            <param name="window">Window on which the modal dialog should be displayed. Must contain a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>.</param>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <remarks>
            As a depth first traversal of the window's visual tree is performed, it is not safe to use this method in a situation where a screen has multiple <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>s.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.Window,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler,MaterialDesignThemes.Wpf.DialogClosingEventHandler,MaterialDesignThemes.Wpf.DialogClosedEventHandler)">
            <summary>
            Shows a dialog using the first found <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> in a given <see cref="T:System.Windows.Window"/>.
            </summary>
            <param name="window">Window on which the modal dialog should be displayed. Must contain a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>.</param>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <param name="closedEventHandler">Allows access to closed event which would otherwise have been subscribed to on a instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <remarks>
            As a depth first traversal of the window's visual tree is performed, it is not safe to use this method in a situation where a screen has multiple <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>s.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.DependencyObject,System.Object)">
            <summary>
            Shows a dialog using the parent/ancestor <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> of the a given <see cref="T:System.Windows.DependencyObject"/>.
            </summary>
            <param name="childDependencyObject">Dependency object which should be a visual child of a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>, where the dialog will be shown.</param>        
            <param name="content">Content to show (can be a control or view model).</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.DependencyObject,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler)">
            <summary>
            Shows a dialog using the parent/ancestor <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> of the a given <see cref="T:System.Windows.DependencyObject"/>.
            </summary>
            <param name="childDependencyObject">Dependency object which should be a visual child of a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>, where the dialog will be shown.</param>        
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.DependencyObject,System.Object,MaterialDesignThemes.Wpf.DialogClosingEventHandler)">
            <summary>
            Shows a dialog using the parent/ancestor <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> of the a given <see cref="T:System.Windows.DependencyObject"/>.
            </summary>
            <param name="childDependencyObject">Dependency object which should be a visual child of a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>, where the dialog will be shown.</param>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.DependencyObject,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler,MaterialDesignThemes.Wpf.DialogClosingEventHandler)">
            <summary>
            Shows a dialog using the parent/ancestor <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> of the a given <see cref="T:System.Windows.DependencyObject"/>.
            </summary>
            <param name="childDependencyObject">Dependency object which should be a visual child of a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>, where the dialog will be shown.</param>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogHostEx.ShowDialog(System.Windows.DependencyObject,System.Object,MaterialDesignThemes.Wpf.DialogOpenedEventHandler,MaterialDesignThemes.Wpf.DialogClosingEventHandler,MaterialDesignThemes.Wpf.DialogClosedEventHandler)">
            <summary>
            Shows a dialog using the parent/ancestor <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> of the a given <see cref="T:System.Windows.DependencyObject"/>.
            </summary>
            <param name="childDependencyObject">Dependency object which should be a visual child of a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/>, where the dialog will be shown.</param>
            <param name="content">Content to show (can be a control or view model).</param>
            <param name="openedEventHandler">Allows access to opened event which would otherwise have been subscribed to on a instance.</param>
            <param name="closingEventHandler">Allows access to closing event which would otherwise have been subscribed to on a instance.</param>
            <param name="closedEventHandler">Allows access to closed event which would otherwise have been subscribed to on a instance.</param>
            <exception cref="T:System.InvalidOperationException">
            Thrown is a <see cref="T:MaterialDesignThemes.Wpf.DialogHost"/> is not found when conducting a depth first traversal of visual tree.  
            </exception>
            <returns></returns>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogOpenedEventArgs.Session">
            <summary>
            Allows interaction with the current dialog session.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.DialogSession">
            <summary>
            Allows an open dialog to be managed. Use is only permitted during a single display operation.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogSession.IsEnded">
            <summary>
            Indicates if the dialog session has ended.  Once ended no further method calls will be permitted.
            </summary>
            <remarks>
            Client code cannot set this directly, this is internally managed.  To end the dialog session use <see cref="M:MaterialDesignThemes.Wpf.DialogSession.Close"/>.
            </remarks>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogSession.CloseParameter">
            <summary>
            The parameter passed to the <see cref="F:MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand" /> and return by <see cref="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object)"/>
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DialogSession.Content">
            <summary>
            Gets the <see cref="P:MaterialDesignThemes.Wpf.DialogHost.DialogContent"/> which is currently displayed, so this could be a view model or a UI element.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogSession.UpdateContent(System.Object)">
            <summary>
            Update the current content in the dialog.
            </summary>
            <param name="content"></param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogSession.Close">
            <summary>
            Closes the dialog.
            </summary>
            <exception cref="T:System.InvalidOperationException">Thrown if the dialog session has ended, or a close operation is currently in progress.</exception>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DialogSession.Close(System.Object)">
            <summary>
            Closes the dialog.
            </summary>
            <param name="parameter">Result parameter which will be returned in <see cref="P:MaterialDesignThemes.Wpf.DialogClosingEventArgs.Parameter"/> or from <see cref="M:MaterialDesignThemes.Wpf.DialogHost.Show(System.Object)"/> method.</param>
            <exception cref="T:System.InvalidOperationException">Thrown if the dialog session has ended, or a close operation is currently in progress.</exception>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.DrawerClosingEventArgs.Cancel">
            <summary>
            Cancel the close.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DrawerClosingEventArgs.IsCancelled">
            <summary>
            Indicates if the close has already been cancelled.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DrawerClosingEventArgs.Dock">
            <summary>
            Allows interaction with the current dialog session.
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.DrawerHost.DrawerOpened">
            <summary>
            Raised when a drawer is opened.
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.DrawerHost.DrawerClosing">
            <summary>
            Raised when a drawer is closing.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.DrawerOpenedEventArgs.Dock">
            <summary>
            Allows interaction with the current dialog session.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Extensions.GetVisualAncestry(System.Windows.DependencyObject)">
            <summary>
            Returns full visual ancestry, starting at the leaf.
            <para>If element is not of <see cref="T:System.Windows.Media.Visual"/> or <see cref="T:System.Windows.Media.Media3D.Visual3D"/> the
            logical ancestry is used.</para>
            </summary>
            <param name="leaf"></param>
            <returns></returns>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.FlipperAssist.UniformCornerRadiusProperty">
            <summary>
            Controls the (uniform) corner radius of the contained card
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.FlipperAssist.CardStyleProperty">
            <summary>
            Controls the style of the contained card
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.IHintProxy">
            <summary>
            This interface is the adapter from UiControl (like <see cref="T:System.Windows.Controls.TextBox"/>, <see cref="T:System.Windows.Controls.ComboBox"/> and others) to <see cref="T:MaterialDesignThemes.Wpf.SmartHint"/>
            <para/>
            You should implement this interface in order to use SmartHint for your own control.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.IHintProxy.IsEmpty">
            <summary>
            Checks to see if the targeted control can be deemed as logically 
            empty, even if not null, affecting the current hint display.
            </summary>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.IHintProxy.IsFocused">
            <summary>
            Targeted control has keyboard focus
            </summary>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ISnackbarMessageQueue.Enqueue(System.Object)">
            <summary>
            Queues a notification message for display in a snackbar.
            </summary>
            <param name="content">Message.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ISnackbarMessageQueue.Enqueue(System.Object,System.Object,System.Action)">
            <summary>
            Queues a notification message for display in a snackbar.
            </summary>
            <param name="content">Message.</param>
            <param name="actionContent">Content for the action button.</param>
            <param name="actionHandler">Call back to be executed if user clicks the action button.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ISnackbarMessageQueue.Enqueue``1(System.Object,System.Object,System.Action{``0},``0)">
            <summary>
            Queues a notification message for display in a snackbar.
            </summary>
            <param name="content">Message.</param>
            <param name="actionContent">Content for the action button.</param>
            <param name="actionHandler">Call back to be executed if user clicks the action button.</param>
            <param name="actionArgument">Argument to pass to <paramref name="actionHandler"/>.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ISnackbarMessageQueue.Enqueue(System.Object,System.Boolean)">
            <summary>
            Queues a notification message for display in a snackbar.
            </summary>
            <param name="content">Message.</param>
            <param name="neverConsiderToBeDuplicate">Subsequent, duplicate messages queued within a short time span will 
            be discarded. To override this behaviour and ensure the message always gets displayed set to <c>true</c>.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ISnackbarMessageQueue.Enqueue(System.Object,System.Object,System.Action,System.Boolean)">
            <summary>
            Queues a notification message for display in a snackbar.
            </summary>
            <param name="content">Message.</param>
            <param name="actionContent">Content for the action button.</param>
            <param name="actionHandler">Call back to be executed if user clicks the action button.</param>
            <param name="promote">The message will promoted to the front of the queue.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ISnackbarMessageQueue.Enqueue``1(System.Object,System.Object,System.Action{``0},``0,System.Boolean)">
            <summary>
            Queues a notification message for display in a snackbar.
            </summary>
            <param name="content">Message.</param>
            <param name="actionContent">Content for the action button.</param>
            <param name="actionHandler">Call back to be executed if user clicks the action button.</param>
            <param name="actionArgument">Argument to pass to <paramref name="actionHandler"/>.</param>
            <param name="promote">The message will be promoted to the front of the queue and never considered to be a duplicate.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ISnackbarMessageQueue.Enqueue``1(System.Object,System.Object,System.Action{``0},``0,System.Boolean,System.Boolean,System.Nullable{System.TimeSpan})">
            <summary>
            Queues a notification message for display in a snackbar.
            </summary>
            <param name="content">Message.</param>
            <param name="actionContent">Content for the action button.</param>
            <param name="actionHandler">Call back to be executed if user clicks the action button.</param>
            <param name="actionArgument">Argument to pass to <paramref name="actionHandler"/>.</param>
            <param name="promote">The message will be promoted to the front of the queue.</param>
            <param name="neverConsiderToBeDuplicate">The message will never be considered a duplicate.</param>
            <param name="durationOverride">Message show duration override.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ISnackbarMessageQueue.Enqueue(System.Object,System.Object,System.Action{System.Object},System.Object,System.Boolean,System.Boolean,System.Nullable{System.TimeSpan})">
            <summary>
            Queues a notification message for display in a snackbar.
            </summary>
            <param name="content">Message.</param>
            <param name="actionContent">Content for the action button.</param>
            <param name="actionHandler">Call back to be executed if user clicks the action button.</param>
            <param name="actionArgument">Argument to pass to <paramref name="actionHandler"/>.</param>
            <param name="promote">The message will promoted to the front of the queue.</param>
            <param name="neverConsiderToBeDuplicate">The message will never be considered a duplicate.</param>
            <param name="durationOverride">Message show duration override.</param>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ListBoxItemAssist.CornerRadiusProperty">
            <summary>
            Controls the corner radius of the selection box.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.MessageQueueExtension">
            <summary>
            Provides shorthand to initialise a new <see cref="T:MaterialDesignThemes.Wpf.SnackbarMessageQueue"/> for a <see cref="T:MaterialDesignThemes.Wpf.Snackbar"/>.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.NavigationBarAssist.CornerRadiusProperty">
            <summary>
            Controls the corner radius of the selection box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.NavigationDrawerAssist.CornerRadiusProperty">
            <summary>
            Controls the corner radius of the selection box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.NavigationRailAssist.CornerRadiusProperty">
            <summary>
            Controls the corner radius of the selection box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.NavigationRailAssist.FloatingContentProperty">
            <summary>
            Floating Content (ex: Button) on navigation rail (optional)
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PackIcon.Kind">
            <summary>
            Gets or sets the icon to display.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PackIcon.Data">
            <summary>
            Gets the icon path data for the current <see cref="P:MaterialDesignThemes.Wpf.PackIcon.Kind"/>.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.PackIconDataFactory">
            ******************************************
            This code is auto generated. Do not amend.
            ******************************************
        </member>
        <member name="T:MaterialDesignThemes.Wpf.PackIconKind">
            ******************************************
            This code is auto generated. Do not amend.
            ******************************************
            <summary>
            List of available icons for use with <see cref="T:MaterialDesignThemes.Wpf.PackIcon" />.
            </summary>
            <remarks>
            All icons sourced from Material Design Icons Font - https://materialdesignicons.com/ - in accordance of 
            https://github.com/Templarian/MaterialDesign/blob/master/LICENSE.
            </remarks>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Palette">
            <summary>
            Provides full information about a palette.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.PasswordBoxAssist.HandlePasswordChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Handles changes to the 'Password' attached property.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.PasswordBoxAssist.PasswordBoxPasswordChanged(System.Object,System.Windows.RoutedEventArgs)">
            <summary>
            Handle the 'PasswordChanged'-event on the PasswordBox
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Plane3D">
            <summary>
            View a control on a 3D plane.    
            </summary>
            <remarks>
            Taken from http://blogs.msdn.com/greg_schechter/archive/2007/10/26/enter-the-planerator-dead-simple-3d-in-wpf-with-a-stupid-name.aspx , Greg Schechter - Fall 2007
            </remarks>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Plane3D.LayoutInvalidationCatcher">
            <summary>
            Wrap this around a class that we want to catch the measure and arrange 
            processes occurring on, and propagate to the parent Planerator, if any.
            Do this because layout invalidations don't flow up out of a 
            Viewport2DVisual3D object.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.PopupBoxPlacementMode">
            <summary>
            Defines how the <see cref="T:MaterialDesignThemes.Wpf.PopupBox"/> popup is aligned to the toggle part of the control.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.BottomAndAlignLeftEdges">
            <summary>
            Display the popup below the toggle, and align the left edges.3
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.BottomAndAlignRightEdges">
            <summary>
            Display the popup below the toggle, and align the right edges.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.BottomAndAlignCentres">
            <summary>
            Display the popup below the toggle, and align the center of the popup with the center of the toggle.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.TopAndAlignLeftEdges">
            <summary>
            Display the popup above the toggle, and align the left edges.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.TopAndAlignRightEdges">
            <summary>
            Display the popup above the toggle, and align the right edges.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.TopAndAlignCentres">
            <summary>
            Display the popup above the toggle, and align the center of the popup with the center of the toggle.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.LeftAndAlignTopEdges">
            <summary>
            Display the popup to the left of the toggle, and align the top edges.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.LeftAndAlignBottomEdges">
            <summary>
            Display the popup to the left of the toggle, and align the bottom edges.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.LeftAndAlignMiddles">
            <summary>
            Display the popup to the left of the toggle, and align the middles.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.RightAndAlignTopEdges">
            <summary>
            Display the popup to the right of the toggle, and align the top edges.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.RightAndAlignBottomEdges">
            <summary>
            Display the popup to the right of the toggle, and align the bottom edges.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPlacementMode.RightAndAlignMiddles">
            <summary>
            Display the popup to the right of the toggle, and align the middles.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.PopupBoxPopupMode">
            <summary>
            Defines what causes the <see cref="T:MaterialDesignThemes.Wpf.PopupBox"/> to open it's popup.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPopupMode.Click">
            <summary>
            Open when the toggle button is clicked.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPopupMode.MouseOver">
            <summary>
            Open when the mouse goes over the toggle button.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBoxPopupMode.MouseOverEager">
            <summary>
            Open when the mouse goes over the toggle button, or the space in which the popup box would occupy should it be open.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.PopupBox">
            <summary>
            Popup box, similar to a <see cref="T:System.Windows.Controls.ComboBox"/>, but allows more customizable content.    
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBox.ClosePopupCommand">
            <summary>
            Routed command to be used inside of a popup content to close it.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.ToggleContent">
            <summary>
            Content to display in the toggle button.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.ToggleContentTemplate">
            <summary>
            Template for <see cref="P:MaterialDesignThemes.Wpf.PopupBox.ToggleContent"/>.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContent">
            <summary>
            Content to display in the toggle when it's checked (when the popup is open). Optional; if not provided the <see cref="P:MaterialDesignThemes.Wpf.PopupBox.ToggleContent"/> is used.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContentTemplate">
            <summary>
            Template for <see cref="P:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContent"/>.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContentCommand">
            <summary>
            Command to execute if toggle is checked (popup is open) and <see cref="P:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContent"/> is set.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContentCommandParameter">
            <summary>
            Command parameter to use in conjunction with <see cref="P:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContentCommand"/>.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PopupContent">
            <summary>
            Content to display in the content.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PopupContentTemplate">
            <summary>
            Popup content template.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.IsPopupOpen">
            <summary>
            Gets or sets whether the popup is currently open.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.StaysOpen">
            <summary>
            Indicates of the popup should stay open if a click occurs inside the popup.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PlacementMode">
            <summary>
            Gets or sets how the popup is aligned in relation to the toggle.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PopupMode">
            <summary>
            Gets or sets what trigger causes the popup to open.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBox.UnfurlOrientationProperty">
            <summary>
            Get or sets how to unfurl controls when opening the popups. Only child elements of type <see cref="T:System.Windows.Controls.Primitives.ButtonBase"/> are animated.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.UnfurlOrientation">
            <summary>
            Gets or sets how to unfurl controls when opening the popups. Only child elements of type <see cref="T:System.Windows.Controls.Primitives.ButtonBase"/> are animated.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBox.PopupHorizontalOffsetProperty">
            <summary>
            Get or sets the popup horizontal offset in relation to the button.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PopupHorizontalOffset">
            <summary>
            Get or sets the popup horizontal offset in relation to the button.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBox.PopupVerticalOffsetProperty">
            <summary>
            Get or sets the popup vertical offset in relation to the button.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PopupVerticalOffset">
            <summary>
            Get or sets the popup vertical offset in relation to the button.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBox.PopupUniformCornerRadiusProperty">
            <summary>
            Get or sets the corner radius of the popup card.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PopupUniformCornerRadius">
            <summary>
            Get or sets the corner radius of the popup card.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBox.PopupElevationProperty">
            <summary>
            Gets or sets the elevation of the popup card.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PopupElevation">
            <summary>
            Gets or sets the elevation of the popup card.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupBox.PopupPlacementMethod">
            <summary>
            Framework use. Provides the method used to position the popup.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContentClickEvent">
            <summary>
            Event raised when the checked toggled content (if set) is clicked.
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContentClick">
            <summary>
            Event raised when the checked toggled content (if set) is clicked.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.PopupBox.OnToggleCheckedContentClick">
            <summary>
            Raises <see cref="F:MaterialDesignThemes.Wpf.PopupBox.ToggleCheckedContentClickEvent"/>.
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.PopupBox.Opened">
            <summary>
            Raised when the popup is opened.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.PopupBox.OnOpened">
            <summary>
            Raises <see cref="F:MaterialDesignThemes.Wpf.PopupBox.OpenedEvent"/>.
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.PopupBox.Closed">
            <summary>
            Raised when the popup is closed.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.PopupBox.OnClosed">
            <summary>
            Raises <see cref="F:MaterialDesignThemes.Wpf.PopupBox.ClosedEvent"/>.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.PopupEx">
            <summary>
            This class was initially based on work done in ControlzEx
            https://github.com/ControlzEx/ControlzEx
            
            This custom popup can be used by validation error templates or something else.
            It provides some additional nice features:
                - repositioning if host-window size or location changed
                - repositioning if host-window gets maximized and vice versa
                - it's only topmost if the host-window is activated
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.PopupEx.CloseOnMouseLeftButtonDown">
            <summary>
            Gets/sets if the popup can be closed by left mouse button down.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.PopupEx.RefreshPosition">
            <summary>
            Causes the popup to update it's position according to it's current settings.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.PopupEx.SWP">
            <summary>
            SetWindowPos options
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.RatingBar">
            <summary>
            A custom control implementing a rating bar.
            The icon aka content may be set as a DataTemplate via the ButtonContentTemplate property.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.RatingBar.ValueIncrements">
            <summary>
            Gets or sets the value increments. Set to a value between 0.0 and 1.0 (both exclusive) to enable fractional values. Default value is 1.0 (i.e. fractional values disabled)
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.Ripple.RecognizesAccessKeyProperty">
            <summary>
              The DependencyProperty for the RecognizesAccessKey property. 
              Default Value: false 
            </summary> 
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Ripple.RecognizesAccessKey">
            <summary> 
              Determine if Ripple should use AccessText in its style
            </summary> 
        </member>
        <member name="F:MaterialDesignThemes.Wpf.RippleAssist.IsCenteredProperty">
            <summary>
            Set to <c>true</c> to cause the ripple to originate from the centre of the 
            content.  Otherwise the effect will originate from the mouse down position.        
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.RippleAssist.SetIsCentered(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Set to <c>true</c> to cause the ripple to originate from the centre of the 
            content.  Otherwise the effect will originate from the mouse down position.        
            </summary>
            <param name="element"></param>
            <param name="value"></param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.RippleAssist.GetIsCentered(System.Windows.DependencyObject)">
            <summary>
            Set to <c>true</c> to cause the ripple to originate from the centre of the 
            content.  Otherwise the effect will originate from the mouse down position.        
            </summary>
            <param name="element"></param>        
        </member>
        <member name="F:MaterialDesignThemes.Wpf.RippleAssist.IsDisabledProperty">
            <summary>
            Set to <c>True</c> to disable ripple effect
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.RippleAssist.SetIsDisabled(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Set to <c>True</c> to disable ripple effect
            </summary>
            <param name="element"></param>
            <param name="value"></param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.RippleAssist.GetIsDisabled(System.Windows.DependencyObject)">
            <summary>
            Set to <c>True</c> to disable ripple effect
            </summary>
            <param name="element"></param>        
        </member>
        <member name="T:MaterialDesignThemes.Wpf.ScaleHost">
            <summary>
            Internal use only.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Screen">
            <summary>
             Represents a display device or multiple display devices on a single system.
             Based on http://referencesource.microsoft.com/#System.Windows.Forms/winforms/Managed/System/WinForms/Screen.cs
             </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.Screen._workingArea">
            <summary>
            Available working area on the screen. This excludes taskbars and other
            docked windows.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Screen.AllScreens">
            <summary>
            Gets an array of all of the displays on the system.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Screen.Bounds">
            <summary>
            Gets the bounds of the display.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Screen.DeviceName">
            <summary>
            Gets the device name associated with a display.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Screen.Primary">
            <summary>
            Gets a value indicating whether a particular display is the primary device.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Screen.PrimaryScreen">
            <summary>
            Gets the primary display.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Screen.WorkingArea">
            <summary>
            Gets the working area of the screen.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Screen.DesktopChangedCount">
            <summary>
            Screen instances call this property to determine
            if their WorkingArea cache needs to be invalidated.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.Equals(System.Object)">
            <summary>
            Specifies a value that indicates whether the specified object is equal to this one.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.FromPoint(System.Windows.Point)">
            <summary>
            Retrieves a <see cref='T:MaterialDesignThemes.Wpf.Screen'/> for the monitor that contains the specified point.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.FromRect(System.Windows.Rect)">
            <summary>
            Retrieves a <see cref='T:MaterialDesignThemes.Wpf.Screen'/> for the monitor that contains the largest region of the Rect.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.GetWorkingArea(System.Windows.Point)">
            <summary>
             Retrieves the working area for the monitor that is closest to the specified point.
             </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.GetWorkingArea(System.Windows.Rect)">
            <summary>
             Retrieves the working area for the monitor that contains the largest region of the specified Rect.
             </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.GetBounds(System.Windows.Point)">
            <summary>
             Retrieves the bounds of the monitor that is closest to the specified point.
             </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.GetBounds(System.Windows.Rect)">
            <summary>
            Retrieves the bounds of the monitor that contains the largest region of the specified Rect.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.GetHashCode">
            <summary>
            Computes and retrieves a hash code for an object.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.OnDisplaySettingsChanging(System.Object,System.EventArgs)">
            <summary>
            Called by the SystemEvents class when our display settings are
            changing.  We cache screen information and at this point we must
            invalidate our cache.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.OnUserPreferenceChanged(System.Object,Microsoft.Win32.UserPreferenceChangedEventArgs)">
            <summary>
            Called by the SystemEvents class when our display settings have
            changed.  Here, we increment a static counter that Screen instances
            can check against to invalidate their cache.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Screen.ToString">
            <summary>
            Retrieves a string representing this object.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.SmartHint">
            <summary>
            A control that implement placeholder behavior. Can work as a simple placeholder either as a floating hint, see <see cref="P:MaterialDesignThemes.Wpf.SmartHint.UseFloating"/> property.
            <para/>
            To set a target control you should set the HintProxy property. Use the <see cref="P:MaterialDesignThemes.Wpf.Converters.HintProxyFabricConverter.Instance"/> converter which converts a control into the IHintProxy interface.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Snackbar">
            <summary>
            Implements a <see cref="T:MaterialDesignThemes.Wpf.Snackbar"/> inspired by the Material Design specs (https://material.google.com/components/snackbars-toasts.html).
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.SnackbarMessage">
            <summary>
            Defines the content of a message within a <see cref="T:MaterialDesignThemes.Wpf.Snackbar"/>.  Primary content should be set via the 
            standard <see cref="!:SnackbarMessage.Content"/> property.  Where an action is allowed, content
            can be provided in <see cref="P:MaterialDesignThemes.Wpf.SnackbarMessage.ActionContent"/>.  Standard button properties are 
            provided for actions, including <see cref="P:MaterialDesignThemes.Wpf.SnackbarMessage.ActionCommand"/>.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.SnackbarMessage.ActionClickEvent">
            <summary>
            Event correspond to left mouse button click on the Action button.
            </summary>
        </member>
        <member name="E:MaterialDesignThemes.Wpf.SnackbarMessage.ActionClick">
            <summary>
            Add / Remove ActionClickEvent handler 
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.SnackbarMessage.InlineActionButtonMaxHeightProperty">
            <summary>
            Maximum total height of snackbar for the action button to be inlined.
            <para>
            Default value (<c>55</c>) is between single line message (<c>48</c>) and two lined snackbar-message (<c>66</c>)
            because tolerance is required (see <a href="https://github.com/MaterialDesignInXAML/MaterialDesignInXamlToolkit/issues/1812">issue</a>)
            </para>
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.SnackbarMessageQueue._closeSnackbarEvent">
            <summary>
            If set, the active snackbar will be closed.
            </summary>
            <remarks>
            Available only while the snackbar is displayed.
            Should be locked by <see cref="F:MaterialDesignThemes.Wpf.SnackbarMessageQueue._snackbarMessagesLock"/>.
            </remarks>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueue.Dispatcher">
            <summary>
            Gets the <see cref="T:System.Windows.Threading.Dispatcher"/> this <see cref="T:MaterialDesignThemes.Wpf.SnackbarMessageQueue"/> is associated with.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueue.DiscardDuplicates">
            <summary>
            Gets or sets a value that indicates whether this message queue displays messages without discarding duplicates. 
            False to show every message even if there are duplicates.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.SnackbarMessageQueue.Clear">
            <summary>
            Clear the message queue and close the active snackbar.
            This method can be called from any thread.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueueItem.Content">
            <summary>
            The content to be displayed
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueueItem.Duration">
            <summary>
            Message show duration.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueueItem.ActionContent">
            <summary>
            The content for the action button on the snackbar
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueueItem.ActionHandler">
            <summary>
            Handler to be invoked when the action button is clicked
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueueItem.ActionArgument">
            <summary>
            The argument to pass to the <see cref="P:MaterialDesignThemes.Wpf.SnackbarMessageQueueItem.ActionHandler"/> delegate.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueueItem.IsPromoted">
            <summary>
            Promote the message, pushing it in front of any message that is not promoted.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.SnackbarMessageQueueItem.AlwaysShow">
            <summary>
            Always show this message, even if it's a duplicate
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextBlockAssist.AutoToolTipProperty">
            <summary>
            Automatic ToolTip for TextBlock or TextBoxBase if containing text is trimmed
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.TextFieldAssist">
            <summary>
            Helper properties for working with text fields.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.TextBoxViewMarginProperty">
            <summary>
            The text box view margin property
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TextFieldAssist.SetTextBoxViewMargin(System.Windows.DependencyObject,System.Windows.Thickness)">
            <summary>
            Sets the text box view margin.
            </summary>
            <param name="element">The element.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TextFieldAssist.GetTextBoxViewMargin(System.Windows.DependencyObject)">
            <summary>
            Gets the text box view margin.
            </summary>
            <param name="element">The element.</param>
            <returns>
            The <see cref="T:System.Windows.Thickness" />.
            </returns>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.DecorationVisibilityProperty">
            <summary>
            Controls the visibility of the underline decoration.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TextFieldAssist.SetDecorationVisibility(System.Windows.DependencyObject,System.Windows.Visibility)">
            <summary>
            Controls the visibility of the underline decoration.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TextFieldAssist.GetDecorationVisibility(System.Windows.DependencyObject)">
            <summary>
            Controls the visibility of the underline decoration.
            </summary>
            <param name="element"></param>
            <returns></returns>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.UnderlineBrushProperty">
            <summary>
            The attached WPF property for getting or setting the <see cref="T:System.Windows.Media.Brush"/> value for an underline decoration.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TextFieldAssist.SetUnderlineBrush(System.Windows.DependencyObject,System.Windows.Media.Brush)">
            <summary>
            Sets the <see cref="T:System.Windows.Media.Brush"/> used for underline decoration.
            </summary>
            <param name="element"></param>
            <param name="value"></param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TextFieldAssist.GetUnderlineBrush(System.Windows.DependencyObject)">
            <summary>
            Gets the <see cref="T:System.Windows.Media.Brush"/> used for underline decoration.
            </summary>
            <param name="element"></param>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.HasFilledTextFieldProperty">
            <summary>
            Controls the visibility of the text field box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.HasOutlinedTextFieldProperty">
            <summary>
            Controls the visibility of the text field area box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.TextFieldCornerRadiusProperty">
            <summary>
            Controls the corner radius of the surrounding box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.UnderlineCornerRadiusProperty">
            <summary>
            Controls the corner radius of the bottom line of the surrounding box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.NewSpecHighlightingEnabledProperty">
            <summary>
            Controls the highlighting style of a text box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.RippleOnFocusEnabledProperty">
            <summary>
            Enables a ripple effect on focusing the text box.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.IncludeSpellingSuggestionsProperty">
            <summary>
            Automatically inserts spelling suggestions into the text box context menu.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.SuffixTextProperty">
            <summary>
            SuffixText dependency property
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.PrefixTextProperty">
            <summary>
            PrefixText dependency property
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.HasClearButtonProperty">
            <summary>
            Controls the visibility of the clear button.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.HasLeadingIconProperty">
            <summary>
            Controls visibility of the leading icon
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.LeadingIconProperty">
            <summary>
            Controls the leading icon
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.LeadingIconSizeProperty">
            <summary>
            Controls the size of the leading icon
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.HasTrailingIconProperty">
            <summary>
            Controls visibility of the trailing icon
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.TrailingIconProperty">
            <summary>
            Controls the trailing icon
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TextFieldAssist.TrailingIconSizeProperty">
            <summary>
            Controls the size of the trailing icon
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TextFieldAssist.ApplyTextBoxViewMargin(System.Windows.Controls.Control,System.Windows.Thickness)">
            <summary>
            Applies the text box view margin.
            </summary>
            <param name="textBox">The text box.</param>
            <param name="margin">The margin.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TextFieldAssist.TextBoxViewMarginPropertyChangedCallback(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            The text box view margin property changed callback.
            </summary>
            <param name="dependencyObject">The dependency object.</param>
            <param name="dependencyPropertyChangedEventArgs">The dependency property changed event args.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.Theme.GetSystemTheme">
            <summary>
            Get the current Windows theme.
            Based on ControlzEx
            https://github.com/ControlzEx/ControlzEx/blob/48230bb023c588e1b7eb86ea83f7ddf7d25be735/src/ControlzEx/Theming/WindowsThemeHelper.cs#L19
            </summary>
            <returns></returns>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TimePicker.OnGotFocus(System.Object,System.Windows.RoutedEventArgs)">
            <summary>
                Called when this element gets focus.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TimePicker.OnIsDropDownOpenChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary> 
            IsDropDownOpenProperty property changed handler.
            </summary> 
            <param name="d">DatePicker that changed its IsDropDownOpen.</param> 
            <param name="e">DependencyPropertyChangedEventArgs.</param>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.TimePicker.IsInvalidTextAllowed">
            <summary>
            Set to true to stop invalid text reverting back to previous valid value. Useful in cases where you
            want to display validation messages and allow the user to correct the data without it reverting.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.TimePicker.WithSeconds">
            <summary>
            Set to true to display seconds in the time and allow the user to select seconds.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ToggleButtonAssist.GetHasOnContent(System.Windows.DependencyObject)">
            <summary>
            Framework use only.
            </summary>
            <param name="element"></param>
            <returns></returns>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ToggleButtonAssist.OnContentProperty">
            <summary>
            Allows on (IsChecked) content to be provided on supporting <see cref="T:System.Windows.Controls.Primitives.ToggleButton"/> styles.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ToggleButtonAssist.SetOnContent(System.Windows.DependencyObject,System.Object)">
            <summary>
            Allows on (IsChecked) content to be provided on supporting <see cref="T:System.Windows.Controls.Primitives.ToggleButton"/> styles.
            </summary>
            <param name="element"></param>
            <param name="value"></param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ToggleButtonAssist.GetOnContent(System.Windows.DependencyObject)">
            <summary>
            Allows on (IsChecked) content to be provided on supporting <see cref="T:System.Windows.Controls.Primitives.ToggleButton"/> styles.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ToggleButtonAssist.OnContentTemplateProperty">
            <summary>
            Allows an on (IsChecked) template to be provided on supporting <see cref="T:System.Windows.Controls.Primitives.ToggleButton"/> styles.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ToggleButtonAssist.SetOnContentTemplate(System.Windows.DependencyObject,System.Windows.DataTemplate)">
            <summary>
            Allows an on (IsChecked) template to be provided on supporting <see cref="T:System.Windows.Controls.Primitives.ToggleButton"/> styles.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ToggleButtonAssist.GetOnContentTemplate(System.Windows.DependencyObject)">
            <summary>
            Allows an on (IsChecked) template to be provided on supporting <see cref="T:System.Windows.Controls.Primitives.ToggleButton"/> styles.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.TransitionAssist">
            <summary>
            Allows transitions to be disabled where supported.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TransitionAssist.DisableTransitionsProperty">
            <summary>
            Allows transitions to be disabled where supported.  Note this is an inheritable property.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TransitionAssist.SetDisableTransitions(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Allows transitions to be disabled where supported.  Note this is an inheritable property.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TransitionAssist.GetDisableTransitions(System.Windows.DependencyObject)">
            <summary>
            Allows transitions to be disabled where supported.  Note this is an inheritable property.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Transitions.FadeWipe.Duration">
            <summary>
            Duration of the animation
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Transitions.IndexedItemOffsetMultiplierExtension">
            <summary>
            Multiplies a time span unit by the index of an item in a list.  
            </summary>
            <remarks>
            Example usage is for a <see cref="T:MaterialDesignThemes.Wpf.Transitions.TransitioningContent"/> to have a <see cref="P:MaterialDesignThemes.Wpf.Transitions.TransitionEffect.OffsetTime" />
            time delayed according to position in a list, so cascading animations can occur.
            </remarks>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Transitions.SlideWipe.Direction">
            <summary>
            Direction of the slide wipe
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Transitions.SlideWipe.Duration">
            <summary>
            Duration of the animation
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Transitions.Transitioner">
            <summary>
            The transitioner provides an easy way to move between content with a default in-place circular transition.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.Transitions.Transitioner.MoveNextCommand">
            <summary>
            Causes the the next slide to be displayed (effectively increments <see cref="P:System.Windows.Controls.Primitives.Selector.SelectedIndex"/>).
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.Transitions.Transitioner.MovePreviousCommand">
            <summary>
            Causes the the previous slide to be displayed (effectively decrements <see cref="P:System.Windows.Controls.Primitives.Selector.SelectedIndex"/>).
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.Transitions.Transitioner.MoveFirstCommand">
            <summary>
            Moves to the first slide.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.Transitions.Transitioner.MoveLastCommand">
            <summary>
            Moves to the last slide.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Transitions.Transitioner.AutoApplyTransitionOrigins">
            <summary>
            If enabled, transition origins will be applied to wipes, according to where a transition was triggered from.  For example, the mouse point where a user clicks a button.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Transitions.TransitionerSlide">
            <summary>
            Content control to host the content of an individual page within a <see cref="T:MaterialDesignThemes.Wpf.Transitions.Transitioner"/>.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Transitions.TransitioningContent">
            <summary>
            Content control to enable easier transitions.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Transitions.TransitioningContentBase.OpeningEffect">
            <summary>
            Gets or sets the transition to run when the content is loaded and made visible.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Transitions.TransitioningContentBase.OpeningEffectsOffset">
            <summary>
            Delay offset to be applied to all opening effect transitions.
            </summary>
        </member>
        <member name="P:MaterialDesignThemes.Wpf.Transitions.TransitioningContentBase.OpeningEffects">
            <summary>
            Allows multiple transition effects to be combined and run upon the content loading or being made visible.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TreeHelper.FindChild``1(System.Windows.DependencyObject,System.String)">
            <summary>
            Finds a Child of a given item in the visual tree. 
            </summary>
            <param name="parent">A direct parent of the queried item.</param>
            <typeparam name="T">The type of the queried item.</typeparam>
            <param name="childName">x:Name or Name of child. </param>
            <returns>The first parent item that matches the submitted type parameter. 
            If not matching item can be found, 
            a null parent is being returned.</returns>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TreeViewAssist.AdditionalTemplateProperty">
            <summary>
            Allows additional rendering for each tree node, outside of the rippled part of the node which responds to user selection.
            </summary>
            <remarks>
            The content to be rendered is the same of the <see cref="T:System.Windows.Controls.TreeViewItem"/>; i.e the Header property, or
            some other content such as a view model, typically when using a <see cref="T:System.Windows.HierarchicalDataTemplate"/>.
            </remarks>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TreeViewAssist.SetAdditionalTemplate(System.Windows.DependencyObject,System.Windows.DataTemplate)">
            <summary>
            Sets the additional template.
            </summary>
            <param name="element">The element.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TreeViewAssist.GetAdditionalTemplate(System.Windows.DependencyObject)">
            <summary>
            Gets the additional template.
            </summary>
            <param name="element">The element.</param>
            <returns>
            The <see cref="T:System.Windows.DataTemplate" />.
            </returns>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TreeViewAssist.AdditionalTemplateSelectorProperty">
            <summary>
            Allows additional rendering for each tree node, outside of the rippled part of the node which responds to user selection.
            </summary>
            <remarks>
            The content to be rendered is the same of the <see cref="T:System.Windows.Controls.TreeViewItem"/>; i.e the Header property, or
            some other content such as a view model, typically when using a <see cref="T:System.Windows.HierarchicalDataTemplate"/>.
            </remarks>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TreeViewAssist.SetAdditionalTemplateSelector(System.Windows.DependencyObject,System.Windows.Controls.DataTemplateSelector)">
            <summary>
            Sets the additional template selector.
            </summary>
            <param name="element">The element.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.TreeViewAssist.GetAdditionalTemplateSelector(System.Windows.DependencyObject)">
            <summary>
            Gets the additional template selector.
            </summary>
            <param name="element">The element.</param>
            <returns>
            The <see cref="T:System.Windows.Controls.DataTemplateSelector" />.
            </returns>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TreeViewAssist.SuppressAdditionalTemplate">
            <summary>
            To be used at <see cref="T:System.Windows.Controls.TreeViewItem"/> level, or to be returned by <see cref="!:AdditionalTemplateSelector"/>
            implementors when the additional template associated with a tree should not be used.
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.TreeViewAssist.HasNoItemsExpanderVisibilityProperty">
            <summary>
            Allows controlling the visibility of the expander arrow on TreeViewItems that do not have any children.
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.UIElementExtensions">
            <summary>
            This is a simple utility to add and remove a single adorner to an element
            since there is no built-in way to do that in xaml.
            <a href="https://docs.microsoft.com/en-us/dotnet/framework/wpf/controls/adorners-overview">See here</a>
            </summary>
        </member>
        <member name="T:MaterialDesignThemes.Wpf.Underline">
            <summary>
            
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ValidationAssist.OnlyShowOnFocusProperty">
            <summary>
            The hint property
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ValidationAssist.UsePopupProperty">
            <summary>
            The hint property
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ValidationAssist.PopupPlacementProperty">
            <summary>
            The hint property
            </summary>
        </member>
        <member name="F:MaterialDesignThemes.Wpf.ValidationAssist.SuppressProperty">
            <summary>
            Framework use only.
            </summary>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ValidationAssist.SetSuppress(System.Windows.DependencyObject,System.Boolean)">
            <summary>
            Framework use only.
            </summary>
            <param name="element"></param>
            <param name="value"></param>
        </member>
        <member name="M:MaterialDesignThemes.Wpf.ValidationAssist.GetSuppress(System.Windows.DependencyObject)">
            <summary>
            Framework use only.
            </summary>
        </member>
    </members>
</doc>
