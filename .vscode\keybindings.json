[
    // ======================================================================
    // ULTIMATE AUTOMATION KEYBINDINGS
    // Zero Manual Work Shortcuts
    // ======================================================================
    // AUTONOMOUS SYSTEM CONTROLS
    {
        "key": "ctrl+alt+shift+a",
        "command": "workbench.action.tasks.runTask",
        "args": "🚀 ULTIMATE ZERO-CLICK AUTONOMOUS SYSTEM",
        "when": "true"
    },
    {
        "key": "ctrl+alt+shift+b",
        "command": "workbench.action.tasks.runTask",
        "args": "🚀 ZERO MANUAL AUTONOMOUS PIPELINE",
        "when": "true"
    },
    // SMART BUILD & DEPLOY
    {
        "key": "f5",
        "command": "workbench.action.tasks.runTask",
        "args": "shell: Fix Addin File",
        "when": "true"
    },
    {
        "key": "ctrl+f5",
        "command": "workbench.action.tasks.build",
        "when": "true"
    },
    // AI-POWERED SHORTCUTS
    {
        "key": "ctrl+shift+i",
        "command": "tabnine.openHub",
        "when": "editorTextFocus"
    },
    {
        "key": "ctrl+alt+i",
        "command": "blackbox.chat",
        "when": "editorTextFocus"
    },
    // PRODUCTIVITY SHORTCUTS
    {
        "key": "ctrl+shift+e",
        "command": "workbench.files.action.showActiveFileInExplorer",
        "when": "true"
    },
    {
        "key": "ctrl+shift+t",
        "command": "workbench.action.terminal.toggleTerminal",
        "when": "true"
    },
    {
        "key": "ctrl+shift+p",
        "command": "workbench.action.showCommands",
        "when": "true"
    },
    // SMART NAVIGATION
    {
        "key": "ctrl+shift+o",
        "command": "workbench.action.gotoSymbol",
        "when": "editorHasDocumentSymbolProvider"
    },
    {
        "key": "ctrl+t",
        "command": "workbench.action.quickOpen",
        "when": "true"
    },
    // ERROR HANDLING
    {
        "key": "f8",
        "command": "editor.action.marker.nextInFiles",
        "when": "true"
    },
    {
        "key": "shift+f8",
        "command": "editor.action.marker.prevInFiles",
        "when": "true"
    },
    // AUTONOMOUS CODE ACTIONS
    {
        "key": "ctrl+.",
        "command": "editor.action.quickFix",
        "when": "editorHasCodeActionsProvider && editorTextFocus && !editorReadonly"
    },
    {
        "key": "alt+shift+f",
        "command": "editor.action.formatDocument",
        "when": "editorHasDocumentFormattingProvider && editorTextFocus && !editorReadonly"
    },
    // POWER USER SHORTCUTS
    {
        "key": "ctrl+k ctrl+s",
        "command": "workbench.action.openGlobalKeybindings",
        "when": "true"
    },
    {
        "key": "ctrl+shift+x",
        "command": "workbench.view.extensions",
        "when": "true"
    },
    // REVIT-SPECIFIC SHORTCUTS
    {
        "key": "ctrl+r ctrl+b",
        "command": "workbench.action.tasks.runTask",
        "args": "Build Revit Add-in",
        "when": "true"
    },
    {
        "key": "ctrl+r ctrl+d",
        "command": "workbench.action.tasks.runTask",
        "args": "Deploy to Revit",
        "when": "true"
    }
]