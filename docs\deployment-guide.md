# RevitAddIn2025 Deployment and Usage Guide

## Deploying the Add-in

To use the RevitAddIn2025 add-in, you must first deploy it correctly to your Revit 2025 installation:

### Method 1: Automated Deployment

1. Build the solution in Visual Studio or Visual Studio Code:
   ```powershell
   cd c:\GITHUB\src
   dotnet build RevitAddIn2025.sln -c Release
   ```

2. Run the deployment script:
   ```powershell
   cd c:\GITHUB\src\RevitAddIn2025
   powershell -ExecutionPolicy Bypass -File .\DeployToRevit.ps1
   ```
   
   > Note: If the deployment script doesn't exist, use Method 2 for manual deployment.

### Method 2: Manual Deployment

1. Build the solution:
   ```powershell
   cd c:\GITHUB\src
   dotnet build RevitAddIn2025.sln -c Release
   ```

2. Copy the compiled files to the Revit Addins folder:
   ```powershell
   # Create Revit 2025 add-ins directory if it doesn't exist
   $addinDir = "$env:APPDATA\Autodesk\Revit\Addins\2025"
   if (!(Test-Path $addinDir)) { New-Item -ItemType Directory -Path $addinDir -Force }
   
   # Copy the add-in manifest file
   Copy-Item "c:\GITHUB\src\RevitAddIn2025\bin\Release\RevitAddIn2025.addin" -Destination $addinDir -Force
   
   # Copy the add-in assembly and dependencies
   $assemblyDir = "$addinDir\RevitAddIn2025"
   if (!(Test-Path $assemblyDir)) { New-Item -ItemType Directory -Path $assemblyDir -Force }
   
   Copy-Item "c:\GITHUB\src\RevitAddIn2025\bin\Release\*" -Destination $assemblyDir -Recurse -Force
   ```

## Accessing the Add-in in Revit 2025

Once deployed, the add-in can be accessed from within Revit 2025:

1. Launch Revit 2025
2. Open any project or create a new one
3. Navigate to the **RevitAddIn2025** tab in the Revit ribbon
   (If you don't see this tab, the add-in may not be properly deployed)
4. The following commands will be available:
   - **Dashboard**: Opens the main dashboard window with project analytics
   - **Settings**: Opens the settings window to configure the add-in
   - **Help**: Displays help information
   - **About**: Displays information about the add-in

## Troubleshooting Deployment Issues

If you don't see the RevitAddIn2025 tab in Revit after following the deployment steps:

1. Verify the add-in files are in the correct location:
   - Check if the `.addin` file is in `%APPDATA%\Autodesk\Revit\Addins\2025`
   - Ensure the DLL files are in the location specified in the `.addin` file

2. Check Revit's journal files for loading errors:
   - Navigate to `%APPDATA%\Autodesk\Revit\Journals`
   - Open the most recent journal file and search for "RevitAddIn2025" or errors

3. Verify that the add-in is not being blocked by Windows:
   - Right-click on the DLL files, select Properties
   - If you see an "Unblock" option, check it and click Apply

4. Restart Revit after deployment:
   - Revit loads add-ins on startup, so a restart is required after deployment

5. Verify the add-in's dependencies are available:
   - Make sure all referenced libraries are in the same folder as the main DLL

## Using the Features

Once the add-in is loaded and you can access the Dashboard:

1. **Dark Theme**: Toggle between light and dark themes using:
   - The theme indicator in the title bar
   - The toggle in Settings
   - The keyboard shortcut Ctrl+Shift+D

2. **Reports**: Generate detailed reports by:
   - Clicking the "Generate Report" button in the dashboard
   - Selecting the report type and category
   
3. **Data Caching**: The dashboard shows if data is being loaded from cache:
   - Check the indicator in the bottom right
   - Use "Refresh Data" button to bypass cache
   - Configure caching in the Settings window

## For Developers

If you need to modify the add-in deployment, you can edit:

1. The `.addin` file to change add-in registration details
2. The `RevitApplication.cs` file to modify ribbon integration
3. The `DeploymentUtils.cs` file if you want to create a custom deployment tool
