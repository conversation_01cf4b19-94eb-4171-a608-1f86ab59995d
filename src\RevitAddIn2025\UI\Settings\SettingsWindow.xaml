<Window x:Class="RevitAddIn2025.UI.Settings.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="RevitAddIn2025 Settings"
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Background="#F2F2F7">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock FontSize="24" FontWeight="SemiBold" Text="RevitAddIn2025 Settings" Margin="0,0,0,8"/>
            <TextBlock FontSize="14" Foreground="Gray" Text="Configure application preferences and options."/>
        </StackPanel>

        <!-- Settings Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- General Settings Section -->
                <Border Background="White" CornerRadius="12" Margin="0,0,0,16" Padding="16">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                    </Border.Effect>
                    <StackPanel>
                        <TextBlock Text="General Settings" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>

                        <!-- Auto-refresh option -->
                        <Grid Margin="0,8,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Auto-refresh Dashboard" FontWeight="Medium"/>
                                <TextBlock Text="Automatically refresh dashboard data when opening" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <CheckBox Grid.Column="1" x:Name="AutoRefreshCheckbox" IsChecked="True" VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Units option -->
                        <Grid Margin="0,8,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Units" FontWeight="Medium"/>
                                <TextBlock Text="Select units for measurements" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <ComboBox Grid.Column="1" x:Name="UnitsComboBox" Width="150">
                                <ComboBoxItem Content="Imperial" IsSelected="True"/>
                                <ComboBoxItem Content="Metric"/>
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Display Settings Section -->
                <Border Background="White" CornerRadius="12" Margin="0,0,0,16" Padding="16">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                    </Border.Effect>
                    <StackPanel>
                        <TextBlock Text="Display Settings" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>

                        <!-- Theme option -->
                        <Grid Margin="0,8,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Dark Theme" FontWeight="Medium"/>
                                <TextBlock Text="Use dark mode for application" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <CheckBox Grid.Column="1" x:Name="ThemeToggle"
                                      Checked="ThemeToggle_CheckedChanged"
                                      Unchecked="ThemeToggle_CheckedChanged"
                                      VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Font size option -->
                        <Grid Margin="0,8,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Font Size" FontWeight="Medium"/>
                                <TextBlock Text="Adjust UI text size" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <ComboBox Grid.Column="1" x:Name="FontSizeComboBox" Width="150">
                                <ComboBoxItem Content="Small"/>
                                <ComboBoxItem Content="Medium" IsSelected="True"/>
                                <ComboBoxItem Content="Large"/>
                            </ComboBox>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Advanced Settings Section -->
                <Border Background="White" CornerRadius="12" Padding="16">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                    </Border.Effect>
                    <StackPanel>
                        <TextBlock Text="Advanced Settings" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>

                        <!-- Data cache option -->
                        <Grid Margin="0,8,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Data Caching" FontWeight="Medium"/>
                                <TextBlock Text="Enable data caching for faster performance" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <CheckBox Grid.Column="1" x:Name="DataCacheCheckbox" IsChecked="True" VerticalAlignment="Center"/>
                        </Grid>

                        <!-- Log level option -->
                        <Grid Margin="0,8,0,8">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Logging Level" FontWeight="Medium"/>
                                <TextBlock Text="Set application logging verbosity" FontSize="12" Foreground="Gray"/>
                            </StackPanel>
                            <ComboBox Grid.Column="1" x:Name="LogLevelComboBox" Width="150">
                                <ComboBoxItem Content="Error"/>
                                <ComboBoxItem Content="Warning"/>
                                <ComboBoxItem Content="Info" IsSelected="True"/>
                                <ComboBoxItem Content="Debug"/>
                                <ComboBoxItem Content="Verbose"/>
                            </ComboBox>
                        </Grid>

                        <!-- System info -->
                        <Border Margin="0,16,0,0" Background="#F5F5F7" Padding="12" CornerRadius="8">
                            <StackPanel>
                                <TextBlock Text="System Information" FontWeight="Medium"/>
                                <TextBlock x:Name="RevitVersionText" Text="Revit Version: 2025" Margin="0,4"/>
                                <TextBlock x:Name="PluginVersionText" Text="Plugin Version: 1.0.0" Margin="0,4"/>
                                <TextBlock x:Name="SystemInfoText" Text="OS: Windows 11" Margin="0,4"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button Grid.Column="0" Content="Reset to Default" Background="Transparent" Foreground="#FF2D55" BorderBrush="#FF2D55" BorderThickness="1"
                    Padding="12,6" Margin="0,0,8,0" Click="ResetToDefault_Click"/>
            <Button Grid.Column="2" Content="Cancel" Background="Transparent" Foreground="#007AFF" BorderBrush="#007AFF" BorderThickness="1"
                    Padding="12,6" Margin="0,0,8,0" Click="Cancel_Click"/>
            <Button Grid.Column="3" Content="Save" Background="#007AFF" Foreground="White" BorderThickness="0"
                    Padding="12,6" Click="Save_Click"/>
        </Grid>
    </Grid>
</Window>
