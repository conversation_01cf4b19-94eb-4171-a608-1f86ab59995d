# RevitAddIn2025 User Guide

This comprehensive guide explains all features and functionality of the RevitAddIn2025 add-in.

## Dashboard

The Dashboard is the main interface providing an overview of your project and quick access to tools.

### Project Information Card

Displays key information from the Revit project:
- Project Name
- Project Number
- Project Address
- Project Status

Click "View Project Details" to see more comprehensive project information.

### Element Summary Card

Shows counts of common element types in the current project:
- Walls
- Doors
- Windows
- Rooms

Click "Analyze Elements" to see a detailed breakdown of all elements.

### Recent Activity Card

Tracks recent actions performed within the add-in:
- Dashboard opened
- Project loaded
- Data refreshed
- Commands executed

Click "View All Activity" to see the complete activity history.

### Tools Card

Provides quick access to frequently used tools:
- **Settings**: Configure add-in preferences
- **Help**: Access help documentation
- **Report**: Generate project reports
- **About**: View add-in information

### Project Statistics Card

Displays key project metrics:
- Total Elements
- File Size
- Last Save time
- Number of Worksets

## Settings

The Settings window allows you to customize the add-in to suit your preferences.

### General Settings

- **Auto-refresh Dashboard**: Enable/disable automatic data refresh when opening the dashboard
- **Units**: Choose between Imperial and Metric measurement units

### Display Settings

- **Theme**: Select Light or Dark theme
- **Font Size**: Choose Small, Medium, or Large text size

### Advanced Settings

- **Data Caching**: Enable/disable caching for improved performance
- **Logging Level**: Set the verbosity of application logging

## Commands

The add-in integrates with Revit's ribbon interface with these commands:

### Dashboard Command

Opens the main dashboard window providing project overview and access to tools.

### Settings Command

Opens the settings window to configure add-in preferences.

### Help Command

Provides access to help documentation and resources.

### About Command

Displays information about the add-in including version and copyright.

## Tips and Tricks

### Performance Optimization

- Enable data caching in settings for faster dashboard loading
- Close the dashboard when not in use to conserve system resources

### Customization

- Switch between light and dark themes to match your preferences and environment
- Adjust the font size for better readability
- Configure logging level based on your needs (Info for normal use, Debug for troubleshooting)
- Enable or disable data caching for performance optimization

### Theme Management

The add-in supports comprehensive theme management with multiple ways to toggle between light and dark modes:

1. **Settings Toggle**: Use the "Dark Theme" toggle in the Settings window
2. **Theme Indicator**: Click the small circle indicator in the top-left corner of any window
3. **Keyboard Shortcut**: Press `Ctrl+Shift+D` from anywhere in the application

### Performance Optimization

The add-in includes smart caching for better performance:

1. **Data Caching**: Project analytics are cached for 5 minutes for faster access
2. **Cache Status**: The dashboard displays whether data is from cache or freshly analyzed
3. **Cache Controls**: Enable or disable caching in the Settings window
4. **Manual Refresh**: Use the "Refresh Data" button or press `Ctrl+R` to bypass the cache

### Keyboard Shortcuts

For a complete list of keyboard shortcuts, see the dedicated [Keyboard Shortcuts Guide](keyboard_shortcuts.md).

Key shortcuts include:
- `Ctrl+Shift+D`: Toggle between light and dark themes
- `Ctrl+R`: Refresh dashboard data (when dashboard is in focus)
- Press ESC to close any add-in window
- Use Tab to navigate between UI elements
- Press Enter to activate the focused button

## Best Practices

1. **Regular Updates**: Keep the add-in updated to the latest version
2. **Data Refresh**: Use the "Refresh Data" button when changes are made to the project
3. **Use Data Caching**: Leave data caching enabled for better performance during intensive work sessions
4. **CSV Exports**: Use CSV exports for detailed data analysis in Excel or other tools
5. **Theme Usage**: Use dark theme in low-light environments to reduce eye strain
6. **Keyboard Shortcuts**: Learn the keyboard shortcuts for improved workflow efficiency
7. **Error Reporting**: If you encounter issues, check Revit journal files for error messages
8. **Optimization**: Close unused windows to improve Revit performance

## Support and Feedback

If you have questions, suggestions, or need assistance:
- Email: <EMAIL>
- Website: https://www.yourcompany.com/support
- GitHub: Submit issues or feature requests on our repository
