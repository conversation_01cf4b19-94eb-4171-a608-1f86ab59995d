using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Media.Animation;
using RevitAddIn2025.Models;

namespace RevitAddIn2025.UI
{
    // Placeholder classes for code compliance
    public class ComplianceVerificationResult
    {
        public double OverallComplianceScore { get; set; }
        public Dictionary<string, ComplianceCategoryStatistics> ComplianceCategories { get; set; } = new Dictionary<string, ComplianceCategoryStatistics>();
        public List<ComplianceIssue> ComplianceIssues { get; set; } = new List<ComplianceIssue>();
        public List<CodeReference> CodeReferences { get; set; } = new List<CodeReference>();
    }

    public class ComplianceCategoryStatistics
    {
        public bool IsBelowThreshold { get; set; }
        public int IssueCount { get; set; }
    }

    public class ComplianceIssue
    {
        public ComplianceIssueSeverity Severity { get; set; }
        public ComplianceIssueType IssueType { get; set; }
        public string SystemType { get; set; } = "";
        public string Description { get; set; } = "";
        public string RecommendedAction { get; set; } = "";
        public CodeReference CodeReference { get; set; } = new CodeReference();
    }

    public class CodeReference
    {
        public string Code { get; set; } = "";
        public string Section { get; set; } = "";
        public string Title { get; set; } = "";
    }

    public enum ComplianceIssueSeverity
    {
        Advisory = 0,
        Minor = 1,
        Major = 2,
        Critical = 3
    }

    public enum ComplianceIssueType
    {
        CodeViolation = 0,
        DesignRecommendation = 1,
        SafetyIssue = 2,
        PerformanceIssue = 3
    }

    /// <summary>
    /// Provides modern UI components for visualizing code compliance verification results
    /// </summary>
    public class MEPCodeComplianceVisualization
    {
        // Constants for styling and presentation
        private readonly Color _criticalColor = Color.FromRgb(220, 53, 69);
        private readonly Color _majorColor = Color.FromRgb(255, 193, 7);
        private readonly Color _minorColor = Color.FromRgb(255, 230, 120);
        private readonly Color _advisoryColor = Color.FromRgb(23, 162, 184);
        private readonly Color _compliantColor = Color.FromRgb(40, 167, 69);

        /// <summary>
        /// Creates the main compliance overview dashboard panel
        /// </summary>
        /// <param name="verificationResult">The code compliance verification result</param>
        /// <returns>A panel with compliance visualization components</returns>
        public StackPanel CreateComplianceDashboard(ComplianceVerificationResult verificationResult)
        {
            var mainPanel = new StackPanel
            {
                Margin = new Thickness(10),
                Background = new SolidColorBrush(Color.FromRgb(250, 250, 250))
            };

            // Add compliance score card
            mainPanel.Children.Add(CreateComplianceScoreCard(verificationResult.OverallComplianceScore));

            // Add category compliance indicators
            mainPanel.Children.Add(CreateCategoryCompliancePanel(verificationResult.ComplianceCategories));

            // Add issue summary
            if (verificationResult.ComplianceIssues.Any())
            {
                mainPanel.Children.Add(new TextBlock
                {
                    Text = "Compliance Issues",
                    FontSize = 18,
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 20, 0, 10)
                });

                mainPanel.Children.Add(CreateIssuesSummaryPanel(verificationResult.ComplianceIssues));
            }
            else
            {
                mainPanel.Children.Add(CreateNoIssuesPanel());
            }

            // Add code references section
            mainPanel.Children.Add(new TextBlock
            {
                Text = "Applicable Code References",
                FontSize = 18,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 20, 0, 10)
            });

            mainPanel.Children.Add(CreateCodeReferencesPanel(verificationResult.CodeReferences));

            return mainPanel;
        }

        /// <summary>
        /// Creates a modern card showing the overall compliance score
        /// </summary>
        private UIElement CreateComplianceScoreCard(double score)
        {
            var border = new Border
            {
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 0, 15),
                Background = new SolidColorBrush(GetComplianceScoreColor(score)),
                Padding = new Thickness(20),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    ShadowDepth = 2,
                    Opacity = 0.2,
                    BlurRadius = 8
                }
            };

            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var scoreText = new TextBlock
            {
                Text = $"{Math.Round(score)}%",
                FontSize = 48,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Colors.White),
                VerticalAlignment = VerticalAlignment.Center
            };

            var detailsPanel = new StackPanel
            {
                Margin = new Thickness(20, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            var titleText = new TextBlock
            {
                Text = "Code Compliance Score",
                FontSize = 18,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Colors.White)
            };

            var descriptionText = new TextBlock
            {
                Text = GetComplianceScoreDescription(score),
                FontSize = 14,
                Foreground = new SolidColorBrush(Colors.White),
                TextWrapping = TextWrapping.Wrap,
                MaxWidth = 300
            };

            detailsPanel.Children.Add(titleText);
            detailsPanel.Children.Add(descriptionText);

            panel.Children.Add(scoreText);
            panel.Children.Add(detailsPanel);

            border.Child = panel;

            // Animate the score
            var animation = new DoubleAnimation
            {
                From = 0,
                To = score,
                Duration = TimeSpan.FromSeconds(1.5),
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
            };

            scoreText.Text = "0%";
            animation.Completed += (s, e) =>
            {
                scoreText.Text = $"{Math.Round(score)}%";
            };

            Storyboard.SetTarget(animation, scoreText);
            Storyboard.SetTargetProperty(animation, new PropertyPath("(Opacity)"));

            var storyboard = new Storyboard();
            storyboard.Children.Add(animation);

            // Start animation after element is loaded
            border.Loaded += (s, e) =>
            {
                storyboard.Begin();
            };

            return border;
        }

        /// <summary>
        /// Creates a panel showing compliance status for each category
        /// </summary>
        private UIElement CreateCategoryCompliancePanel(Dictionary<string, ComplianceCategoryStatistics> categories)
        {
            var grid = new Grid
            {
                Margin = new Thickness(0, 0, 0, 15)
            };

            // Define columns based on number of categories
            for (int i = 0; i < categories.Count; i++)
            {
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            }

            int column = 0;
            foreach (var category in categories)
            {
                var categoryCard = CreateCategoryCard(category.Key, category.Value);

                Grid.SetColumn(categoryCard, column);
                grid.Children.Add(categoryCard);

                column++;
            }

            return grid;
        }

        /// <summary>
        /// Creates a card for a single compliance category
        /// </summary>
        private UIElement CreateCategoryCard(string categoryName, ComplianceCategoryStatistics statistics)
        {
            var border = new Border
            {
                CornerRadius = new CornerRadius(6),
                Background = new SolidColorBrush(statistics.IsBelowThreshold ? _compliantColor : _majorColor),
                Margin = new Thickness(5),
                Padding = new Thickness(10, 15, 10, 15)
            };

            var panel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var nameText = new TextBlock
            {
                Text = categoryName,
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                Foreground = new SolidColorBrush(Colors.White),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var statusText = new TextBlock
            {
                Text = statistics.IsBelowThreshold ? "Compliant" : $"{statistics.IssueCount} Issues",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Colors.White),
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 5, 0, 0)
            };

            var icon = statistics.IsBelowThreshold ?
                CreateCheckmarkIcon() :
                CreateWarningIcon(statistics.IssueCount);

            panel.Children.Add(icon);
            panel.Children.Add(nameText);
            panel.Children.Add(statusText);

            border.Child = panel;

            return border;
        }

        /// <summary>
        /// Creates a panel with the issues summary
        /// </summary>
        private UIElement CreateIssuesSummaryPanel(List<ComplianceIssue> issues)
        {
            var panel = new StackPanel();

            // Group issues by severity
            var criticalIssues = issues.Where(i => i.Severity == ComplianceIssueSeverity.Critical).ToList();
            var majorIssues = issues.Where(i => i.Severity == ComplianceIssueSeverity.Major).ToList();
            var minorIssues = issues.Where(i => i.Severity == ComplianceIssueSeverity.Minor).ToList();
            var advisoryIssues = issues.Where(i => i.Severity == ComplianceIssueSeverity.Advisory).ToList();

            // Add critical issues first
            if (criticalIssues.Any())
            {
                panel.Children.Add(CreateIssueGroupPanel("Critical Issues", criticalIssues, _criticalColor));
            }

            // Add major issues
            if (majorIssues.Any())
            {
                panel.Children.Add(CreateIssueGroupPanel("Major Issues", majorIssues, _majorColor));
            }

            // Add minor issues
            if (minorIssues.Any())
            {
                panel.Children.Add(CreateIssueGroupPanel("Minor Issues", minorIssues, _minorColor));
            }

            // Add advisory issues
            if (advisoryIssues.Any())
            {
                panel.Children.Add(CreateIssueGroupPanel("Advisory Notes", advisoryIssues, _advisoryColor));
            }

            return panel;
        }

        /// <summary>
        /// Creates a panel for a group of issues with the same severity
        /// </summary>
        private UIElement CreateIssueGroupPanel(string title, List<ComplianceIssue> issues, Color color)
        {
            var expander = new Expander
            {
                Header = $"{title} ({issues.Count})",
                IsExpanded = title.Contains("Critical"), // Auto-expand critical issues
                Margin = new Thickness(0, 0, 0, 10),
                Background = new SolidColorBrush(Color.FromArgb(30, color.R, color.G, color.B))
            };

            var issuesPanel = new StackPanel();

            foreach (var issue in issues)
            {
                issuesPanel.Children.Add(CreateIssueCard(issue, color));
            }

            expander.Content = issuesPanel;

            return expander;
        }

        /// <summary>
        /// Creates a card for a single compliance issue
        /// </summary>
        private UIElement CreateIssueCard(ComplianceIssue issue, Color color)
        {
            var border = new Border
            {
                BorderBrush = new SolidColorBrush(color),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                Margin = new Thickness(0, 5, 0, 5),
                Padding = new Thickness(10)
            };

            var panel = new StackPanel();

            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var typeText = new TextBlock
            {
                Text = issue.IssueType.ToString().Replace("Violation", ""),
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(color)
            };

            var systemText = new TextBlock
            {
                Text = $" - {issue.SystemType}",
                Foreground = new SolidColorBrush(Colors.Gray)
            };

            headerPanel.Children.Add(typeText);
            headerPanel.Children.Add(systemText);

            var descriptionText = new TextBlock
            {
                Text = issue.Description,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 5, 0, 5)
            };

            var recommendationText = new TextBlock
            {
                Text = $"Recommendation: {issue.RecommendedAction}",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 5, 0, 0),
                FontStyle = FontStyles.Italic
            };

            // Code reference
            var referencePanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 5, 0, 0)
            };

            var refText = new TextBlock
            {
                Text = $"Reference: {issue.CodeReference.Code} {issue.CodeReference.Section}",
                FontSize = 11,
                Foreground = new SolidColorBrush(Colors.Gray)
            };

            referencePanel.Children.Add(refText);

            panel.Children.Add(headerPanel);
            panel.Children.Add(descriptionText);
            panel.Children.Add(recommendationText);
            panel.Children.Add(referencePanel);

            border.Child = panel;

            return border;
        }

        /// <summary>
        /// Creates a panel showing no issues were found
        /// </summary>
        private UIElement CreateNoIssuesPanel()
        {
            var border = new Border
            {
                BorderBrush = new SolidColorBrush(_compliantColor),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                Margin = new Thickness(0, 10, 0, 10),
                Padding = new Thickness(15),
                Background = new SolidColorBrush(Color.FromArgb(30, _compliantColor.R, _compliantColor.G, _compliantColor.B))
            };

            var panel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var checkmark = CreateCheckmarkIcon();

            var messageText = new TextBlock
            {
                Text = "No compliance issues were detected. The system appears to be fully compliant.",
                TextAlignment = TextAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 0, 0),
                FontSize = 16
            };

            panel.Children.Add(checkmark);
            panel.Children.Add(messageText);

            border.Child = panel;

            return border;
        }

        /// <summary>
        /// Creates a panel with code references
        /// </summary>
        private UIElement CreateCodeReferencesPanel(List<CodeReference> references)
        {
            var panel = new StackPanel();

            foreach (var reference in references)
            {
                var refPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    Margin = new Thickness(0, 5, 0, 5)
                };

                var bullet = new Ellipse
                {
                    Width = 6,
                    Height = 6,
                    Fill = new SolidColorBrush(Colors.DarkGray),
                    Margin = new Thickness(0, 0, 10, 0),
                    VerticalAlignment = VerticalAlignment.Center
                };

                var refText = new TextBlock
                {
                    Text = $"{reference.Code} {reference.Section} - {reference.Title}",
                    VerticalAlignment = VerticalAlignment.Center
                };

                refPanel.Children.Add(bullet);
                refPanel.Children.Add(refText);

                panel.Children.Add(refPanel);
            }

            return panel;
        }

        /// <summary>
        /// Creates a checkmark icon
        /// </summary>
        private UIElement CreateCheckmarkIcon()
        {
            var viewbox = new Viewbox
            {
                Width = 32,
                Height = 32
            };

            var path = new Path
            {
                Data = Geometry.Parse("M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"),
                Fill = new SolidColorBrush(Colors.White),
                Stretch = Stretch.Uniform
            };

            viewbox.Child = path;

            return viewbox;
        }

        /// <summary>
        /// Creates a warning icon
        /// </summary>
        private UIElement CreateWarningIcon(int count)
        {
            var panel = new Grid
            {
                Width = 32,
                Height = 32
            };

            var viewbox = new Viewbox
            {
                Width = 32,
                Height = 32
            };

            var path = new Path
            {
                Data = Geometry.Parse("M12,2L1,21H23M12,6L19.53,19H4.47M11,10V14H13V10M11,16V18H13V16"),
                Fill = new SolidColorBrush(Colors.White),
                Stretch = Stretch.Uniform
            };

            viewbox.Child = path;
            panel.Children.Add(viewbox);

            if (count > 0)
            {
                var countBorder = new Border
                {
                    Background = new SolidColorBrush(Colors.Red),
                    CornerRadius = new CornerRadius(10),
                    Width = 20,
                    Height = 20,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    VerticalAlignment = VerticalAlignment.Top,
                    Margin = new Thickness(0, -5, -5, 0)
                };

                var countText = new TextBlock
                {
                    Text = count.ToString(),
                    Foreground = new SolidColorBrush(Colors.White),
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };

                countBorder.Child = countText;
                panel.Children.Add(countBorder);
            }

            return panel;
        }

        /// <summary>
        /// Gets a color based on compliance score
        /// </summary>
        private Color GetComplianceScoreColor(double score)
        {
            if (score >= 90)
                return _compliantColor;
            if (score >= 75)
                return _advisoryColor;
            if (score >= 60)
                return _minorColor;
            if (score >= 40)
                return _majorColor;

            return _criticalColor;
        }

        /// <summary>
        /// Gets a description based on compliance score
        /// </summary>
        private string GetComplianceScoreDescription(double score)
        {
            if (score >= 90)
                return "Excellent. The MEP systems meet or exceed code requirements.";
            if (score >= 75)
                return "Good. Minor recommendations for improvement.";
            if (score >= 60)
                return "Acceptable. Some issues should be addressed.";
            if (score >= 40)
                return "Needs Improvement. Several code issues must be resolved.";

            return "Critical. Numerous code violations require immediate attention.";
        }
    }
}
