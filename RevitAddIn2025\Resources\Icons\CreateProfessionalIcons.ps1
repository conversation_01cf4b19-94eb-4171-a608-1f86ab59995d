# Professional Apple-Inspired Icon Generator for RevitAddIn2025
# Creates high-quality 32x32 PNG icons with modern design

Add-Type -AssemblyName System.Drawing
Add-Type -AssemblyName System.Windows.Forms

function Create-AppleIcon {
    param(
        [string]$IconName,
        [string]$BackgroundColor,
        [string]$IconColor,
        [string]$Symbol,
        [string]$OutputPath
    )
    
    # Create 32x32 bitmap
    $bitmap = New-Object System.Drawing.Bitmap(32, 32)
    $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
    
    # Enable high-quality rendering
    $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias
    $graphics.TextRenderingHint = [System.Drawing.Text.TextRenderingHint]::AntiAlias
    
    # Parse colors
    $bgColor = [System.Drawing.ColorTranslator]::FromHtml($BackgroundColor)
    $iconColor = [System.Drawing.ColorTranslator]::FromHtml($IconColor)
    
    # Create gradient background (Apple-style)
    $rect = New-Object System.Drawing.Rectangle(0, 0, 32, 32)
    $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush($rect, $bgColor, [System.Drawing.Color]::FromArgb(200, $bgColor.R, $bgColor.G, $bgColor.B), 45)
    
    # Draw rounded rectangle background
    $path = New-Object System.Drawing.Drawing2D.GraphicsPath
    $radius = 6
    $path.AddArc(0, 0, $radius * 2, $radius * 2, 180, 90)
    $path.AddArc(32 - $radius * 2, 0, $radius * 2, $radius * 2, 270, 90)
    $path.AddArc(32 - $radius * 2, 32 - $radius * 2, $radius * 2, $radius * 2, 0, 90)
    $path.AddArc(0, 32 - $radius * 2, $radius * 2, $radius * 2, 90, 90)
    $path.CloseFigure()
    
    $graphics.FillPath($brush, $path)
    
    # Add subtle border
    $borderPen = New-Object System.Drawing.Pen([System.Drawing.Color]::FromArgb(50, 0, 0, 0), 1)
    $graphics.DrawPath($borderPen, $path)
    
    # Draw icon symbol
    $font = New-Object System.Drawing.Font("Segoe UI Symbol", 16, [System.Drawing.FontStyle]::Bold)
    $textBrush = New-Object System.Drawing.SolidBrush($iconColor)
    
    # Center the text
    $textSize = $graphics.MeasureString($Symbol, $font)
    $x = (32 - $textSize.Width) / 2
    $y = (32 - $textSize.Height) / 2
    
    $graphics.DrawString($Symbol, $font, $textBrush, $x, $y)
    
    # Add subtle highlight
    $highlightBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(30, 255, 255, 255))
    $highlightRect = New-Object System.Drawing.Rectangle(2, 2, 28, 14)
    $graphics.FillEllipse($highlightBrush, $highlightRect)
    
    # Save the icon
    $bitmap.Save($OutputPath, [System.Drawing.Imaging.ImageFormat]::Png)
    
    # Cleanup
    $graphics.Dispose()
    $bitmap.Dispose()
    $brush.Dispose()
    $borderPen.Dispose()
    $font.Dispose()
    $textBrush.Dispose()
    $highlightBrush.Dispose()
    $path.Dispose()
    
    Write-Host "Created: $OutputPath" -ForegroundColor Green
}

# Create all icons with Apple-inspired design
$iconsPath = "C:\GITHUB\src\RevitAddIn2025\Resources\Icons"

Write-Host "Creating Professional Apple-Inspired Icons..." -ForegroundColor Cyan

# Dashboard Icon - Analytics theme
Create-AppleIcon -IconName "Dashboard" -BackgroundColor "#007AFF" -IconColor "#FFFFFF" -Symbol "📊" -OutputPath "$iconsPath\dashboard_icon.png"

# Settings Icon - Configuration theme  
Create-AppleIcon -IconName "Settings" -BackgroundColor "#8E8E93" -IconColor "#FFFFFF" -Symbol "⚙️" -OutputPath "$iconsPath\settings_icon.png"

# Test Icon - Verification theme
Create-AppleIcon -IconName "Test" -BackgroundColor "#34C759" -IconColor "#FFFFFF" -Symbol "🎯" -OutputPath "$iconsPath\test_icon.png"

# MEP AI Icon - Brain/AI theme
Create-AppleIcon -IconName "MEP_AI" -BackgroundColor "#FF3B30" -IconColor "#FFFFFF" -Symbol "🧠" -OutputPath "$iconsPath\mep_icon.png"

# Help Icon - Support theme
Create-AppleIcon -IconName "Help" -BackgroundColor "#FF9500" -IconColor "#FFFFFF" -Symbol "❓" -OutputPath "$iconsPath\help_icon.png"

# About Icon - Info theme
Create-AppleIcon -IconName "About" -BackgroundColor "#5856D6" -IconColor "#FFFFFF" -Symbol "ℹ️" -OutputPath "$iconsPath\about_icon.png"

# Dev Reload Icon - Refresh theme
Create-AppleIcon -IconName "DevReload" -BackgroundColor "#00C7BE" -IconColor "#FFFFFF" -Symbol "🔄" -OutputPath "$iconsPath\reload_icon.png"

Write-Host "`nAll professional icons created successfully!" -ForegroundColor Green
Write-Host "Icons saved to: $iconsPath" -ForegroundColor Yellow
