# CLEAN AND DEPLOY - RevitAddIn2025
# Resolves "Assembly with same name is already loaded" error

$ErrorActionPreference = "Stop"

Write-Host ""
Write-Host "CLEAN AND DEPLOY - RevitAddIn2025" -ForegroundColor Magenta
Write-Host "==================================" -ForegroundColor Magenta

# Configuration
$revitAddinsFolder = "$env:APPDATA\Autodesk\Revit\Addins\2025"
$scriptDir = $PSScriptRoot
$projectRoot = Split-Path -Parent $scriptDir
$buildOutput = Join-Path $projectRoot "bin\Release"

Write-Host ""
Write-Host "STEP 1: COMPLETE CLEANUP" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

# Remove all RevitAddIn2025 related files
$cleanupPaths = @(
    "$revitAddinsFolder\RevitAddIn2025*",
    "$revitAddinsFolder\*RevitAddIn2025*"
)

foreach ($pattern in $cleanupPaths) {
    $items = Get-ChildItem -Path $pattern -ErrorAction SilentlyContinue
    foreach ($item in $items) {
        try {
            if ($item.PSIsContainer) {
                Remove-Item -Path $item.FullName -Recurse -Force
                Write-Host "REMOVED FOLDER: $($item.Name)" -ForegroundColor Green
            } else {
                Remove-Item -Path $item.FullName -Force
                Write-Host "REMOVED FILE: $($item.Name)" -ForegroundColor Green
            }
        } catch {
            Write-Host "FAILED TO REMOVE: $($item.Name)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "STEP 2: VERIFY BUILD" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan

$dllPath = Join-Path $buildOutput "RevitAddIn2025.dll"
if (-not (Test-Path $dllPath)) {
    Write-Host "Building project..." -ForegroundColor Yellow
    $projectFile = Join-Path $scriptDir "RevitAddIn2025.csproj"
    & dotnet build $projectFile -c Release -p:Platform=x64 --verbosity minimal
}

if (Test-Path $dllPath) {
    $dllInfo = Get-Item $dllPath
    $sizeKB = [math]::Round($dllInfo.Length / 1024, 2)
    Write-Host "SUCCESS: DLL found - Size: $sizeKB KB" -ForegroundColor Green
} else {
    Write-Host "ERROR: DLL not found" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "STEP 3: CREATE UNIQUE DEPLOYMENT" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Create unique deployment to avoid conflicts
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$uniqueName = "RevitAddIn2025_$timestamp"
$deployFolder = Join-Path $revitAddinsFolder $uniqueName
$addinFile = Join-Path $revitAddinsFolder "$uniqueName.addin"

# Create deployment folder
New-Item -ItemType Directory -Path $deployFolder -Force | Out-Null
Write-Host "Created deployment folder: $uniqueName" -ForegroundColor Green

# Copy files
Copy-Item -Path $dllPath -Destination $deployFolder -Force
Write-Host "Copied DLL" -ForegroundColor Green

$pdbPath = Join-Path $buildOutput "RevitAddIn2025.pdb"
if (Test-Path $pdbPath) {
    Copy-Item -Path $pdbPath -Destination $deployFolder -Force
    Write-Host "Copied PDB" -ForegroundColor Green
}

Write-Host ""
Write-Host "STEP 4: CREATE UNIQUE .ADDIN FILE" -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Cyan

$dllFullPath = Join-Path $deployFolder "RevitAddIn2025.dll"
$uniqueGuid = [System.Guid]::NewGuid().ToString().ToUpper()

$addinContent = @"
<?xml version="1.0" encoding="utf-8"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>$uniqueName</Name>
    <Assembly>$dllFullPath</Assembly>
    <AddInId>$uniqueGuid</AddInId>
    <FullClassName>RevitAddIn2025.App.RevitApplication</FullClassName>
    <VendorId>REVIT2025_$timestamp</VendorId>
    <VendorDescription>Revit 2025 Add-in - Unique Instance</VendorDescription>
  </AddIn>
</RevitAddIns>
"@

Set-Content -Path $addinFile -Value $addinContent -Encoding UTF8
Write-Host "Created .addin file: $uniqueName.addin" -ForegroundColor Green

Write-Host ""
Write-Host "STEP 5: VERIFICATION" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan

if (Test-Path $dllFullPath) {
    Write-Host "SUCCESS: DLL deployed correctly" -ForegroundColor Green
} else {
    Write-Host "ERROR: DLL deployment failed" -ForegroundColor Red
    exit 1
}

if (Test-Path $addinFile) {
    Write-Host "SUCCESS: .addin file created correctly" -ForegroundColor Green
} else {
    Write-Host "ERROR: .addin file creation failed" -ForegroundColor Red
    exit 1
}

# Test assembly loading
try {
    $assembly = [System.Reflection.Assembly]::LoadFrom($dllFullPath)
    Write-Host "SUCCESS: Assembly loads without errors" -ForegroundColor Green
    
    $mainClass = $assembly.GetType("RevitAddIn2025.App.RevitApplication")
    if ($mainClass) {
        Write-Host "SUCCESS: Main application class found" -ForegroundColor Green
    } else {
        Write-Host "WARNING: Main application class not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "ERROR: Assembly loading failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "DEPLOYMENT SUMMARY" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green
Write-Host "Unique Name: $uniqueName" -ForegroundColor White
Write-Host "DLL Path: $dllFullPath" -ForegroundColor White
Write-Host ".addin File: $addinFile" -ForegroundColor White
Write-Host "GUID: $uniqueGuid" -ForegroundColor White

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Close Revit completely (wait 10 seconds)" -ForegroundColor White
Write-Host "2. Start Revit 2025" -ForegroundColor White
Write-Host "3. Look for startup dialogs" -ForegroundColor White
Write-Host "4. Check for ribbon tab: $uniqueName" -ForegroundColor White

Write-Host ""
Write-Host "If this still fails, we'll try alternative approaches." -ForegroundColor Cyan
