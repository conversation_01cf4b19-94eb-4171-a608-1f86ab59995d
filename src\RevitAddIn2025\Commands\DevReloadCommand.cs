using System;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.Development;

namespace RevitAddIn2025.Commands
{
    /// <summary>
    /// Development command to manually trigger hot reload
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class DevReloadCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                // Show current time to verify reload
                string currentTime = DateTime.Now.ToString("HH:mm:ss");
                
                TaskDialog.Show("🔄 Manual Reload", 
                    $"Triggering manual hot reload...\n\n" +
                    $"⏰ Time: {currentTime}\n" +
                    $"🔧 This will reload all command implementations\n\n" +
                    "Click OK to proceed...");

                // Trigger manual reload
                HotReloadManager.ManualReload();

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                message = $"Reload Error: {ex.Message}";
                TaskDialog.Show("Reload Error", ex.ToString());
                return Result.Failed;
            }
        }
    }
}
