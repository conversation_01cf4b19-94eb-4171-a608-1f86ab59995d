# RevitAddIn2025 New Features Guide

## Dark Theme Support

### How to Toggle Dark Mode
There are three ways to toggle between light and dark themes:

**Method 1: Via Settings**
1. Open the RevitAddIn2025 add-in in Revit 2025
2. Click on the "Settings" button in the dashboard
3. In the Settings window, find the "Dark Theme" toggle switch
4. Click the switch to toggle between light and dark themes

**Method 2: Theme Indicator**
1. Look for the small circle indicator in the top-left corner of any window
2. The indicator shows your current theme (dark/light)
3. Click on the indicator to instantly toggle between themes

**Method 3: Keyboard Shortcut**
1. From any window in the application, press Ctrl+Shift+D
2. The theme will toggle instantly between light and dark modes

### Benefits of Dark Mode
- Reduced eye strain during night-time use
- Modern, professional appearance
- Consistent with other modern applications
- Potential energy savings on OLED displays

## Enhanced Report Generation

### Generating a Project Summary Report
1. Open your Revit project
2. Launch the RevitAddIn2025 dashboard
3. Click the "Generate Report" button at the bottom of the window
4. Select "Project Summary Report" in the dialog
5. Click "Generate"
6. The report will be saved to your desktop with a timestamp in the filename

### Generating an Element Detail Report
1. Open your Revit project
2. Launch the RevitAddIn2025 dashboard
3. Click the "Generate Report" button
4. Select "Element Details Report" in the dialog
5. Choose a specific category (or "All Categories")
6. Click "Generate"
7. The report will be saved to your desktop with the category name and timestamp in the filename

### Report Contents
**Project Summary Report includes:**
- Basic project information (name, number, address)
- Element statistics (counts by type)
- File information (size, last save time)
- Workset information (if applicable)

**Element Detail Report includes:**
- Total element count for the selected category
- Elements grouped by family and type
- Detailed information about each element
- Parameter values for key parameters

## High-Resolution Display Support
The application now features enhanced visuals on high-DPI displays:
- Crisp, clear icons at all zoom levels
- Properly scaled UI elements
- Consistent visual quality across different screen resolutions

## Performance Improvements

### Data Caching System
The add-in now includes an intelligent caching system that can significantly improve responsiveness:

1. **Cache Lifetime**: Project analytics are cached for 5 minutes to provide instant access to data
2. **Cache Status Indicator**: The dashboard displays whether data is coming from cache or freshly analyzed
3. **Manual Refresh**: Cached data can be manually refreshed using the "Refresh Data" button
4. **Performance Metrics**: The cache status indicator shows load time in milliseconds
5. **Toggle Caching**: Caching can be disabled in Settings if always-fresh data is preferred

### How to Use Cache Effectively
- **First Load**: When you first open a project, data will be freshly analyzed and cached
- **Subsequent Access**: Return to the dashboard within 5 minutes to use cached data
- **After Project Changes**: Use the "Refresh Data" button to reload after making significant project changes
- **Cache Status**: Check the status indicator in the footer to see if you're viewing cached or fresh data

### CSV Export Functionality
For enhanced performance when working with large datasets:

1. Navigate to the dashboard
2. Click "Generate Report"
3. Select a specific category
4. A comprehensive CSV file will be generated with all relevant element data
5. Open the CSV in Excel or other data analysis software for further processing

## Feedback and Support
We welcome your feedback on these new features! If you encounter any issues or have suggestions for improvements, please contact support at:
- Email: <EMAIL>
- Phone: ************
- Website: www.yourcompany.com/support

## Keyboard Shortcuts
- **Ctrl+Shift+D**: Toggle between light and dark themes
- **Ctrl+R**: Refresh dashboard data (when dashboard is in focus)
- **F1**: Open help documentation
