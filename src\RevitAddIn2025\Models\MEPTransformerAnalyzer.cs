using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using RevitAddIn2025.AI;
using RevitAddIn2025.AI.Transformer;

namespace RevitAddIn2025.Models
{
    /// <summary>
    /// Analysis engine for MEP elements using transformer model
    /// </summary>
    public class MEPTransformerAnalyzer
    {
        private readonly MEPTransformerModel _transformerModel;
        private readonly Document _document;
        private readonly MEPClashDetector _clashDetector;
        private bool _isInitialized = false;
        private Dictionary<ElementId, MEPElementData> _elementCache = new Dictionary<ElementId, MEPElementData>();

        // Analysis results
        public List<MEPClashDetector.ClashResult> DetectedClashes { get; private set; } = new List<MEPClashDetector.ClashResult>();
        public Dictionary<MEPSystemClassifier.DetailedMEPSystemType, int> SystemCounts { get; private set; } = new Dictionary<MEPSystemClassifier.DetailedMEPSystemType, int>();
        public Dictionary<MEPSystemClassifier.DetailedMEPSystemType, double> SystemEfficiencyScores { get; private set; } = new Dictionary<MEPSystemClassifier.DetailedMEPSystemType, double>();
        public Dictionary<string, object> AIInsights { get; private set; } = new Dictionary<string, object>();

        // Analysis status
        public bool IsAnalysisComplete { get; private set; } = false;
        public double AnalysisProgress { get; private set; } = 0.0;
        public string CurrentAnalysisStep { get; private set; } = "Not started";

        public MEPTransformerAnalyzer(Document document, MEPTransformerModel transformerModel)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _transformerModel = transformerModel ?? throw new ArgumentNullException(nameof(transformerModel));
            _clashDetector = new MEPClashDetector(document, transformerModel);
        }

        /// <summary>
        /// Initialize the analyzer by collecting MEP elements
        /// </summary>
        public async Task Initialize()
        {
            if (_isInitialized) return;

            CurrentAnalysisStep = "Initializing";
            AnalysisProgress = 0.1;

            await Task.Run(() =>
            {
                // Collect all MEP elements in the model
                CollectMEPElements();
                AnalysisProgress = 0.3;

                // Process elements through transformer
                ProcessElementData();
                AnalysisProgress = 0.5;

                // Initialize system statistics
                CalculateSystemStatistics();
                AnalysisProgress = 0.6;

                _isInitialized = true;
                CurrentAnalysisStep = "Initialization complete";
                AnalysisProgress = 1.0;
            });
        }

        /// <summary>
        /// Collect all MEP elements from the document
        /// </summary>
        private void CollectMEPElements()
        {
            CurrentAnalysisStep = "Collecting MEP elements";
            _elementCache.Clear();

            // Collect mechanical elements
            CollectElementsByCategory(BuiltInCategory.OST_DuctCurves);
            CollectElementsByCategory(BuiltInCategory.OST_DuctFitting);
            CollectElementsByCategory(BuiltInCategory.OST_DuctTerminal);
            CollectElementsByCategory(BuiltInCategory.OST_MechanicalEquipment);

            // Collect electrical elements
            CollectElementsByCategory(BuiltInCategory.OST_CableTray);
            CollectElementsByCategory(BuiltInCategory.OST_Conduit);
            CollectElementsByCategory(BuiltInCategory.OST_ElectricalEquipment);
            CollectElementsByCategory(BuiltInCategory.OST_ElectricalFixtures);

            // Collect plumbing elements
            CollectElementsByCategory(BuiltInCategory.OST_PipeCurves);
            CollectElementsByCategory(BuiltInCategory.OST_PipeFitting);
            CollectElementsByCategory(BuiltInCategory.OST_PlumbingFixtures);
            CollectElementsByCategory(BuiltInCategory.OST_Sprinklers);
        }

        /// <summary>
        /// Collect elements by category and add them to the cache
        /// </summary>
        private void CollectElementsByCategory(BuiltInCategory category)
        {
            var collector = new FilteredElementCollector(_document);
            var elements = collector
                .OfCategory(category)
                .WhereElementIsNotElementType()
                .ToElements();

            foreach (var element in elements)
            {
                if (!_elementCache.ContainsKey(element.Id))
                {
                    _elementCache.Add(element.Id, new MEPElementData(element));
                }
            }
        }
        /// <summary>
        /// Process element data through the transformer model
        /// </summary>
        private void ProcessElementData()
        {
            CurrentAnalysisStep = "Processing elements with AI transformer";

            // Convert element data to transformer input format
            var inputData = PrepareTransformerInput();

            // Process through transformer model
            _transformerModel.ProcessMEPElements(inputData);
        }

        /// <summary>
        /// Calculate statistics about MEP systems in the model
        /// </summary>
        private void CalculateSystemStatistics()
        {
            CurrentAnalysisStep = "Analyzing system statistics";
            SystemCounts.Clear();

            // Count elements by system type
            foreach (var element in _elementCache.Values)
            {
                if (!SystemCounts.ContainsKey(element.DetailedSystemType))
                {
                    SystemCounts[element.DetailedSystemType] = 0;
                }

                SystemCounts[element.DetailedSystemType]++;
            }
        }

        /// <summary>
        /// Analyze the document for MEP clashes
        /// </summary>
        public async Task<List<MEPClashResult>> AnalyzeClashes()
        {
            if (!_isInitialized)
            {
                await Initialize();
            }

            // Get clash predictions from transformer model
            var predictions = await _transformerModel.PredictClashesAsync(_elementCache.Values.ToList());

            // Convert string predictions to MEPClashResult objects
            var clashResults = new List<MEPClashResult>();
            foreach (var prediction in predictions)
            {
                // Create a placeholder clash result from the prediction string
                clashResults.Add(new MEPClashResult(
                    new ElementId(1), // Placeholder element IDs
                    new ElementId(2),
                    XYZ.Zero, // Placeholder location
                    0.5 // Placeholder severity
                ));
            }

            return clashResults;
        }

        /// <summary>
        /// Analyze energy efficiency of MEP systems
        /// </summary>
        public async Task<MEPEnergyReport> AnalyzeEnergyEfficiency()
        {
            if (!_isInitialized)
            {
                await Initialize();
            }

            // Get energy predictions from transformer model
            var energyScore = await _transformerModel.PredictEnergyEfficiencyAsync(_elementCache.Values.ToList());

            // Convert double score to MEPEnergyReport
            var energyReport = new MEPEnergyReport
            {
                OverallEfficiencyScore = energyScore
            };

            return energyReport;
        }

        /// <summary>
        /// Analyze code compliance of MEP systems
        /// </summary>
        public async Task<List<MEPCodeViolation>> AnalyzeCodeCompliance()
        {
            if (!_isInitialized)
            {
                await Initialize();
            }

            // Get code compliance predictions from transformer model
            var complianceResults = await _transformerModel.PredictCodeComplianceAsync(_elementCache.Values.ToList());

            // Convert string results to MEPCodeViolation objects
            var violations = new List<MEPCodeViolation>();
            foreach (var result in complianceResults)
            {
                if (result.Contains("violation") || result.Contains("issue"))
                {
                    violations.Add(new MEPCodeViolation
                    {
                        ViolationCode = "GENERAL",
                        Description = result,
                        ElementId = new ElementId(1), // Placeholder
                        RecommendedFix = "Review and correct as needed"
                    });
                }
            }

            return violations;
        }
        /// <summary>
        /// Prepare input data for the transformer model
        /// </summary>
        private MEPElementBatch PrepareTransformerInput()
        {
            var batch = new MEPElementBatch();
            foreach (var element in _elementCache.Values)
            {
                batch.AddElement(element);
            }
            return batch;
        }

        /// <summary>
        /// Run full MEP analysis with clash detection, energy efficiency, and code compliance
        /// </summary>
        public async Task<AnalysisResults> RunFullAnalysisAsync()
        {
            CurrentAnalysisStep = "Starting full MEP analysis";
            AnalysisProgress = 0.0;

            try
            {
                if (!_isInitialized)
                {
                    await Initialize();
                }

                // Create results container
                var results = new AnalysisResults();

                // Run clash detection
                CurrentAnalysisStep = "Detecting MEP clashes";
                AnalysisProgress = 0.2;
                DetectedClashes = await _clashDetector.DetectClashesAsync(_elementCache.Values.ToList());
                results.Clashes = DetectedClashes;

                // Process clash results with AI for optimization insights
                CurrentAnalysisStep = "Analyzing clashes with AI transformer";
                AnalysisProgress = 0.4;
                await EnhanceClashesWithAI(results);

                // Analyze energy efficiency
                CurrentAnalysisStep = "Calculating energy efficiency";
                AnalysisProgress = 0.6;
                await CalculateEnergyEfficiency(results);

                // Analyze code compliance
                CurrentAnalysisStep = "Verifying code compliance";
                AnalysisProgress = 0.8;
                await VerifyCodeCompliance(results);

                // Generate final report
                CurrentAnalysisStep = "Generating final analysis report";
                AnalysisProgress = 0.9;
                GenerateFinalReport(results);

                // Complete
                CurrentAnalysisStep = "Analysis complete";
                AnalysisProgress = 1.0;
                IsAnalysisComplete = true;

                return results;
            }
            catch (Exception)
            {
                CurrentAnalysisStep = "Analysis failed";
                throw;
            }
        }

        /// <summary>
        /// Enhance clash results with AI insights
        /// </summary>
        private async Task EnhanceClashesWithAI(AnalysisResults results)
        {
            // Group clashes by system type
            var clashGroups = new Dictionary<MEPSystemClassifier.DetailedMEPSystemType, List<MEPClashDetector.ClashResult>>();

            foreach (var clash in DetectedClashes)
            {
                var systemType1 = clash.Element1.DetailedSystemType;
                var systemType2 = clash.Element2.DetailedSystemType;

                // Add to both system types' groups
                if (!clashGroups.ContainsKey(systemType1))
                {
                    clashGroups[systemType1] = new List<MEPClashDetector.ClashResult>();
                }
                clashGroups[systemType1].Add(clash);

                if (systemType1 != systemType2)
                {
                    if (!clashGroups.ContainsKey(systemType2))
                    {
                        clashGroups[systemType2] = new List<MEPClashDetector.ClashResult>();
                    }
                    clashGroups[systemType2].Add(clash);
                }
            }

            // Calculate clash rates by system type
            var clashRates = new Dictionary<MEPSystemClassifier.DetailedMEPSystemType, double>();
            foreach (var entry in SystemCounts)
            {
                var systemType = entry.Key;
                int elementCount = entry.Value;

                if (clashGroups.TryGetValue(systemType, out var clashes) && elementCount > 0)
                {
                    clashRates[systemType] = (double)clashes.Count / elementCount;
                }
                else
                {
                    clashRates[systemType] = 0.0;
                }
            }

            // Store in results
            results.ClashRatesBySystem = clashRates;

            // Use transformer model to get additional insights
            if (_transformerModel != null)
            {
                try
                {
                    var clashInsights = await _transformerModel.GetClashInsightsAsync(_elementCache.Values.ToList());
                    AIInsights = new Dictionary<string, object>
                    {
                        { "insights", clashInsights },
                        { "status", "success" }
                    };
                    results.AIInsights = AIInsights;
                }
                catch
                {
                    // AI insights failed, just continue without them
                    results.AIInsights = new Dictionary<string, object>
                    {
                        { "status", "AI analysis failed" }
                    };
                }
            }
        }

        /// <summary>
        /// Calculate energy efficiency for MEP systems
        /// </summary>
        private async Task CalculateEnergyEfficiency(AnalysisResults results)
        {
            CurrentAnalysisStep = "Calculating energy efficiency";

            try
            {
                // Use new energy optimizer for enhanced analysis
                var energyOptimizer = new MEPEnergyOptimizer(_document, _transformerModel);
                var optimizationResults = await energyOptimizer.AnalyzeEnergyEfficiencyAsync(_elementCache.Values.ToList());
                // Store results
                SystemEfficiencyScores = optimizationResults.SystemSavingsPotential.ToDictionary(
                    kvp => kvp.Key,
                    kvp => 1.0 - kvp.Value);  // Convert savings potential to efficiency score

                results.EnergyEfficiencyScores = SystemEfficiencyScores;
                results.OverallEfficiencyScore = 1.0 - optimizationResults.OverallSavingsPotential;

                // Convert MEPEnergyOptimizer.OptimizationResults to MEPEnergyOptimizationResults
                results.EnergyOptimizationResults = ConvertOptimizationResults(optimizationResults);
            }
            catch (Exception)
            {
                // Fall back to basic implementation if optimizer fails
                SystemEfficiencyScores = new Dictionary<MEPSystemClassifier.DetailedMEPSystemType, double>();

                // Get systems with sufficient elements for analysis
                var validSystems = SystemCounts
                    .Where(s => s.Value >= 5) // Need at least 5 elements for meaningful analysis
                    .Select(s => s.Key)
                    .ToList();

                foreach (var systemType in validSystems)
                {
                    // Get elements of this system type
                    var systemElements = _elementCache.Values
                        .Where(e => e.DetailedSystemType == systemType)
                        .ToList();

                    // Calculate base efficiency score (simplified for this example)
                    double efficiencyScore = CalculateBaseEfficiencyScore(systemType, systemElements);

                    SystemEfficiencyScores[systemType] = efficiencyScore;
                }

                results.EnergyEfficiencyScores = SystemEfficiencyScores;
                results.OverallEfficiencyScore = SystemEfficiencyScores.Count > 0 ?
                    SystemEfficiencyScores.Values.Average() : 0.5;
            }
        }

        /// <summary>
        /// Calculate a base efficiency score for a system type
        /// </summary>
        private double CalculateBaseEfficiencyScore(MEPSystemClassifier.DetailedMEPSystemType systemType, List<MEPElementData> elements)
        {
            // This is a placeholder implementation - a real version would be more sophisticated
            // and use proper energy calculations based on the system type

            // Default score - average
            double score = 0.5;

            // Apply simple heuristics based on system type
            switch (systemType)
            {
                case MEPSystemClassifier.DetailedMEPSystemType.SupplyAir:
                case MEPSystemClassifier.DetailedMEPSystemType.ReturnAir:
                    // Count 90-degree turns (more turns = less efficient)
                    int sharpTurns = CountSharpTurns(elements);
                    score = Math.Max(0.3, 0.8 - (sharpTurns * 0.05));
                    break;

                case MEPSystemClassifier.DetailedMEPSystemType.DomesticHotWater:
                    // Check insulation and pipe lengths
                    bool hasInsulation = elements.Any(e => e.ElementName.Contains("insul"));
                    score = hasInsulation ? 0.7 : 0.4;
                    break;

                case MEPSystemClassifier.DetailedMEPSystemType.PowerDistribution:
                case MEPSystemClassifier.DetailedMEPSystemType.Lighting:
                    // Simple placeholder for electrical
                    score = 0.6;
                    break;

                default:
                    score = 0.5;
                    break;
            }

            return score;
        }
        /// <summary>
        /// Count sharp turns in ducting or piping for efficiency calculations
        /// </summary>
        private int CountSharpTurns(List<MEPElementData> elements)
        {
            // This is a placeholder - would need proper geometric analysis in a real implementation
            return elements.Count / 10;
        }

        /// <summary>
        /// Convert MEPEnergyOptimizer.OptimizationResults to MEPEnergyOptimizationResults
        /// </summary>
        private MEPEnergyOptimizationResults ConvertOptimizationResults(MEPEnergyOptimizer.OptimizationResults source)
        {
            var result = new MEPEnergyOptimizationResults
            {
                EnergyEfficiencyScore = 1.0 - source.OverallSavingsPotential,
                PotentialSavings = source.OverallSavingsPotential,
                Recommendations = source.AIRecommendations.ToArray(),
                OptimizedFlowRate = 0.0, // Default values
                OptimizedPressure = 0.0,
                OptimizedTemperature = 70.0,
                RequiresSystemModification = source.Recommendations.Any(r => r.EnergySavingsPotential > 0.15) // Use savings potential instead
            };

            return result;
        }

        /// <summary>
        /// Verify code compliance for MEP systems
        /// </summary>
        private async Task VerifyCodeCompliance(AnalysisResults results)
        {
            var complianceIssues = new List<ComplianceIssue>();

            // Check basic clearance requirements
            foreach (var clash in DetectedClashes)
            {
                // For clashes with severe intersection volume
                if (clash.IntersectionVolume > 0.3)
                {
                    complianceIssues.Add(new ComplianceIssue
                    {
                        IssueType = ComplianceIssueType.InsufficientClearance,
                        SystemType = clash.Element1.DetailedSystemType,
                        Description = $"Insufficient clearance between {clash.Element1.ElementName} and {clash.Element2.ElementName}",
                        RecommendedFix = clash.ResolutionDescription,
                        Severity = ComplianceIssueSeverity.Major
                    });
                }
            }

            // Check fire protection systems for code compliance
            var fireProtectionElements = _elementCache.Values
                .Where(e => e.DetailedSystemType == MEPSystemClassifier.DetailedMEPSystemType.FireProtection)
                .ToList();

            if (fireProtectionElements.Any())
            {
                // Check for appropriate coverage
                var coverageIssue = CheckFireProtectionCoverage(fireProtectionElements);
                if (coverageIssue != null)
                {
                    complianceIssues.Add(coverageIssue);
                }
            }

            // Use transformer model for advanced code checks if available
            if (_transformerModel != null)
            {
                try
                {
                    var isCompliant = await _transformerModel.CheckCodeComplianceAsync(_elementCache.Values.ToList());
                    if (!isCompliant)
                    {
                        complianceIssues.Add(new ComplianceIssue
                        {
                            IssueType = ComplianceIssueType.CodeViolation,
                            SystemType = MEPSystemClassifier.DetailedMEPSystemType.Other,
                            Description = "AI analysis detected potential code compliance issues",
                            RecommendedFix = "Review system design for code compliance",
                            Severity = ComplianceIssueSeverity.Major
                        });
                    }
                }
                catch
                {
                    // AI compliance check failed, just continue with basic checks
                }
            }

            results.ComplianceIssues = complianceIssues;
        }

        /// <summary>
        /// Check fire protection coverage - placeholder implementation
        /// </summary>
        private ComplianceIssue CheckFireProtectionCoverage(List<MEPElementData> fireProtectionElements)
        {
            // This is a placeholder implementation
            // A real implementation would check spacing, coverage areas, etc.

            if (fireProtectionElements.Count < 2)
            {
                return new ComplianceIssue
                {
                    IssueType = ComplianceIssueType.InsufficientCoverage,
                    SystemType = MEPSystemClassifier.DetailedMEPSystemType.FireProtection,
                    Description = "Potentially insufficient fire protection coverage. Review sprinkler layout.",
                    RecommendedFix = "Add additional sprinklers to ensure proper coverage per code requirements.",
                    Severity = ComplianceIssueSeverity.Critical
                };
            }

            return null;
        }

        /// <summary>
        /// Generate final analysis report with all insights
        /// </summary>
        private void GenerateFinalReport(AnalysisResults results)
        {
            results.TotalElementCount = _elementCache.Count;
            results.CompletedAt = DateTime.Now;

            // Calculate overall statistics
            results.OverallClashRate = _elementCache.Count > 0 ?
                (double)DetectedClashes.Count / _elementCache.Count : 0;

            results.OverallEfficiencyScore = SystemEfficiencyScores.Count > 0 ?
                SystemEfficiencyScores.Values.Average() : 0.5;

            results.CriticalIssuesCount = results.ComplianceIssues?
                .Count(i => i.Severity == ComplianceIssueSeverity.Critical) ?? 0;
        }

        /// <summary>
        /// Results of a full MEP analysis
        /// </summary>
        public class AnalysisResults
        {
            public DateTime CompletedAt { get; set; }
            public int TotalElementCount { get; set; }
            public List<MEPClashDetector.ClashResult> Clashes { get; set; } = new List<MEPClashDetector.ClashResult>();
            public double OverallClashRate { get; set; }
            public Dictionary<MEPSystemClassifier.DetailedMEPSystemType, double> ClashRatesBySystem { get; set; }
            public Dictionary<MEPSystemClassifier.DetailedMEPSystemType, double> EnergyEfficiencyScores { get; set; }
            public double OverallEfficiencyScore { get; set; }
            public List<ComplianceIssue> ComplianceIssues { get; set; } = new List<ComplianceIssue>();
            public int CriticalIssuesCount { get; set; }
            public Dictionary<string, object> AIInsights { get; set; }
            public MEPEnergyOptimizationResults EnergyOptimizationResults { get; set; }
        }

        /// <summary>
        /// Represents a code compliance issue
        /// </summary>
        public class ComplianceIssue
        {
            public ComplianceIssueType IssueType { get; set; }
            public MEPSystemClassifier.DetailedMEPSystemType SystemType { get; set; }
            public string Description { get; set; }
            public string RecommendedFix { get; set; }
            public ComplianceIssueSeverity Severity { get; set; }
            public Dictionary<string, object> AdditionalData { get; set; }
        }

        /// <summary>
        /// Types of compliance issues
        /// </summary>
        public enum ComplianceIssueType
        {
            InsufficientClearance,
            InsufficientCoverage,
            ExcessivePressureDrop,
            ImproperSlope,
            InadequateSupport,
            ImproperConnection,
            CodeViolation,
            Other
        }

        /// <summary>
        /// Severity levels for compliance issues
        /// </summary>
        public enum ComplianceIssueSeverity
        {
            Minor,
            Moderate,
            Major,
            Critical
        }
    }
}
