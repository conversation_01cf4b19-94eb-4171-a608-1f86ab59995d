using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using RevitAddIn2025.Models;
using RevitAddIn2025.UI;
using RevitAddIn2025.Utilities;
using ModelMEPSystemType = RevitAddIn2025.Models.MEPSystemType;

namespace RevitAddIn2025.AI
{
    /// <summary>
    /// Smart Analysis Integration System
    /// Combines placement system with Energy Analytics and Clash Detection for optimal MEP placement
    /// </summary>
    public class SmartAnalysisIntegrator
    {
        private readonly Document _document;
        private readonly MEPTransformerModel _aiModel;
        private readonly VectorPlacementEngine _placementEngine;
        private readonly CeilingAnalysisEngine _ceilingEngine;

        // Integration weights for multi-objective optimization
        private const double ENERGY_WEIGHT = 0.3;
        private const double CLASH_WEIGHT = 0.3;
        private const double COMPLIANCE_WEIGHT = 0.25;
        private const double COST_WEIGHT = 0.15;

        public SmartAnalysisIntegrator(
            Document document,
            MEPTransformerModel aiModel,
            VectorPlacementEngine placementEngine,
            CeilingAnalysisEngine ceilingEngine)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _aiModel = aiModel ?? throw new ArgumentNullException(nameof(aiModel));
            _placementEngine = placementEngine ?? throw new ArgumentNullException(nameof(placementEngine));
            _ceilingEngine = ceilingEngine ?? throw new ArgumentNullException(nameof(ceilingEngine));
        }

        /// <summary>
        /// Performs comprehensive smart analysis for optimal MEP placement
        /// </summary>
        public async Task<PlacementAnalysisResult> PerformSmartAnalysis(
            CeilingSpace space,
            ModelMEPSystemType systemType,
            PlacementParameters parameters)
        {
            var result = new PlacementAnalysisResult
            {
                AnalysisId = Guid.NewGuid().ToString(),
                AnalysisTimestamp = DateTime.Now,
                AnalyzedSpace = space,
                SystemType = systemType
            };

            try
            {
                Logger.Info($"Starting smart analysis for {systemType} in space {space.SpaceId}");

                // Step 1: Generate initial placement vectors
                var placementVectors = await _placementEngine.CalculateOptimalPlacementVectors(space, systemType, parameters);

                // Step 2: Integrate with Energy Analytics
                var energyOptimizedVectors = await IntegrateEnergyAnalytics(placementVectors, space, systemType);

                // Step 3: Integrate with Clash Detection
                var clashOptimizedVectors = await IntegrateClashDetection(energyOptimizedVectors, space);

                // Step 4: Apply code compliance analysis
                var complianceOptimizedVectors = await ApplyCodeComplianceAnalysis(clashOptimizedVectors, systemType, space);

                // Step 5: Perform machine learning optimization
                var mlOptimizedVectors = await ApplyMachineLearningOptimization(complianceOptimizedVectors, space, systemType);

                // Step 6: Generate final recommendations with scoring
                result.Recommendations = await GenerateRecommendations(mlOptimizedVectors, space, systemType, parameters);

                // Step 7: Calculate statistics
                result.Statistics = CalculateStatistics(result.Recommendations, space);

                result.AnalysisSuccessful = true;
                Logger.Info($"Smart analysis completed successfully with {result.Recommendations.Count} recommendations");
            }
            catch (Exception ex)
            {
                Logger.Error("Error during smart analysis", ex);
                result.AnalysisSuccessful = false;
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// Integrates with Energy Analytics for energy-optimized placement
        /// </summary>
        private async Task<List<PlacementVector>> IntegrateEnergyAnalytics(
            List<PlacementVector> placementVectors,
            CeilingSpace space,
            ModelMEPSystemType systemType)
        {
            var optimizedVectors = new List<PlacementVector>();

            try
            {
                Logger.Info("Integrating with Energy Analytics system");

                foreach (var vector in placementVectors)
                {
                    // Calculate energy efficiency score for this placement
                    double energyScore = await CalculateEnergyEfficiencyScore(vector, space, systemType);

                    // Update vector with energy data
                    vector.EnergyEfficiencyScore = energyScore;

                    // Apply energy optimization adjustments
                    var optimizedVector = await ApplyEnergyOptimizations(vector, space, systemType);

                    optimizedVectors.Add(optimizedVector);
                }

                // Sort by energy efficiency and select top performers
                var topEnergyVectors = optimizedVectors
                    .OrderByDescending(v => v.EnergyEfficiencyScore)
                    .Take(Math.Max(10, optimizedVectors.Count / 2))
                    .ToList();

                Logger.Info($"Energy analytics integration completed. Selected {topEnergyVectors.Count} energy-optimized vectors");
                return topEnergyVectors;
            }
            catch (Exception ex)
            {
                Logger.Error("Error integrating energy analytics", ex);
                return placementVectors; // Return original vectors if integration fails
            }
        }

        /// <summary>
        /// Calculates energy efficiency score for a placement vector
        /// </summary>
        private async Task<double> CalculateEnergyEfficiencyScore(
            PlacementVector vector,
            CeilingSpace space,
            MEPSystemType systemType)
        {
            return await Task.Run(() =>
            {
                double score = 0.5; // Base score

                try
                {
                    switch (systemType)
                    {
                        case MEPSystemType.Lighting:
                            score = CalculateLightingEnergyScore(vector, space);
                            break;
                        case MEPSystemType.HVAC:
                            score = CalculateHVACEnergyScore(vector, space);
                            break;
                        default:
                            score = 0.7; // Default good score for other systems
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error("Error calculating energy efficiency score", ex);
                }

                return Math.Max(0.0, Math.Min(1.0, score)); // Clamp between 0 and 1
            });
        }

        /// <summary>
        /// Calculates lighting energy efficiency score
        /// </summary>
        private double CalculateLightingEnergyScore(PlacementVector vector, CeilingSpace space)
        {
            double score = 0.5;

            try
            {
                // Calculate distance from optimal positions
                double distanceFromCenter = CalculateDistanceFromCenter(vector.Position, space.CenterPoint);
                double maxDistance = Math.Sqrt(Math.Pow(space.Width / 2, 2) + Math.Pow(space.Length / 2, 2));
                double centerScore = 1.0 - (distanceFromCenter / maxDistance);

                // Calculate coverage efficiency
                double coverageScore = CalculateCoverageEfficiency(vector, space);

                // Calculate uniformity score
                double uniformityScore = CalculateUniformityScore(vector, space);

                // Weighted combination
                score = (centerScore * 0.3) + (coverageScore * 0.4) + (uniformityScore * 0.3);

                // Bonus for energy-efficient positioning
                if (IsEnergyEfficientPosition(vector, space))
                {
                    score += 0.1;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error calculating lighting energy score", ex);
            }

            return score;
        }

        /// <summary>
        /// Integrates with Clash Detection AI for conflict-free placement
        /// </summary>
        private async Task<List<PlacementVector>> IntegrateClashDetection(
            List<PlacementVector> placementVectors,
            CeilingSpace space)
        {
            var clashFreeVectors = new List<PlacementVector>();

            try
            {
                Logger.Info("Integrating with Clash Detection AI");

                // Get existing MEP elements for clash analysis
                var existingElements = space.ExistingMEPElements;

                foreach (var vector in placementVectors)
                {
                    // Calculate clash risk score
                    double clashRisk = await CalculateClashRiskScore(vector, existingElements, space);
                    vector.ClashRiskScore = clashRisk;

                    // Apply clash avoidance adjustments
                    var adjustedVector = await ApplyClashAvoidanceAdjustments(vector, existingElements, space);

                    // Only include vectors with acceptable clash risk
                    if (adjustedVector.ClashRiskScore < 0.3) // Less than 30% clash risk
                    {
                        clashFreeVectors.Add(adjustedVector);
                    }
                }

                Logger.Info($"Clash detection integration completed. {clashFreeVectors.Count} clash-free vectors identified");
                return clashFreeVectors;
            }
            catch (Exception ex)
            {
                Logger.Error("Error integrating clash detection", ex);
                return placementVectors; // Return original vectors if integration fails
            }
        }

        /// <summary>
        /// Calculates clash risk score for a placement vector
        /// </summary>
        private async Task<double> CalculateClashRiskScore(
            PlacementVector vector,
            List<MEPElementData> existingElements,
            CeilingSpace space)
        {
            return await Task.Run(() =>
            {
                double riskScore = 0.0;

                try
                {
                    foreach (var element in existingElements)
                    {
                        double distance = CalculateDistance(vector.Position, element.Position);
                        double requiredClearance = GetRequiredClearance(vector.SystemType, element.Discipline);

                        if (distance < requiredClearance)
                        {
                            // Calculate risk based on proximity
                            double proximityRisk = 1.0 - (distance / requiredClearance);
                            riskScore = Math.Max(riskScore, proximityRisk);
                        }
                    }

                    // Check for structural clashes
                    double structuralRisk = CalculateStructuralClashRisk(vector, space);
                    riskScore = Math.Max(riskScore, structuralRisk);
                }
                catch (Exception ex)
                {
                    Logger.Error("Error calculating clash risk score", ex);
                    riskScore = 0.5; // Default moderate risk
                }

                return Math.Max(0.0, Math.Min(1.0, riskScore));
            });
        }

        /// <summary>
        /// Applies code compliance analysis
        /// </summary>
        private async Task<List<PlacementVector>> ApplyCodeComplianceAnalysis(
            List<PlacementVector> placementVectors,
            MEPSystemType systemType,
            CeilingSpace space)
        {
            var compliantVectors = new List<PlacementVector>();

            try
            {
                Logger.Info("Applying code compliance analysis");

                foreach (var vector in placementVectors)
                {
                    bool isCompliant = await CheckCodeCompliance(vector, systemType, space);

                    if (isCompliant)
                    {
                        // Add compliance notes
                        vector.ComplianceNotes.AddRange(GetComplianceNotes(vector, systemType, space));
                        compliantVectors.Add(vector);
                    }
                }

                Logger.Info($"Code compliance analysis completed. {compliantVectors.Count} compliant vectors identified");
                return compliantVectors;
            }
            catch (Exception ex)
            {
                Logger.Error("Error applying code compliance analysis", ex);
                return placementVectors;
            }
        }

        /// <summary>
        /// Applies machine learning optimization for usage pattern analysis
        /// </summary>
        private async Task<List<PlacementVector>> ApplyMachineLearningOptimization(
            List<PlacementVector> placementVectors,
            CeilingSpace space,
            MEPSystemType systemType)
        {
            try
            {
                Logger.Info("Applying machine learning optimization");

                // Use the AI model for pattern analysis and optimization
                var mepElements = placementVectors.Select(v => new MEPElementData
                {
                    Position = new MEPPosition3D { X = v.Position.X, Y = v.Position.Y, Z = v.Position.Z },
                    SystemType = systemType.ToString(),
                    Discipline = ConvertSystemTypeToDiscipline(systemType)
                }).ToList();

                // Process through AI model for optimization
                var optimizationResults = await _aiModel.ProcessMEPElements(mepElements);

                // Apply ML insights to improve placement quality
                foreach (var vector in placementVectors)
                {
                    // Apply ML-based quality improvements
                    vector.QualityScore = await CalculateMLEnhancedQualityScore(vector, space, systemType);
                }

                // Sort by ML-enhanced quality score
                var optimizedVectors = placementVectors
                    .OrderByDescending(v => v.QualityScore)
                    .ToList();

                Logger.Info("Machine learning optimization completed");
                return optimizedVectors;
            }
            catch (Exception ex)
            {
                Logger.Error("Error applying machine learning optimization", ex);
                return placementVectors;
            }
        }

        /// <summary>
        /// Generates final recommendations with comprehensive scoring
        /// </summary>
        private async Task<List<PlacementRecommendation>> GenerateRecommendations(
            List<PlacementVector> optimizedVectors,
            CeilingSpace space,
            MEPSystemType systemType,
            PlacementParameters parameters)
        {
            var recommendations = new List<PlacementRecommendation>();

            try
            {
                Logger.Info("Generating final placement recommendations");

                foreach (var vector in optimizedVectors)
                {
                    var recommendation = new PlacementRecommendation
                    {
                        PlacementVector = vector,
                        EnergyScore = vector.EnergyEfficiencyScore,
                        ClashScore = 1.0 - vector.ClashRiskScore, // Invert risk to score
                        ComplianceScore = vector.ComplianceNotes.Any() ? 1.0 : 0.8
                    };

                    // Calculate cost score
                    recommendation.CostScore = await CalculateCostScore(vector, space, systemType);

                    // Calculate overall score using weighted combination
                    recommendation.OverallScore =
                        (recommendation.EnergyScore * ENERGY_WEIGHT) +
                        (recommendation.ClashScore * CLASH_WEIGHT) +
                        (recommendation.ComplianceScore * COMPLIANCE_WEIGHT) +
                        (recommendation.CostScore * COST_WEIGHT);

                    // Determine if recommended
                    recommendation.IsRecommended = recommendation.OverallScore > 0.7;

                    // Generate recommendation reason and benefits
                    recommendation.RecommendationReason = GenerateRecommendationReason(recommendation);
                    recommendation.Benefits = GenerateBenefits(recommendation, systemType);
                    recommendation.Considerations = GenerateConsiderations(recommendation, systemType);

                    recommendations.Add(recommendation);
                }

                // Sort by overall score
                recommendations = recommendations
                    .OrderByDescending(r => r.OverallScore)
                    .Take(20) // Limit to top 20 recommendations
                    .ToList();

                Logger.Info($"Generated {recommendations.Count} final recommendations");
            }
            catch (Exception ex)
            {
                Logger.Error("Error generating recommendations", ex);
            }

            return recommendations;
        }

        #region Helper Methods

        private double CalculateDistanceFromCenter(Vector3D position, Vector3D center)
        {
            return Math.Sqrt(
                Math.Pow(position.X - center.X, 2) +
                Math.Pow(position.Y - center.Y, 2) +
                Math.Pow(position.Z - center.Z, 2)
            );
        }

        private double CalculateDistance(Vector3D pos1, MEPPosition3D pos2)
        {
            return Math.Sqrt(
                Math.Pow(pos1.X - pos2.X, 2) +
                Math.Pow(pos1.Y - pos2.Y, 2) +
                Math.Pow(pos1.Z - pos2.Z, 2)
            );
        }

        private double GetRequiredClearance(MEPSystemType systemType, MEPDiscipline discipline)
        {
            // Return required clearance based on system types
            return systemType switch
            {
                MEPSystemType.Lighting => 1.5,
                MEPSystemType.HVAC => 2.0,
                MEPSystemType.FireSafety => 1.0,
                _ => 1.5
            };
        }

        private MEPDiscipline ConvertSystemTypeToDiscipline(MEPSystemType systemType)
        {
            return systemType switch
            {
                MEPSystemType.Lighting => MEPDiscipline.Electrical,
                MEPSystemType.HVAC => MEPDiscipline.Mechanical,
                MEPSystemType.FireSafety => MEPDiscipline.FireProtection,
                _ => MEPDiscipline.Other
            };
        }

        #endregion
    }
}
