# MINIMAL TEST DEPLOY - RevitAddIn2025
# Create a minimal version to test if the basic loading works

$ErrorActionPreference = "Stop"

Write-Host ""
Write-Host "MINIMAL TEST DEPLOY - RevitAddIn2025" -ForegroundColor Magenta
Write-Host "====================================" -ForegroundColor Magenta

# Configuration
$revitAddinsFolder = "$env:APPDATA\Autodesk\Revit\Addins\2025"
$scriptDir = $PSScriptRoot

Write-Host ""
Write-Host "STEP 1: CREATE MINIMAL TEST VERSION" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# Create a minimal RevitApplication class that should definitely work
$minimalCode = @"
using System;
using Autodesk.Revit.UI;

namespace RevitAddIn2025.App
{
    public class RevitApplication : IExternalApplication
    {
        public Result OnStartup(UIControlledApplication application)
        {
            try
            {
                TaskDialog.Show("MINIMAL TEST SUCCESS", 
                    "🎉 MINIMAL VERSION LOADED SUCCESSFULLY!\n\n" +
                    "This proves the basic loading mechanism works.\n" +
                    "If you see this dialog, the issue is with the complex code,\n" +
                    "not with the deployment or .addin file.");
                
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                TaskDialog.Show("MINIMAL TEST ERROR", 
                    "Error in minimal version: " + ex.Message);
                return Result.Failed;
            }
        }

        public Result OnShutdown(UIControlledApplication application)
        {
            return Result.Succeeded;
        }
    }
}
"@

# Create temporary directory for minimal build
$tempDir = Join-Path $env:TEMP "RevitAddIn2025_Minimal"
if (Test-Path $tempDir) {
    Remove-Item -Path $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# Create minimal project file
$minimalProject = @"
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <Platform>x64</Platform>
    <AssemblyName>RevitAddIn2025_Minimal</AssemblyName>
    <RootNamespace>RevitAddIn2025</RootNamespace>
  </PropertyGroup>
  
  <ItemGroup>
    <Reference Include="RevitAPI">
      <HintPath>C:\Program Files\Autodesk\Revit 2025\RevitAPI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RevitAPIUI">
      <HintPath>C:\Program Files\Autodesk\Revit 2025\RevitAPIUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
</Project>
"@

Set-Content -Path (Join-Path $tempDir "RevitAddIn2025_Minimal.csproj") -Value $minimalProject
Set-Content -Path (Join-Path $tempDir "RevitApplication.cs") -Value $minimalCode

Write-Host "Created minimal test project" -ForegroundColor Green

Write-Host ""
Write-Host "STEP 2: BUILD MINIMAL VERSION" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

# Build the minimal version
Push-Location $tempDir
try {
    & dotnet build -c Release -p:Platform=x64 --verbosity minimal
    if ($LASTEXITCODE -eq 0) {
        Write-Host "SUCCESS: Minimal version built" -ForegroundColor Green
    } else {
        Write-Host "ERROR: Minimal build failed" -ForegroundColor Red
        exit 1
    }
} finally {
    Pop-Location
}

Write-Host ""
Write-Host "STEP 3: DEPLOY MINIMAL VERSION" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

# Clean existing deployments
$cleanupPaths = @(
    "$revitAddinsFolder\RevitAddIn2025*",
    "$revitAddinsFolder\*RevitAddIn2025*"
)

foreach ($pattern in $cleanupPaths) {
    $items = Get-ChildItem -Path $pattern -ErrorAction SilentlyContinue
    foreach ($item in $items) {
        Remove-Item -Path $item.FullName -Recurse -Force -ErrorAction SilentlyContinue
        Write-Host "Removed: $($item.Name)" -ForegroundColor Yellow
    }
}

# Find the built DLL
$builtDll = Get-ChildItem -Path $tempDir -Recurse -Filter "RevitAddIn2025_Minimal.dll" | Select-Object -First 1

if (-not $builtDll) {
    Write-Host "ERROR: Built DLL not found" -ForegroundColor Red
    exit 1
}

Write-Host "Found built DLL: $($builtDll.FullName)" -ForegroundColor Green

# Create unique deployment
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$deployName = "RevitAddIn2025_Minimal_$timestamp"
$deployFolder = Join-Path $revitAddinsFolder $deployName
$addinFile = Join-Path $revitAddinsFolder "$deployName.addin"

# Create deployment folder and copy DLL
New-Item -ItemType Directory -Path $deployFolder -Force | Out-Null
Copy-Item -Path $builtDll.FullName -Destination $deployFolder -Force

$deployedDll = Join-Path $deployFolder $builtDll.Name
Write-Host "Deployed DLL to: $deployedDll" -ForegroundColor Green

# Create .addin file
$uniqueGuid = [System.Guid]::NewGuid().ToString().ToUpper()
$addinContent = @"
<?xml version="1.0" encoding="utf-8"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>$deployName</Name>
    <Assembly>$deployedDll</Assembly>
    <AddInId>$uniqueGuid</AddInId>
    <FullClassName>RevitAddIn2025.App.RevitApplication</FullClassName>
    <VendorId>MINIMAL_TEST_$timestamp</VendorId>
    <VendorDescription>Minimal Test Version</VendorDescription>
  </AddIn>
</RevitAddIns>
"@

Set-Content -Path $addinFile -Value $addinContent -Encoding UTF8
Write-Host "Created .addin file: $addinFile" -ForegroundColor Green

Write-Host ""
Write-Host "STEP 4: VERIFICATION" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan

if (Test-Path $deployedDll) {
    Write-Host "SUCCESS: DLL deployed" -ForegroundColor Green
} else {
    Write-Host "ERROR: DLL deployment failed" -ForegroundColor Red
    exit 1
}

if (Test-Path $addinFile) {
    Write-Host "SUCCESS: .addin file created" -ForegroundColor Green
} else {
    Write-Host "ERROR: .addin file creation failed" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "MINIMAL TEST DEPLOYMENT COMPLETE" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green
Write-Host ""
Write-Host "DEPLOYMENT DETAILS:" -ForegroundColor Cyan
Write-Host "Name: $deployName" -ForegroundColor White
Write-Host "DLL: $deployedDll" -ForegroundColor White
Write-Host ".addin: $addinFile" -ForegroundColor White
Write-Host "GUID: $uniqueGuid" -ForegroundColor White

Write-Host ""
Write-Host "TEST INSTRUCTIONS:" -ForegroundColor Yellow
Write-Host "1. Close Revit completely" -ForegroundColor White
Write-Host "2. Start Revit 2025" -ForegroundColor White
Write-Host "3. Look for 'MINIMAL TEST SUCCESS' dialog" -ForegroundColor White
Write-Host ""
Write-Host "EXPECTED RESULTS:" -ForegroundColor Cyan
Write-Host "✅ If dialog appears: Basic loading works, issue is in complex code" -ForegroundColor Green
Write-Host "❌ If no dialog: Fundamental deployment/loading issue" -ForegroundColor Red

Write-Host ""
Write-Host "Cleanup temp directory..." -ForegroundColor Gray
Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
