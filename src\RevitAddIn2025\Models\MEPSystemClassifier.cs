using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Plumbing;

namespace RevitAddIn2025.Models
{
    /// <summary>
    /// Advanced classifier for MEP systems to enhance transformer AI understanding of system types
    /// </summary>
    public class MEPSystemClassifier
    {
        // System type classification with more granular details
        public enum DetailedMEPSystemType
        {
            // Mechanical systems
            SupplyAir,
            ReturnAir,
            ExhaustAir,
            OutdoorAir,
            MixedAir,

            // Plumbing systems
            DomesticColdWater,
            DomesticHotWater,
            SanitarySewer,
            StormDrainage,
            NaturalGas,
            MedicalGas,
            FireProtection,

            // Electrical systems
            PowerDistribution,
            Lighting,
            DataComm,
            FireAlarm,
            SecuritySystem,
            EmergencyPower,

            // Default/unknown
            Unknown,
            Other
        }

        /// <summary>
        /// Classifies an MEP element into a detailed system type
        /// </summary>
        /// <param name="element">The Revit MEP element to classify</param>
        /// <returns>The detailed MEP system type</returns>
        public static DetailedMEPSystemType ClassifyMEPElement(Element element)
        {
            if (element == null)
                return DetailedMEPSystemType.Unknown;

            try
            {
                // Check for mechanical (duct) systems
                if (element is Duct duct)
                {
                    return ClassifyDuctSystem(duct);
                }
                // Check for pipe systems
                else if (element is Pipe pipe)
                {
                    return ClassifyPipeSystem(pipe);
                }                // Check for electrical systems
                else if (element.Category?.Id.Value == (int)BuiltInCategory.OST_CableTray ||
                         element.Category?.Id.Value == (int)BuiltInCategory.OST_Conduit ||
                         element.Category?.Id.Value == (int)BuiltInCategory.OST_ElectricalEquipment)
                {
                    return ClassifyElectricalSystem(element);
                }
                // All other cases
                else
                {
                    return DetailedMEPSystemType.Unknown;
                }
            }
            catch (Exception)
            {
                return DetailedMEPSystemType.Unknown;
            }
        }

        /// <summary>
        /// Classifies a duct element into a detailed system type
        /// </summary>
        private static DetailedMEPSystemType ClassifyDuctSystem(Duct duct)
        {
            try
            {
                MEPSystem system = duct.MEPSystem;

                if (system == null)
                    return DetailedMEPSystemType.Unknown;

                // Check for system type from system name
                string systemName = system.Name.ToLowerInvariant();

                if (systemName.Contains("supply") || systemName.Contains("sa "))
                    return DetailedMEPSystemType.SupplyAir;

                if (systemName.Contains("return") || systemName.Contains("ra "))
                    return DetailedMEPSystemType.ReturnAir;

                if (systemName.Contains("exhaust") || systemName.Contains("ea "))
                    return DetailedMEPSystemType.ExhaustAir;

                if (systemName.Contains("outside") || systemName.Contains("outdoor") || systemName.Contains("oa "))
                    return DetailedMEPSystemType.OutdoorAir;

                if (systemName.Contains("mixed") || systemName.Contains("ma "))
                    return DetailedMEPSystemType.MixedAir;

                // Check system type by analyzing duct properties
                DuctType ductType = duct.Document.GetElement(duct.GetTypeId()) as DuctType;
                if (ductType != null)
                {
                    string typeName = ductType.Name.ToLowerInvariant();

                    if (typeName.Contains("supply"))
                        return DetailedMEPSystemType.SupplyAir;

                    if (typeName.Contains("return"))
                        return DetailedMEPSystemType.ReturnAir;

                    if (typeName.Contains("exhaust"))
                        return DetailedMEPSystemType.ExhaustAir;

                    if (typeName.Contains("outdoor") || typeName.Contains("oa"))
                        return DetailedMEPSystemType.OutdoorAir;
                }

                // Default to Unknown if we can't determine the specific type
                return DetailedMEPSystemType.Unknown;
            }
            catch (Exception)
            {
                return DetailedMEPSystemType.Unknown;
            }
        }

        /// <summary>
        /// Classifies a pipe element into a detailed system type
        /// </summary>
        private static DetailedMEPSystemType ClassifyPipeSystem(Pipe pipe)
        {
            try
            {
                MEPSystem system = pipe.MEPSystem;

                if (system == null)
                    return DetailedMEPSystemType.Unknown;

                // Check system name for common patterns
                string systemName = system.Name.ToLowerInvariant();

                if (systemName.Contains("cold") && (systemName.Contains("water") || systemName.Contains("cw")))
                    return DetailedMEPSystemType.DomesticColdWater;

                if (systemName.Contains("hot") && (systemName.Contains("water") || systemName.Contains("hw")))
                    return DetailedMEPSystemType.DomesticHotWater;

                if (systemName.Contains("sanitary") || systemName.Contains("waste") || systemName.Contains("san"))
                    return DetailedMEPSystemType.SanitarySewer;

                if (systemName.Contains("storm") || systemName.Contains("rain") || systemName.Contains("roof drain"))
                    return DetailedMEPSystemType.StormDrainage;

                if (systemName.Contains("gas") || systemName.Contains("fuel"))
                    return DetailedMEPSystemType.NaturalGas;

                if (systemName.Contains("fire") || systemName.Contains("sprinkler") || systemName.Contains("fpr"))
                    return DetailedMEPSystemType.FireProtection;

                if (systemName.Contains("medical") || systemName.Contains("oxygen") || systemName.Contains("med"))
                    return DetailedMEPSystemType.MedicalGas;                // Try to get system type information from the PipingSystem if available
                if (system is PipingSystem pipingSystem)
                {
                    // Get system type name instead of using enum values that may not exist
                    string systemTypeName = pipingSystem.SystemType.ToString().ToLowerInvariant();

                    if (systemTypeName.Contains("domesticcoldwater") || systemTypeName.Contains("cold"))
                        return DetailedMEPSystemType.DomesticColdWater;

                    if (systemTypeName.Contains("domestichotwater") || systemTypeName.Contains("hot"))
                        return DetailedMEPSystemType.DomesticHotWater;

                    if (systemTypeName.Contains("sanitary") || systemTypeName.Contains("waste"))
                        return DetailedMEPSystemType.SanitarySewer;

                    if (systemTypeName.Contains("storm") || systemTypeName.Contains("rain"))
                        return DetailedMEPSystemType.StormDrainage;

                    // For other types, try to determine from pipe type
                    PipeType pipeType = pipe.Document.GetElement(pipe.GetTypeId()) as PipeType;
                    if (pipeType != null)
                    {
                        string typeName = pipeType.Name.ToLowerInvariant();
                        if (typeName.Contains("gas"))
                            return DetailedMEPSystemType.NaturalGas;
                        else if (typeName.Contains("fire") || typeName.Contains("sprinkler"))
                            return DetailedMEPSystemType.FireProtection;
                        else if (typeName.Contains("medical") || typeName.Contains("med gas"))
                            return DetailedMEPSystemType.MedicalGas;
                    }
                }

                return DetailedMEPSystemType.Unknown;
            }
            catch (Exception)
            {
                return DetailedMEPSystemType.Unknown;
            }
        }

        /// <summary>
        /// Classifies an electrical element into a detailed system type
        /// </summary>
        private static DetailedMEPSystemType ClassifyElectricalSystem(Element element)
        {
            try
            {
                // Try to get electrical system type from element parameters
                Parameter systemParam = element.get_Parameter(BuiltInParameter.RBS_ELEC_CIRCUIT_TYPE);
                if (systemParam != null && systemParam.HasValue)
                {
                    string systemValue = systemParam.AsString().ToLowerInvariant();

                    if (systemValue.Contains("power") || systemValue.Contains("circuit"))
                        return DetailedMEPSystemType.PowerDistribution;

                    if (systemValue.Contains("light"))
                        return DetailedMEPSystemType.Lighting;

                    if (systemValue.Contains("data") || systemValue.Contains("comm") || systemValue.Contains("network"))
                        return DetailedMEPSystemType.DataComm;

                    if (systemValue.Contains("fire") || systemValue.Contains("alarm"))
                        return DetailedMEPSystemType.FireAlarm;

                    if (systemValue.Contains("security") || systemValue.Contains("access"))
                        return DetailedMEPSystemType.SecuritySystem;

                    if (systemValue.Contains("emergency") || systemValue.Contains("backup"))
                        return DetailedMEPSystemType.EmergencyPower;
                }

                // Check element category and family name for clues
                Category category = element.Category;
                if (category != null)
                {
                    string categoryName = category.Name.ToLowerInvariant();

                    if (categoryName.Contains("light"))
                        return DetailedMEPSystemType.Lighting;

                    if (categoryName.Contains("data") || categoryName.Contains("comm"))
                        return DetailedMEPSystemType.DataComm;

                    if (categoryName.Contains("fire") || categoryName.Contains("alarm"))
                        return DetailedMEPSystemType.FireAlarm;

                    if (categoryName.Contains("security"))
                        return DetailedMEPSystemType.SecuritySystem;

                    if (categoryName.Contains("emergency") || categoryName.Contains("generator"))
                        return DetailedMEPSystemType.EmergencyPower;
                }

                // Check element name for clues
                string elementName = element.Name.ToLowerInvariant();

                if (elementName.Contains("panel") || elementName.Contains("switch") || elementName.Contains("transformer"))
                    return DetailedMEPSystemType.PowerDistribution;

                if (elementName.Contains("light") || elementName.Contains("fixture"))
                    return DetailedMEPSystemType.Lighting;

                if (elementName.Contains("data") || elementName.Contains("comm") || elementName.Contains("rack"))
                    return DetailedMEPSystemType.DataComm;

                if (elementName.Contains("fire") || elementName.Contains("alarm"))
                    return DetailedMEPSystemType.FireAlarm;

                if (elementName.Contains("security") || elementName.Contains("camera") || elementName.Contains("access"))
                    return DetailedMEPSystemType.SecuritySystem;

                if (elementName.Contains("emergency") || elementName.Contains("generator") || elementName.Contains("ups"))
                    return DetailedMEPSystemType.EmergencyPower;

                // Default
                return DetailedMEPSystemType.PowerDistribution;
            }
            catch (Exception)
            {
                return DetailedMEPSystemType.Unknown;
            }
        }

        /// <summary>
        /// Gets the critical priority level for a specific system type (for clash resolution)
        /// </summary>
        public static int GetSystemPriority(DetailedMEPSystemType systemType)
        {
            switch (systemType)
            {
                // Highest priority (critical systems)
                case DetailedMEPSystemType.FireProtection:
                case DetailedMEPSystemType.FireAlarm:
                case DetailedMEPSystemType.EmergencyPower:
                    return 10;

                // Medical systems (high priority)
                case DetailedMEPSystemType.MedicalGas:
                    return 9;

                // Primary mechanical systems
                case DetailedMEPSystemType.SupplyAir:
                case DetailedMEPSystemType.ReturnAir:
                    return 8;

                // Critical plumbing systems
                case DetailedMEPSystemType.SanitarySewer:
                case DetailedMEPSystemType.DomesticColdWater:
                    return 7;

                // Secondary mechanical systems
                case DetailedMEPSystemType.ExhaustAir:
                case DetailedMEPSystemType.OutdoorAir:
                    return 6;

                // Standard plumbing systems
                case DetailedMEPSystemType.DomesticHotWater:
                case DetailedMEPSystemType.StormDrainage:
                case DetailedMEPSystemType.NaturalGas:
                    return 5;

                // Primary electrical
                case DetailedMEPSystemType.PowerDistribution:
                    return 4;

                // Secondary electrical
                case DetailedMEPSystemType.Lighting:
                    return 3;

                // Communications
                case DetailedMEPSystemType.DataComm:
                case DetailedMEPSystemType.SecuritySystem:
                    return 2;

                // Misc
                case DetailedMEPSystemType.MixedAir:
                    return 1;

                // Unknown type
                case DetailedMEPSystemType.Unknown:
                default:
                    return 0;
            }
        }

        /// <summary>
        /// Gets the recommended minimum clearance distances for a system type in millimeters
        /// </summary>
        public static double GetRecommendedClearance(DetailedMEPSystemType systemType)
        {
            switch (systemType)
            {
                // Fire protection needs good access
                case DetailedMEPSystemType.FireProtection:
                    return 450.0;

                // These systems need maintenance access
                case DetailedMEPSystemType.SupplyAir:
                case DetailedMEPSystemType.ReturnAir:
                case DetailedMEPSystemType.DomesticColdWater:
                case DetailedMEPSystemType.DomesticHotWater:
                    return 300.0;

                // Standard clearance for most systems
                case DetailedMEPSystemType.ExhaustAir:
                case DetailedMEPSystemType.OutdoorAir:
                case DetailedMEPSystemType.SanitarySewer:
                case DetailedMEPSystemType.StormDrainage:
                case DetailedMEPSystemType.PowerDistribution:
                case DetailedMEPSystemType.EmergencyPower:
                    return 200.0;

                // Smaller systems need less clearance
                case DetailedMEPSystemType.NaturalGas:
                case DetailedMEPSystemType.MedicalGas:
                case DetailedMEPSystemType.Lighting:
                case DetailedMEPSystemType.DataComm:
                case DetailedMEPSystemType.FireAlarm:
                case DetailedMEPSystemType.SecuritySystem:
                    return 150.0;

                // Default
                case DetailedMEPSystemType.MixedAir:
                case DetailedMEPSystemType.Unknown:
                default:
                    return 100.0;
            }
        }
    }
}
