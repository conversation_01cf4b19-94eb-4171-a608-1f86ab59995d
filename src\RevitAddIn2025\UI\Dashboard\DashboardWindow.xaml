<Window x:Class="RevitAddIn2025.UI.Dashboard.DashboardWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="RevitAddIn2025 Dashboard"
        Height="720" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="#F2F2F7">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock FontSize="24" FontWeight="SemiBold" Text="RevitAddIn2025 Dashboard" Margin="0,0,0,8"/>
            <TextBlock FontSize="14" Foreground="Gray" Text="Welcome to RevitAddIn2025. This dashboard provides access to all features and tools."/>
        </StackPanel>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <!-- Content Cards -->
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <Grid Margin="0,8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Project Card -->
                    <Border Grid.Column="0" Grid.Row="0" Background="White" CornerRadius="12" Margin="0,0,8,8" Padding="16">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                        </Border.Effect>
                        <StackPanel>
                            <TextBlock Text="Project Information" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>
                            <TextBlock x:Name="ProjectNameText" Text="Project Name: [Not Available]" Margin="0,4"/>
                            <TextBlock x:Name="ProjectNumberText" Text="Project Number: [Not Available]" Margin="0,4"/>
                            <TextBlock x:Name="ProjectAddressText" Text="Project Address: [Not Available]" Margin="0,4"/>
                            <TextBlock x:Name="ProjectStatusText" Text="Project Status: [Not Available]" Margin="0,4"/>
                            <Button Content="View Project Details" Background="#007AFF" Foreground="White"
                                    Padding="12,6" Margin="0,12,0,0" BorderThickness="0"
                                    HorizontalAlignment="Left" Click="ViewProjectDetails_Click"/>
                        </StackPanel>
                    </Border>

                    <!-- Elements Card -->
                    <Border Grid.Column="1" Grid.Row="0" Background="White" CornerRadius="12" Margin="8,0,8,8" Padding="16">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                        </Border.Effect>
                        <StackPanel>
                            <TextBlock Text="Element Summary" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>
                            <TextBlock x:Name="WallsCountText" Text="Walls: 0" Margin="0,4"/>
                            <TextBlock x:Name="DoorsCountText" Text="Doors: 0" Margin="0,4"/>
                            <TextBlock x:Name="WindowsCountText" Text="Windows: 0" Margin="0,4"/>
                            <TextBlock x:Name="RoomsCountText" Text="Rooms: 0" Margin="0,4"/>
                            <Button Content="Analyze Elements" Background="#007AFF" Foreground="White"
                                    Padding="12,6" Margin="0,12,0,0" BorderThickness="0"
                                    HorizontalAlignment="Left" Click="AnalyzeElements_Click"/>
                        </StackPanel>
                    </Border>

                    <!-- Recent Activity Card -->
                    <Border Grid.Column="2" Grid.Row="0" Background="White" CornerRadius="12" Margin="8,0,0,8" Padding="16">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                        </Border.Effect>
                        <StackPanel>
                            <TextBlock Text="Recent Activity" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>
                            <ListView x:Name="RecentActivityList" BorderThickness="0" Background="Transparent" Height="100">
                                <ListViewItem>Dashboard opened</ListViewItem>
                                <ListViewItem>Project loaded</ListViewItem>
                            </ListView>
                            <Button Content="View All Activity" Background="#007AFF" Foreground="White"
                                    Padding="12,6" Margin="0,12,0,0" BorderThickness="0"
                                    HorizontalAlignment="Left" Click="ViewAllActivity_Click"/>
                        </StackPanel>
                    </Border>

                    <!-- Statistics Card -->
                    <Border Grid.Column="0" Grid.ColumnSpan="3" Grid.Row="1" Background="White" CornerRadius="12" Margin="0,8,0,0" Padding="16">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.2"/>
                        </Border.Effect>
                        <StackPanel>
                            <TextBlock Text="Project Statistics" FontSize="16" FontWeight="SemiBold" Margin="0,0,0,12"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <Border Grid.Column="0" Background="#007AFF" CornerRadius="8" Margin="0,0,4,0" Padding="12">
                                    <StackPanel>
                                        <TextBlock Text="Total Elements" Foreground="White" FontWeight="Medium"/>
                                        <TextBlock x:Name="TotalElementsText" Text="0" FontSize="20" FontWeight="SemiBold" Foreground="White"/>
                                    </StackPanel>
                                </Border>

                                <Border Grid.Column="1" Background="#5856D6" CornerRadius="8" Margin="4,0,4,0" Padding="12">
                                    <StackPanel>
                                        <TextBlock Text="File Size" Foreground="White" FontWeight="Medium"/>
                                        <TextBlock x:Name="FileSizeText" Text="0 MB" FontSize="20" FontWeight="SemiBold" Foreground="White"/>
                                    </StackPanel>
                                </Border>

                                <Border Grid.Column="2" Background="#FF2D55" CornerRadius="8" Margin="4,0,4,0" Padding="12">
                                    <StackPanel>
                                        <TextBlock Text="Last Save" Foreground="White" FontWeight="Medium"/>
                                        <TextBlock x:Name="LastSaveText" Text="Unknown" FontSize="20" FontWeight="SemiBold" Foreground="White"/>
                                    </StackPanel>
                                </Border>

                                <Border Grid.Column="3" Background="#34C759" CornerRadius="8" Margin="4,0,0,0" Padding="12">
                                    <StackPanel>
                                        <TextBlock Text="Worksets" Foreground="White" FontWeight="Medium"/>
                                        <TextBlock x:Name="WorksetsText" Text="0" FontSize="20" FontWeight="SemiBold" Foreground="White"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </Border>
                </Grid>
            </ScrollViewer>
        </Grid>

        <!-- Footer -->
        <Grid Grid.Row="2" Margin="0,20,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <Ellipse x:Name="CacheStatusDot" Width="8" Height="8" Fill="Gray" Margin="0,0,6,0" VerticalAlignment="Center"/>
                <TextBlock x:Name="CacheStatusText" Text="Data Status" FontSize="12" Foreground="Gray" VerticalAlignment="Center"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="Settings" Background="Transparent" Foreground="#007AFF" BorderBrush="#007AFF" BorderThickness="1"
                        Padding="12,6" Margin="0,0,8,0" Click="OpenSettings_Click"/>
                <Button Content="Generate Report" Background="Transparent" Foreground="#007AFF" BorderBrush="#007AFF" BorderThickness="1"
                        Padding="12,6" Margin="0,0,8,0" Click="GenerateReport_Click"/>
                <Button Content="Refresh Data" Background="Transparent" Foreground="#007AFF" BorderBrush="#007AFF" BorderThickness="1"
                        Padding="12,6" Margin="0,0,8,0" Click="RefreshData_Click"/>
                <Button Content="Close" Background="#007AFF" Foreground="White" BorderThickness="0"
                        Padding="12,6" Click="Close_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</Window>
