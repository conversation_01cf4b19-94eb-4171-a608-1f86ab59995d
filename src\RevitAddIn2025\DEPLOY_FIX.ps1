# DEPLOY FIX - RevitAddIn2025
# This script fixes the UI loading issues

$ErrorActionPreference = "Stop"

# Configuration
$scriptDir = $PSScriptRoot
$projectRoot = Split-Path -Parent $scriptDir
$buildOutput = Join-Path $projectRoot "bin\Release"
$revitAddinsFolder = "$env:APPDATA\Autodesk\Revit\Addins\2025"
$revitAddinFolder = Join-Path $revitAddinsFolder "RevitAddIn2025"
$revitAddinFile = Join-Path $revitAddinsFolder "RevitAddIn2025.addin"

Write-Host ""
Write-Host "DEPLOY FIX - RevitAddIn2025" -ForegroundColor Magenta
Write-Host "============================" -ForegroundColor Magenta

# Step 1: Verify build output exists
Write-Host ""
Write-Host "Step 1: Verifying build output" -ForegroundColor Cyan
$dllPath = Join-Path $buildOutput "RevitAddIn2025.dll"

if (-not (Test-Path $dllPath)) {
    Write-Host "DLL not found, building project..." -ForegroundColor Red
    $projectFile = Join-Path $scriptDir "RevitAddIn2025.csproj"
    & dotnet build $projectFile -c Release -p:Platform=x64 --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build failed!" -ForegroundColor Red
        exit 1
    }
}

if (Test-Path $dllPath) {
    $dllInfo = Get-Item $dllPath
    $sizeKB = [math]::Round($dllInfo.Length / 1024, 2)
    Write-Host "SUCCESS: DLL found - Size: $sizeKB KB" -ForegroundColor Green
}
else {
    Write-Host "ERROR: DLL still not found!" -ForegroundColor Red
    exit 1
}

# Step 2: Clean existing deployment
Write-Host ""
Write-Host "Step 2: Cleaning existing deployment" -ForegroundColor Cyan
if (Test-Path $revitAddinFolder) {
    Remove-Item -Path $revitAddinFolder -Recurse -Force
    Write-Host "SUCCESS: Cleaned deployment folder" -ForegroundColor Green
}

if (Test-Path $revitAddinFile) {
    Remove-Item -Path $revitAddinFile -Force
    Write-Host "SUCCESS: Removed old .addin file" -ForegroundColor Green
}

# Step 3: Create deployment directory
Write-Host ""
Write-Host "Step 3: Creating deployment directory" -ForegroundColor Cyan
New-Item -ItemType Directory -Path $revitAddinFolder -Force | Out-Null
Write-Host "SUCCESS: Created deployment directory" -ForegroundColor Green

# Step 4: Copy DLL and dependencies
Write-Host ""
Write-Host "Step 4: Copying files" -ForegroundColor Cyan
Copy-Item -Path $dllPath -Destination $revitAddinFolder -Force
Write-Host "SUCCESS: Copied main DLL" -ForegroundColor Green

# Copy PDB if exists
$pdbPath = Join-Path $buildOutput "RevitAddIn2025.pdb"
if (Test-Path $pdbPath) {
    Copy-Item -Path $pdbPath -Destination $revitAddinFolder -Force
    Write-Host "SUCCESS: Copied PDB file" -ForegroundColor Green
}

# Step 5: Create correct .addin file with absolute path
Write-Host ""
Write-Host "Step 5: Creating .addin file with absolute path" -ForegroundColor Cyan
$absoluteDllPath = Join-Path $revitAddinFolder "RevitAddIn2025.dll"

$addinContent = @"
<?xml version="1.0" encoding="utf-8"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>RevitAddIn2025</Name>
    <Assembly>$absoluteDllPath</Assembly>
    <AddInId>B8399B5E-9B39-42E9-9B1D-869C8F59E76E</AddInId>
    <FullClassName>RevitAddIn2025.App.RevitApplication</FullClassName>
    <VendorId>REVIT2025</VendorId>
    <VendorDescription>Revit 2025 Add-in with Apple-inspired UI</VendorDescription>
  </AddIn>
</RevitAddIns>
"@

Set-Content -Path $revitAddinFile -Value $addinContent -Encoding UTF8
Write-Host "SUCCESS: Created .addin file with absolute path" -ForegroundColor Green
Write-Host "Assembly path: $absoluteDllPath" -ForegroundColor Gray

# Step 6: Verify deployment
Write-Host ""
Write-Host "Step 6: Verifying deployment" -ForegroundColor Cyan
$deployedDll = Join-Path $revitAddinFolder "RevitAddIn2025.dll"
if (Test-Path $deployedDll) {
    Write-Host "SUCCESS: Deployed DLL verified" -ForegroundColor Green
}
else {
    Write-Host "ERROR: Deployed DLL not found!" -ForegroundColor Red
    exit 1
}

if (Test-Path $revitAddinFile) {
    Write-Host "SUCCESS: .addin file verified" -ForegroundColor Green
}
else {
    Write-Host "ERROR: .addin file not found!" -ForegroundColor Red
    exit 1
}

# Final summary
Write-Host ""
Write-Host "DEPLOYMENT SUMMARY:" -ForegroundColor Green
Write-Host "SUCCESS: DLL deployed to: $deployedDll" -ForegroundColor Green
Write-Host "SUCCESS: .addin file created: $revitAddinFile" -ForegroundColor Green
Write-Host "SUCCESS: Assembly path uses ABSOLUTE path (FIXED)" -ForegroundColor Green

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Close Revit completely if it's running" -ForegroundColor White
Write-Host "2. Start Revit 2025" -ForegroundColor White
Write-Host "3. Look for startup dialogs from the plugin" -ForegroundColor White
Write-Host "4. Check for 'RevitAddIn2025' tab in the ribbon" -ForegroundColor White
Write-Host "5. Test the ribbon buttons" -ForegroundColor White

Write-Host ""
Write-Host "DEPLOYMENT COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "The .addin file now uses an absolute path which should fix the 'External Tool Failure' error." -ForegroundColor Yellow
