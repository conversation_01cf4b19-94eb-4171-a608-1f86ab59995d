using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Effects;
using Autodesk.Revit.UI;
using RevitAddIn2025.Commands;
using RevitAddIn2025.Models;
using RevitAddIn2025.Utilities;
using RevitAddIn2025.AI.Transformer;
using OptimizationIssue = RevitAddIn2025.Models.OptimizationIssue;
using OptimizationRecommendation = RevitAddIn2025.Models.OptimizationRecommendation;
using OptimizationSeverity = RevitAddIn2025.Models.OptimizationSeverity;
using OptimizationPriority = RevitAddIn2025.Models.OptimizationPriority;
using WinGrid = System.Windows.Controls.Grid;
using WinColor = System.Windows.Media.Color;

namespace RevitAddIn2025.UI
{
    /// <summary>
    /// Advanced MEP AI Transformer Dialog with real-time analysis
    /// </summary>
    public class MEPTransformerDialog : Window
    {
        private readonly UIApplication _uiApp;
        private readonly RevitTransformerMEPPlugin _transformerPlugin;
        private RevitAddIn2025.AI.Transformer.MEPOptimizationResult _currentResults;

        // UI Elements
        private TextBlock _statusText;
        private ProgressBar _progressBar;
        private ListBox _issuesList;
        private ListBox _recommendationsList;
        private TextBlock _energyScoreText;
        private TextBlock _complianceScoreText;
        private TextBlock _optimizationScoreText;
        private Button _analyzeButton;
        private Button _exportButton;
        private TabControl _tabControl;

        public MEPTransformerDialog(UIApplication uiApp, RevitTransformerMEPPlugin transformerPlugin)
        {
            _uiApp = uiApp ?? throw new ArgumentNullException(nameof(uiApp));
            _transformerPlugin = transformerPlugin ?? throw new ArgumentNullException(nameof(transformerPlugin));

            InitializeComponent();
            Logger.Info("MEP Transformer Dialog initialized");
        }

        private void InitializeComponent()
        {
            // Window properties
            Title = "MEP AI Transformer - Advanced Analysis";
            Width = 1000;
            Height = 700;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Background = new SolidColorBrush(WinColor.FromRgb(242, 242, 247));

            // Main container
            var mainGrid = new WinGrid { Margin = new Thickness(20) };
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Header
            CreateHeader(mainGrid);

            // Control Panel
            CreateControlPanel(mainGrid);

            // Main Content
            CreateMainContent(mainGrid);

            // Footer
            CreateFooter(mainGrid);

            Content = mainGrid;
        }

        private void CreateHeader(WinGrid parent)
        {
            var headerStack = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };

            var titleText = new TextBlock
            {
                Text = "🧠 MEP AI Transformer",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(WinColor.FromRgb(0, 122, 255)),
                Margin = new Thickness(0, 0, 0, 8)
            };
            headerStack.Children.Add(titleText);

            var subtitleText = new TextBlock
            {
                Text = "AI-powered MEP coordination, clash detection, and optimization",
                FontSize = 16,
                Foreground = Brushes.Gray
            };
            headerStack.Children.Add(subtitleText);

            WinGrid.SetRow(headerStack, 0);
            parent.Children.Add(headerStack);
        }

        private void CreateControlPanel(WinGrid parent)
        {
            var controlCard = CreateCard("Analysis Control");
            var controlStack = (StackPanel)controlCard.Child;

            // Status and progress
            _statusText = new TextBlock
            {
                Text = "Ready to analyze MEP systems",
                FontSize = 14,
                Margin = new Thickness(0, 0, 0, 10)
            };
            controlStack.Children.Add(_statusText);

            _progressBar = new ProgressBar
            {
                Height = 8,
                Margin = new Thickness(0, 0, 0, 15),
                Background = Brushes.LightGray,
                Foreground = new SolidColorBrush(WinColor.FromRgb(0, 122, 255))
            };
            controlStack.Children.Add(_progressBar);

            // Control buttons
            var buttonStack = new StackPanel { Orientation = Orientation.Horizontal };

            _analyzeButton = CreateAppleButton("🔍 Start AI Analysis", WinColor.FromRgb(0, 122, 255));
            _analyzeButton.Click += AnalyzeButton_Click;
            _analyzeButton.Margin = new Thickness(0, 0, 10, 0);
            buttonStack.Children.Add(_analyzeButton);

            _exportButton = CreateAppleButton("📊 Export Results", WinColor.FromRgb(52, 199, 89));
            _exportButton.Click += ExportButton_Click;
            _exportButton.IsEnabled = false;
            buttonStack.Children.Add(_exportButton);

            controlStack.Children.Add(buttonStack);

            WinGrid.SetRow(controlCard, 1);
            parent.Children.Add(controlCard);
        }

        private void CreateMainContent(WinGrid parent)
        {
            _tabControl = new TabControl
            {
                Margin = new Thickness(0, 20, 0, 0),
                Background = Brushes.Transparent,
                BorderThickness = new Thickness(0)
            };

            // Overview Tab
            CreateOverviewTab();

            // Issues Tab
            CreateIssuesTab();

            // Recommendations Tab
            CreateRecommendationsTab();

            WinGrid.SetRow(_tabControl, 2);
            parent.Children.Add(_tabControl);
        }

        private void CreateOverviewTab()
        {
            var tab = new TabItem { Header = "📊 Overview" };
            var scrollViewer = new ScrollViewer { VerticalScrollBarVisibility = ScrollBarVisibility.Auto };
            var contentStack = new StackPanel();

            // Score cards
            var scoresGrid = new WinGrid();
            scoresGrid.ColumnDefinitions.Add(new ColumnDefinition());
            scoresGrid.ColumnDefinitions.Add(new ColumnDefinition());
            scoresGrid.ColumnDefinitions.Add(new ColumnDefinition());

            // Energy Score Card
            var energyCard = CreateScoreCard("⚡ Energy Efficiency", "85%", WinColor.FromRgb(52, 199, 89));
            _energyScoreText = ((StackPanel)energyCard.Child).Children.OfType<TextBlock>().Last();
            WinGrid.SetColumn(energyCard, 0);
            scoresGrid.Children.Add(energyCard);

            // Compliance Score Card
            var complianceCard = CreateScoreCard("📋 Code Compliance", "92%", WinColor.FromRgb(0, 122, 255));
            _complianceScoreText = ((StackPanel)complianceCard.Child).Children.OfType<TextBlock>().Last();
            WinGrid.SetColumn(complianceCard, 1);
            scoresGrid.Children.Add(complianceCard);

            // Optimization Score Card
            var optimizationCard = CreateScoreCard("🎯 Optimization", "78%", WinColor.FromRgb(255, 149, 0));
            _optimizationScoreText = ((StackPanel)optimizationCard.Child).Children.OfType<TextBlock>().Last();
            WinGrid.SetColumn(optimizationCard, 2);
            scoresGrid.Children.Add(optimizationCard);

            contentStack.Children.Add(scoresGrid);

            // Analysis Summary
            var summaryCard = CreateCard("Analysis Summary");
            var summaryStack = (StackPanel)summaryCard.Child;

            var summaryText = new TextBlock
            {
                Text = "Click 'Start AI Analysis' to begin comprehensive MEP system analysis.\n\n" +
                       "The AI transformer will analyze:\n" +
                       "• Clash detection and resolution\n" +
                       "• Energy efficiency optimization\n" +
                       "• Code compliance verification\n" +
                       "• System performance optimization",
                FontSize = 14,
                TextWrapping = TextWrapping.Wrap,
                LineHeight = 20
            };
            summaryStack.Children.Add(summaryText);

            summaryCard.Margin = new Thickness(0, 20, 0, 0);
            contentStack.Children.Add(summaryCard);

            scrollViewer.Content = contentStack;
            tab.Content = scrollViewer;
            _tabControl.Items.Add(tab);
        }

        private void CreateIssuesTab()
        {
            var tab = new TabItem { Header = "⚠️ Issues" };
            var card = CreateCard("Detected Issues");
            var cardStack = (StackPanel)card.Child;

            _issuesList = new ListBox
            {
                Background = Brushes.Transparent,
                BorderThickness = new Thickness(0),
                MinHeight = 300
            };

            // Add placeholder item
            var placeholderItem = new ListBoxItem
            {
                Content = "No issues detected yet. Run analysis to identify potential problems.",
                FontStyle = FontStyles.Italic,
                Foreground = Brushes.Gray
            };
            _issuesList.Items.Add(placeholderItem);

            cardStack.Children.Add(_issuesList);
            tab.Content = card;
            _tabControl.Items.Add(tab);
        }

        private void CreateRecommendationsTab()
        {
            var tab = new TabItem { Header = "💡 Recommendations" };
            var card = CreateCard("AI Recommendations");
            var cardStack = (StackPanel)card.Child;

            _recommendationsList = new ListBox
            {
                Background = Brushes.Transparent,
                BorderThickness = new Thickness(0),
                MinHeight = 300
            };

            // Add placeholder item
            var placeholderItem = new ListBoxItem
            {
                Content = "No recommendations available yet. Run analysis to get AI-powered suggestions.",
                FontStyle = FontStyles.Italic,
                Foreground = Brushes.Gray
            };
            _recommendationsList.Items.Add(placeholderItem);

            cardStack.Children.Add(_recommendationsList);
            tab.Content = card;
            _tabControl.Items.Add(tab);
        }

        private void CreateFooter(WinGrid parent)
        {
            var footerStack = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 20, 0, 0)
            };

            var closeButton = CreateAppleButton("Close", WinColor.FromRgb(142, 142, 147));
            closeButton.Click += (s, e) => Close();
            footerStack.Children.Add(closeButton);

            WinGrid.SetRow(footerStack, 3);
            parent.Children.Add(footerStack);
        }

        private async void AnalyzeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _analyzeButton.IsEnabled = false;
                _statusText.Text = "Initializing AI transformer...";
                _progressBar.Value = 10;

                Logger.Info("Starting MEP AI analysis");

                // Run analysis
                _statusText.Text = "Analyzing MEP systems with AI...";
                _progressBar.Value = 50;

                _currentResults = await _transformerPlugin.AnalyzeMEPLayoutAsync();

                _statusText.Text = "Processing results...";
                _progressBar.Value = 80;

                // Update UI with results
                UpdateResultsDisplay();

                _statusText.Text = $"Analysis complete! Found {_currentResults.Issues.Count} issues and {_currentResults.Recommendations.Count} recommendations.";
                _progressBar.Value = 100;

                _exportButton.IsEnabled = true;
                Logger.Info("MEP AI analysis completed successfully");
            }
            catch (Exception ex)
            {
                Logger.Error("MEP analysis failed", ex);
                _statusText.Text = $"Analysis failed: {ex.Message}";
                _progressBar.Value = 0;
                MessageBox.Show($"Analysis failed: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _analyzeButton.IsEnabled = true;
            }
        }

        private void UpdateResultsDisplay()
        {
            if (_currentResults == null) return;

            // Update score displays
            _energyScoreText.Text = $"{_currentResults.OverallEnergyScore:F1}%";
            _complianceScoreText.Text = $"{_currentResults.OverallComplianceScore:F1}%";
            _optimizationScoreText.Text = $"{_currentResults.OptimizationPotential:F1}%";

            // Update issues list
            _issuesList.Items.Clear();
            foreach (var issue in _currentResults.Issues)
            {
                var issueItem = CreateIssueListItem(issue);
                _issuesList.Items.Add(issueItem);
            }

            if (_currentResults.Issues.Count == 0)
            {
                _issuesList.Items.Add(new ListBoxItem
                {
                    Content = "✅ No critical issues detected!",
                    Foreground = new SolidColorBrush(WinColor.FromRgb(52, 199, 89)),
                    FontWeight = FontWeights.SemiBold
                });
            }

            // Update recommendations list
            _recommendationsList.Items.Clear();
            foreach (var recommendation in _currentResults.Recommendations)
            {
                var recommendationItem = CreateRecommendationListItem(recommendation);
                _recommendationsList.Items.Add(recommendationItem);
            }

            if (_currentResults.Recommendations.Count == 0)
            {
                _recommendationsList.Items.Add(new ListBoxItem
                {
                    Content = "✅ System is optimally configured!",
                    Foreground = new SolidColorBrush(WinColor.FromRgb(52, 199, 89)),
                    FontWeight = FontWeights.SemiBold
                });
            }
        }

        private ListBoxItem CreateIssueListItem(OptimizationIssue issue)
        {
            var stack = new StackPanel { Margin = new Thickness(10) };

            var headerStack = new StackPanel { Orientation = Orientation.Horizontal };

            var severityIcon = new TextBlock
            {
                Text = GetSeverityIcon(issue.Severity),
                FontSize = 16,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            headerStack.Children.Add(severityIcon);

            var titleText = new TextBlock
            {
                Text = issue.Description,
                FontWeight = FontWeights.SemiBold,
                FontSize = 14
            };
            headerStack.Children.Add(titleText);

            stack.Children.Add(headerStack);

            var detailText = new TextBlock
            {
                Text = $"Type: {issue.Type} | Confidence: {issue.Confidence:P0} | Element ID: {issue.ElementId}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(24, 4, 0, 0)
            };
            stack.Children.Add(detailText);

            return new ListBoxItem { Content = stack };
        }

        private ListBoxItem CreateRecommendationListItem(OptimizationRecommendation recommendation)
        {
            var stack = new StackPanel { Margin = new Thickness(10) };

            var headerStack = new StackPanel { Orientation = Orientation.Horizontal };

            var priorityIcon = new TextBlock
            {
                Text = GetPriorityIcon(recommendation.Priority),
                FontSize = 16,
                Margin = new Thickness(0, 0, 8, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            headerStack.Children.Add(priorityIcon);

            var titleText = new TextBlock
            {
                Text = recommendation.Description,
                FontWeight = FontWeights.SemiBold,
                FontSize = 14
            };
            headerStack.Children.Add(titleText);

            stack.Children.Add(headerStack);

            var detailText = new TextBlock
            {
                Text = $"Type: {recommendation.Type} | Improvement: {recommendation.ExpectedImprovement:P0} | Cost: {recommendation.ImplementationCost}",
                FontSize = 12,
                Foreground = Brushes.Gray,
                Margin = new Thickness(24, 4, 0, 0)
            };
            stack.Children.Add(detailText);

            return new ListBoxItem { Content = stack };
        }

        private string GetSeverityIcon(OptimizationSeverity severity)
        {
            switch (severity)
            {
                case OptimizationSeverity.Critical: return "🔴";
                case OptimizationSeverity.High: return "🟠";
                case OptimizationSeverity.Medium: return "🟡";
                case OptimizationSeverity.Low: return "🟢";
                default: return "⚪";
            }
        }

        private string GetPriorityIcon(OptimizationPriority priority)
        {
            switch (priority)
            {
                case OptimizationPriority.Critical: return "🔥";
                case OptimizationPriority.High: return "⭐";
                case OptimizationPriority.Medium: return "💡";
                case OptimizationPriority.Low: return "💭";
                default: return "ℹ️";
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentResults == null)
                {
                    MessageBox.Show("No results to export. Please run analysis first.", "Export", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // TODO: Implement export functionality
                MessageBox.Show($"Export functionality will save:\n\n" +
                               $"• {_currentResults.Issues.Count} issues\n" +
                               $"• {_currentResults.Recommendations.Count} recommendations\n" +
                               $"• Analysis scores and metrics\n\n" +
                               "Export feature coming soon!", "Export Results", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Logger.Error("Export failed", ex);
                MessageBox.Show($"Export failed: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // Helper methods for UI creation
        private Border CreateCard(string title)
        {
            var card = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(20),
                Margin = new Thickness(0, 0, 0, 16),
                Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Opacity = 0.1,
                    BlurRadius = 10,
                    ShadowDepth = 2
                }
            };

            var stack = new StackPanel();

            if (!string.IsNullOrEmpty(title))
            {
                var titleText = new TextBlock
                {
                    Text = title,
                    FontSize = 18,
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 0, 0, 16)
                };
                stack.Children.Add(titleText);
            }

            card.Child = stack;
            return card;
        }

        private Border CreateScoreCard(string title, string score, WinColor color)
        {
            var card = new Border
            {
                Background = new SolidColorBrush(color),
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(20),
                Margin = new Thickness(8),
                Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Opacity = 0.2,
                    BlurRadius = 8,
                    ShadowDepth = 2
                }
            };

            var stack = new StackPanel { HorizontalAlignment = HorizontalAlignment.Center };

            var titleText = new TextBlock
            {
                Text = title,
                FontSize = 14,
                FontWeight = FontWeights.Medium,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8)
            };
            stack.Children.Add(titleText);

            var scoreText = new TextBlock
            {
                Text = score,
                FontSize = 32,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            };
            stack.Children.Add(scoreText);

            card.Child = stack;
            return card;
        }

        private Button CreateAppleButton(string text, WinColor backgroundColor)
        {
            var button = new Button
            {
                Content = text,
                Background = new SolidColorBrush(backgroundColor),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0, 0, 0, 0),
                Padding = new Thickness(20, 10, 20, 10),
                FontSize = 14,
                FontWeight = FontWeights.Medium,
                Cursor = System.Windows.Input.Cursors.Hand
            };

            // Add rounded corners
            button.Template = CreateButtonTemplate(backgroundColor);

            return button;
        }

        private ControlTemplate CreateButtonTemplate(WinColor backgroundColor)
        {
            var template = new ControlTemplate(typeof(Button));

            var border = new FrameworkElementFactory(typeof(Border));
            border.SetValue(Border.BackgroundProperty, new SolidColorBrush(backgroundColor));
            border.SetValue(Border.CornerRadiusProperty, new CornerRadius(8));
            border.SetValue(Border.PaddingProperty, new Thickness(20, 10, 20, 10));

            var contentPresenter = new FrameworkElementFactory(typeof(ContentPresenter));
            contentPresenter.SetValue(ContentPresenter.HorizontalAlignmentProperty, HorizontalAlignment.Center);
            contentPresenter.SetValue(ContentPresenter.VerticalAlignmentProperty, VerticalAlignment.Center);

            border.AppendChild(contentPresenter);
            template.VisualTree = border;

            return template;
        }
    }
}
