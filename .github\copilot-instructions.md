<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

This is a Revit 2025 add-in project using C# and .NET 8 (x64). The project features a modern, Apple-inspired dashboard UI with a contrasting color scheme, robust command structure, and best-practice ribbon integration. Prioritize error-free loading, maintainability, extensibility, and compatibility with Revit 2025 and future versions. All UI should be visually appealing and user-friendly. All commands should be implemented as separate classes and registered in the ribbon via the main application class.
