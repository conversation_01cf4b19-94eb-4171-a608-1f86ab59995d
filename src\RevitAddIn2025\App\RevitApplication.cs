using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Windows.Media.Imaging;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.Commands;
using RevitAddIn2025.Utilities;

namespace RevitAddIn2025.App
{
    /// <summary>
    /// Enhanced RevitAddIn2025 Application with Full UI and Apple-inspired Design
    /// </summary>
    public class RevitApplication : IExternalApplication
    {
        private const string TAB_NAME = "RevitAddIn2025";
        private RibbonPanel _mainPanel;
        private string AssemblyPath => Assembly.GetExecutingAssembly().Location;

        /// <summary>
        /// Startup method with comprehensive ribbon creation
        /// </summary>
        public Result OnStartup(UIControlledApplication application)
        {
            try
            {
                // Show success message
                TaskDialog.Show("RevitAddIn2025",
                    "🎉 RevitAddIn2025 loaded successfully!\n\n" +
                    "✅ Plugin is working correctly\n" +
                    "✅ Ready for development\n\n" +
                    "Version: 1.0.0");

                // Create comprehensive ribbon interface
                CreateRibbonTab(application);
                CreateRibbonPanels(application);
                CreateRibbonButtons();

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                TaskDialog.Show("RevitAddIn2025 Error",
                    $"Failed to load RevitAddIn2025.\n\nError: {ex.Message}");
                return Result.Failed;
            }
        }

        /// <summary>
        /// Creates the custom tab in the Revit ribbon
        /// </summary>
        private void CreateRibbonTab(UIControlledApplication application)
        {
            try
            {
                application.CreateRibbonTab(TAB_NAME);
            }
            catch (Exception)
            {
                // Tab may already exist
            }
        }

        /// <summary>
        /// Creates panels in the custom ribbon tab
        /// </summary>
        private void CreateRibbonPanels(UIControlledApplication application)
        {
            // Create main ribbon panel
            _mainPanel = application.CreateRibbonPanel(TAB_NAME, "Tools");
        }

        /// <summary>
        /// Creates all ribbon buttons and associates them with commands
        /// </summary>
        private void CreateRibbonButtons()
        {
            // TEST BUTTON - IMMEDIATE VERIFICATION
            PushButton testButton = CreatePushButton(
                _mainPanel,
                "🎯 TEST",
                "Test if plugin is working - Click me!",
                "Icons/test_icon.png",
                typeof(TestCommand));

            // Create Dashboard button
            PushButton dashboardButton = CreatePushButton(
                _mainPanel,
                "Dashboard",
                "Open the main dashboard window",
                "Icons/dashboard_icon.png",
                typeof(DashboardCommand));

            // Create Settings button
            PushButton settingsButton = CreatePushButton(
                _mainPanel,
                "Settings",
                "Configure application settings",
                "Icons/settings_icon.png",
                typeof(SettingsCommand));

            // Create Help button
            PushButton helpButton = CreatePushButton(
                _mainPanel,
                "Help",
                "Access help documentation",
                "Icons/help_icon.png",
                typeof(HelpCommand));

            // Create About button
            PushButton aboutButton = CreatePushButton(
                _mainPanel,
                "About",
                "About the application",
                "Icons/about_icon.png",
                typeof(AboutCommand));
        }

        /// <summary>
        /// Helper method to create a push button
        /// </summary>
        private PushButton CreatePushButton(
            RibbonPanel panel,
            string name,
            string tooltip,
            string iconPath,
            Type commandType)
        {
            PushButtonData buttonData = new PushButtonData(
                $"{name}Button",
                name,
                AssemblyPath,
                commandType.FullName)
            {
                ToolTip = tooltip,
                LargeImage = LoadIconBitmap(iconPath)
            };

            return panel.AddItem(buttonData) as PushButton;
        }

        /// <summary>
        /// Helper method to load bitmap images for icons
        /// </summary>
        private BitmapSource LoadIconBitmap(string iconName)
        {
            try
            {
                // Use the ResourceHelper to load the image
                BitmapImage image = ResourceHelper.LoadImage(iconName);

                // Return the loaded image or create a default icon if loading failed
                if (image == null)
                {
                    return CreateDefaultIcon();
                }

                return image;
            }
            catch (Exception)
            {
                return CreateDefaultIcon();
            }
        }

        /// <summary>
        /// Creates a default icon when image loading fails
        /// </summary>
        private BitmapSource CreateDefaultIcon()
        {
            try
            {
                // Create a simple 32x32 blue square as default icon
                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri("pack://application:,,,/RevitAddIn2025;component/Resources/Icons/PNG/dashboard_icon.png");
                bitmap.EndInit();
                return bitmap;
            }
            catch
            {
                // Return null if even default icon fails
                return null;
            }
        }



        /// <summary>
        /// Shutdown method called when Revit closes
        /// </summary>
        public Result OnShutdown(UIControlledApplication application)
        {
            return Result.Succeeded;
        }
    }
}
