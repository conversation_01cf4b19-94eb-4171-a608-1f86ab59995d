using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Reflection;
using System.Windows.Media.Imaging;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.Commands;
using RevitAddIn2025.Utilities;
using static RevitAddIn2025.Utilities.Logger;
using RevitAddIn2025.Development;

namespace RevitAddIn2025.App
{
    /// <summary>
    /// Enhanced RevitAddIn2025 Application with Full UI and Apple-inspired Design
    /// </summary>
    public class RevitApplication : IExternalApplication
    {
        private const string TAB_NAME = "RevitAddIn2025";
        private RibbonPanel _mainPanel;
        private string AssemblyPath => Assembly.GetExecutingAssembly().Location;

        /// <summary>
        /// Startup method with comprehensive ribbon creation
        /// </summary>
        public Result OnStartup(UIControlledApplication application)
        {
            try
            {
                // Initialize logging system first
                Logger.Initialize();
                Logger.Info("RevitAddIn2025 startup initiated");

                // CRITICAL DIAGNOSTIC: Force multiple visible indicators
                TaskDialog.Show("🚨 PLUGIN LOADING TEST 🚨",
                    "🔥 THIS DIALOG PROVES THE PLUGIN IS LOADING! 🔥\n\n" +
                    "If you see this dialog, our code IS running!\n" +
                    "If you DON'T see this dialog, Revit is not loading our plugin.\n\n" +
                    "📅 Build Time: 27-05-2025 11:30\n" +
                    "📁 DLL Size: 14848 bytes\n\n" +
                    "Click OK to continue loading the ribbon...");

                // Create comprehensive ribbon interface
                CreateRibbonTab(application);
                CreateRibbonPanels(application);
                CreateRibbonButtons();

                // Second confirmation dialog
                TaskDialog.Show("✅ RIBBON CREATED",
                    "🎯 Ribbon creation completed!\n\n" +
                    "You should now see:\n" +
                    "• RevitAddIn2025 tab in ribbon\n" +
                    "• 7 buttons with colored icons\n" +
                    "• Test, Dashboard, Settings, Help, About, MEP AI, Dev Reload\n\n" +
                    "If you don't see the ribbon, there's an issue with ribbon creation.");

                // Initialize hot reload system for development
#if DEBUG
                try
                {
                    HotReloadManager.Initialize(application, _mainPanel);
                }
                catch (Exception ex)
                {
                    // Don't fail startup if hot reload fails
                    System.Diagnostics.Debug.WriteLine($"Hot reload initialization failed: {ex.Message}");
                }
#endif

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                TaskDialog.Show("🚨 PLUGIN ERROR 🚨",
                    $"❌ CRITICAL ERROR in RevitAddIn2025!\n\n" +
                    "Error: {ex.Message}\n\n" +
                    "Stack Trace:\n{ex.StackTrace}");
                return Result.Failed;
            }
        }

        /// <summary>
        /// Creates the custom tab in the Revit ribbon
        /// </summary>
        private void CreateRibbonTab(UIControlledApplication application)
        {
            try
            {
                application.CreateRibbonTab(TAB_NAME);
            }
            catch (Exception)
            {
                // Tab may already exist
            }
        }

        /// <summary>
        /// Creates panels in the custom ribbon tab
        /// </summary>
        private void CreateRibbonPanels(UIControlledApplication application)
        {
            // Create main ribbon panel
            _mainPanel = application.CreateRibbonPanel(TAB_NAME, "Tools");
        }

        /// <summary>
        /// Creates all ribbon buttons and associates them with commands
        /// </summary>
        private void CreateRibbonButtons()
        {
            // TEST BUTTON - IMMEDIATE VERIFICATION
            PushButton testButton = CreatePushButton(
                _mainPanel,
                "🎯 TEST",
                "Test if plugin is working - Click me!",
                "Icons/test_icon.png",
                typeof(TestCommand));

            // Create Dashboard button
            PushButton dashboardButton = CreatePushButton(
                _mainPanel,
                "Dashboard",
                "Open the main dashboard window",
                "Icons/dashboard_icon.png",
                typeof(DashboardCommand));

            // Create Settings button
            PushButton settingsButton = CreatePushButton(
                _mainPanel,
                "Settings",
                "Configure application settings",
                "Icons/settings_icon.png",
                typeof(SettingsCommand));

            // Create Help button
            PushButton helpButton = CreatePushButton(
                _mainPanel,
                "Help",
                "Access help documentation",
                "Icons/help_icon.png",
                typeof(HelpCommand));

            // Create About button
            PushButton aboutButton = CreatePushButton(
                _mainPanel,
                "About",
                "About the application",
                "Icons/about_icon.png",
                typeof(AboutCommand));

            // Create MEP AI Transformer button
            PushButton mepButton = CreatePushButton(
                _mainPanel,
                "🧠 MEP AI",
                "AI-powered MEP coordination and optimization",
                "Icons/mep_icon.png",
                typeof(MEPTransformerCommand));

            // Create Development Reload button (only in debug builds)
#if DEBUG
            PushButton devReloadButton = CreatePushButton(
                _mainPanel,
                "🔄 Dev Reload",
                "Manually trigger hot reload for development",
                "Icons/reload_icon.png",
                typeof(DevReloadCommand));
#endif
        }

        /// <summary>
        /// Helper method to create a push button
        /// </summary>
        private PushButton CreatePushButton(
            RibbonPanel panel,
            string name,
            string tooltip,
            string iconPath,
            Type commandType)
        {
            PushButtonData buttonData = new PushButtonData(
                $"{name}Button",
                name,
                AssemblyPath,
                commandType.FullName)
            {
                ToolTip = tooltip,
                LargeImage = LoadIconBitmap(iconPath)
            };

            return panel.AddItem(buttonData) as PushButton;
        }

        /// <summary>
        /// Helper method to load bitmap images for icons
        /// </summary>
        private BitmapSource LoadIconBitmap(string iconName)
        {
            try
            {
                // Generate professional icons if they don't exist
                GenerateProfessionalIcons();

                // Try to load from Resources/Icons folder first
                string resourcePath = Path.Combine(Path.GetDirectoryName(AssemblyPath), "Resources", "Icons", iconName);
                if (File.Exists(resourcePath))
                {
                    return LoadImageFromFile(resourcePath);
                }

                // Fallback to creating a colored icon
                return CreateColoredIcon(iconName);
            }
            catch (Exception)
            {
                return CreateDefaultIcon();
            }
        }

        /// <summary>
        /// Generate professional Apple-inspired icons
        /// </summary>
        private void GenerateProfessionalIcons()
        {
            try
            {
                string iconsPath = Path.Combine(Path.GetDirectoryName(AssemblyPath), "Resources", "Icons");
                Directory.CreateDirectory(iconsPath);

                // Only generate if icons don't exist
                if (!File.Exists(Path.Combine(iconsPath, "dashboard_icon.png")))
                {
                    RevitAddIn2025.Resources.Icons.IconGenerator.GenerateAllIcons();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Icon generation failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Load image from file path
        /// </summary>
        private BitmapSource LoadImageFromFile(string filePath)
        {
            try
            {
                var bitmap = new System.Drawing.Bitmap(filePath);
                return System.Windows.Interop.Imaging.CreateBitmapSourceFromHBitmap(
                    bitmap.GetHbitmap(),
                    IntPtr.Zero,
                    System.Windows.Int32Rect.Empty,
                    BitmapSizeOptions.FromEmptyOptions());
            }
            catch
            {
                return CreateDefaultIcon();
            }
        }

        /// <summary>
        /// Creates a colored icon based on the icon name
        /// </summary>
        private BitmapSource CreateColoredIcon(string iconName)
        {
            try
            {
                // Create a 32x32 bitmap with different colors for different icons
                var bitmap = new System.Drawing.Bitmap(32, 32);
                using (var graphics = System.Drawing.Graphics.FromImage(bitmap))
                {
                    // Choose color based on icon name
                    System.Drawing.Color color = GetIconColor(iconName);

                    // Fill with solid color
                    graphics.FillRectangle(new System.Drawing.SolidBrush(color), 0, 0, 32, 32);

                    // Add a border
                    graphics.DrawRectangle(new System.Drawing.Pen(System.Drawing.Color.White, 2), 1, 1, 30, 30);
                }

                // Convert to BitmapSource
                return System.Windows.Interop.Imaging.CreateBitmapSourceFromHBitmap(
                    bitmap.GetHbitmap(),
                    IntPtr.Zero,
                    System.Windows.Int32Rect.Empty,
                    BitmapSizeOptions.FromEmptyOptions());
            }
            catch
            {
                return CreateDefaultIcon();
            }
        }

        /// <summary>
        /// Gets a color for an icon based on its name
        /// </summary>
        private System.Drawing.Color GetIconColor(string iconName)
        {
            if (iconName.Contains("test")) return System.Drawing.Color.Green;
            if (iconName.Contains("dashboard")) return System.Drawing.Color.Blue;
            if (iconName.Contains("settings")) return System.Drawing.Color.Gray;
            if (iconName.Contains("help")) return System.Drawing.Color.Orange;
            if (iconName.Contains("about")) return System.Drawing.Color.Purple;
            if (iconName.Contains("mep")) return System.Drawing.Color.Red;
            if (iconName.Contains("reload")) return System.Drawing.Color.Cyan;
            return System.Drawing.Color.DarkBlue; // Default
        }

        /// <summary>
        /// Creates a default icon when image loading fails
        /// </summary>
        private BitmapSource CreateDefaultIcon()
        {
            try
            {
                // Create a simple 32x32 blue square as default icon
                var bitmap = new System.Drawing.Bitmap(32, 32);
                using (var graphics = System.Drawing.Graphics.FromImage(bitmap))
                {
                    graphics.FillRectangle(new System.Drawing.SolidBrush(System.Drawing.Color.DarkBlue), 0, 0, 32, 32);
                    graphics.DrawRectangle(new System.Drawing.Pen(System.Drawing.Color.White, 2), 1, 1, 30, 30);
                }

                return System.Windows.Interop.Imaging.CreateBitmapSourceFromHBitmap(
                    bitmap.GetHbitmap(),
                    IntPtr.Zero,
                    System.Windows.Int32Rect.Empty,
                    BitmapSizeOptions.FromEmptyOptions());
            }
            catch
            {
                // Return null if even default icon fails
                return null;
            }
        }



        /// <summary>
        /// Shutdown method called when Revit closes
        /// </summary>
        public Result OnShutdown(UIControlledApplication application)
        {
            // Clean up hot reload system
#if DEBUG
            try
            {
                HotReloadManager.Stop();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Hot reload cleanup failed: {ex.Message}");
            }
#endif

            return Result.Succeeded;
        }
    }
}
