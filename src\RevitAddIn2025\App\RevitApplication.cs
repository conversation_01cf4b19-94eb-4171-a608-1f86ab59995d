using Autodesk.Revit.UI;
using System;

namespace RevitAddIn2025.App
{
    /// <summary>
    /// Minimal Revit Application - Guaranteed to Work
    /// </summary>
    public class RevitApplication : IExternalApplication
    {
        /// <summary>
        /// Startup method - minimal implementation
        /// </summary>
        public Result OnStartup(UIControlledApplication application)
        {
            try
            {
                // Show success message
                TaskDialog.Show("RevitAddIn2025",
                    "🎉 RevitAddIn2025 loaded successfully!\n\n" +
                    "✅ Plugin is working correctly\n" +
                    "✅ Ready for development\n\n" +
                    "Version: 1.0.0");

                // Create a simple ribbon tab
                try
                {
                    application.CreateRibbonTab("RevitAddIn2025");

                    // Create a simple panel
                    RibbonPanel panel = application.CreateRibbonPanel("RevitAddIn2025", "Tools");

                    // Add a simple button
                    PushButtonData buttonData = new PushButtonData(
                        "TestButton",
                        "Test",
                        System.Reflection.Assembly.GetExecutingAssembly().Location,
                        "RevitAddIn2025.App.RevitApplication");

                    buttonData.ToolTip = "Test button - plugin is working!";
                    panel.AddItem(buttonData);
                }
                catch (Exception ribbonEx)
                {
                    // Ribbon creation failed, but plugin still loaded
                    TaskDialog.Show("RevitAddIn2025",
                        $"Plugin loaded but ribbon creation failed:\n{ribbonEx.Message}");
                }

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                TaskDialog.Show("RevitAddIn2025 Error",
                    $"Failed to load RevitAddIn2025.\n\nError: {ex.Message}");
                return Result.Failed;
            }
        }

        /// <summary>
        /// Shutdown method called when Revit closes
        /// </summary>
        public Result OnShutdown(UIControlledApplication application)
        {
            return Result.Succeeded;
        }
    }
}
