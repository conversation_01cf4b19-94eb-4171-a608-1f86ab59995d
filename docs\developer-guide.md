# Developer Documentation

This document provides technical information for developers looking to extend, modify or maintain the RevitAddIn2025 add-in.

## Architecture Overview

RevitAddIn2025 follows a modular architecture with clear separation of concerns:

```
RevitAddIn2025
├── App                  # Application integration with Revit
│   └── RevitApplication # Main entry point implementing IExternalApplication
├── Commands             # Revit command implementations
├── Models               # Data models and business logic
├── Resources            # Icons and other resources
├── UI                   # User interface components
│   ├── Common           # Shared styles and controls
│   ├── Dashboard        # Main dashboard window
│   └── Settings         # Settings window
└── Utilities            # Helper and utility classes
```

### Key Components

1. **RevitApplication**: The main entry point implementing `IExternalApplication` that creates the ribbon integration
2. **Command Classes**: Each implementing `IExternalCommand` to handle specific functionality
3. **UI Windows**: WPF windows implementing the Apple-inspired user interface
4. **Utility Classes**: Helper functions for Revit operations, resource management and more

## Development Environment Setup

### Prerequisites

- Visual Studio 2022 or later
- .NET 8.0 SDK
- Revit 2025 SDK
- Git (for version control)

### Building the Project

1. Clone the repository
2. Open `src/RevitAddIn2025.sln` in Visual Studio
3. Restore NuGet packages
4. Build the solution in Release/x64 configuration
5. Output will be in `src/bin/Release`

### Debugging

To debug the add-in:

1. Set up Visual Studio to attach to Revit:
   - Project Properties > Debug > Start external program: `C:\Program Files\Autodesk\Revit 2025\Revit.exe`
2. Build the project in Debug configuration
3. Copy the output to your Revit Addins folder
4. Launch Revit and set breakpoints in Visual Studio
5. When the breakpoint is hit, Visual Studio will attach to the Revit process

## Extending the Add-in

### Adding a New Command

1. Create a new class in the `Commands` folder implementing `IExternalCommand`
2. Register the command in `RevitApplication.cs` by adding a new button in the `CreateRibbonButtons` method
3. Implement the command logic in the `Execute` method

Example:

```csharp
[Transaction(TransactionMode.Manual)]
[Regeneration(RegenerationOption.Manual)]
public class NewCommand : IExternalCommand
{
    public Result Execute(
        ExternalCommandData commandData,
        ref string message,
        ElementSet elements)
    {
        try
        {
            // Command implementation
            TaskDialog.Show("New Command", "New command executed!");
            return Result.Succeeded;
        }
        catch (Exception ex)
        {
            message = ex.Message;
            return Result.Failed;
        }
    }
}
```

### Adding a New UI Window

1. Create a new XAML window in the appropriate UI folder
2. Make the window class inherit from `BaseWindow` for consistent styling
3. Implement the window logic in the code-behind file

Example:

```xml
<local:BaseWindow x:Class="RevitAddIn2025.UI.NewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:RevitAddIn2025.UI.Common"
        Title="New Window" 
        Height="600" Width="800">
    
    <!-- Window content -->
</local:BaseWindow>
```

### Customizing the UI

1. Modify `AppStyles.xaml` to change colors, fonts, and control styles
2. Update specific window layouts as needed
3. Add new resource files to the `Resources` folder

## Best Practices

### Coding Guidelines

- Follow C# coding conventions
- Use meaningful names for variables, methods, and classes
- Document public APIs with XML comments
- Handle exceptions gracefully with user-friendly messages
- Use `RevitUtils` for Revit API interactions

### Testing

- Test in different Revit versions to ensure compatibility
- Verify ribbon integration works correctly
- Test on different screen resolutions and scaling settings
- Check error handling by intentionally triggering edge cases

### Performance

- Minimize API calls within loops
- Use transactions efficiently
- Consider caching results when appropriate
- Profile code to identify bottlenecks

## Deployment

### Creating a Release

1. Update version information in `RevitAddIn2025.csproj`
2. Build in Release/x64 configuration
3. Test the build thoroughly
4. Package the output files into a ZIP archive
5. Create a new release on GitHub with release notes

### Installation Package

The release package should include:

- RevitAddIn2025.dll
- RevitAddIn2025.addin
- All dependent assemblies
- Resources folder with icons
- README and installation instructions

## Support and Maintenance

### Journal and Logging

The add-in uses a simple logging system that writes to:
- Debug output (when debugging)
- Log file in `%AppData%\RevitAddIn2025\logs`

To log messages:

```csharp
// Log methods will be automatically prefixed with timestamp and severity
Logger.Info("Operation completed successfully");
Logger.Warning("Something unusual occurred");
Logger.Error("An error occurred", exception);
```

### Troubleshooting

Common development issues:

1. **Missing references**: Ensure all required assemblies are referenced
2. **Revit API version mismatch**: Verify you're using the correct Revit API version
3. **Ribbon not showing**: Check the .addin manifest file and ribbon creation code
4. **Resources not loading**: Verify build action for resources (Resource vs. Content)
