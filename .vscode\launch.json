{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug MCP Server", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/src/RevitAddIn2025/AI/MCP/mcp-server.js", "cwd": "${workspaceFolder}/src/RevitAddIn2025/AI/MCP", "env": {"MCP_SERVER_PORT": "3000", "MCP_SERVER_NAME": "revit-mep-ai-enhanced-server", "MCP_SERVER_VERSION": "v2.0.0", "MCP_LEARNING_ENABLED": "true", "MCP_REVIT_WORKSPACE": "${workspaceFolder}/src/RevitAddIn2025", "MCP_ENHANCED_MODE": "true", "MCP_AUTO_ENHANCE": "true"}}]}