# DIAGNOSE DEPENDENCIES - RevitAddIn2025
# Check for dependency loading issues

$dllPath = "C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2025\RevitAddIn2025_20250527_184504\RevitAddIn2025.dll"

Write-Host "DEPENDENCY DIAGNOSIS" -ForegroundColor Magenta
Write-Host "====================" -ForegroundColor Magenta

try {
    $assembly = [System.Reflection.Assembly]::LoadFrom($dllPath)
    Write-Host "Assembly loaded successfully: $($assembly.FullName)" -ForegroundColor Green
    
    try {
        $types = $assembly.GetTypes()
        Write-Host "SUCCESS: All types loaded successfully" -ForegroundColor Green
        Write-Host "Total types: $($types.Count)" -ForegroundColor White
        
        # Look for our main class
        $mainClass = $types | Where-Object { $_.FullName -eq "RevitAddIn2025.App.RevitApplication" }
        if ($mainClass) {
            Write-Host "SUCCESS: Found main application class" -ForegroundColor Green
        } else {
            Write-Host "ERROR: Main application class not found" -ForegroundColor Red
            Write-Host "Available classes:" -ForegroundColor Yellow
            foreach ($type in $types) {
                Write-Host "  $($type.FullName)" -ForegroundColor White
            }
        }
        
    } catch [System.Reflection.ReflectionTypeLoadException] {
        Write-Host "ERROR: ReflectionTypeLoadException occurred" -ForegroundColor Red
        $loadException = $_.Exception
        
        Write-Host ""
        Write-Host "LOADER EXCEPTIONS:" -ForegroundColor Red
        Write-Host "==================" -ForegroundColor Red
        
        foreach ($loaderException in $loadException.LoaderExceptions) {
            Write-Host "  $($loaderException.Message)" -ForegroundColor Yellow
            if ($loaderException.InnerException) {
                Write-Host "    Inner: $($loaderException.InnerException.Message)" -ForegroundColor Gray
            }
        }
        
        Write-Host ""
        Write-Host "SUCCESSFULLY LOADED TYPES:" -ForegroundColor Green
        Write-Host "==========================" -ForegroundColor Green
        
        $loadedTypes = $loadException.Types | Where-Object { $_ -ne $null }
        foreach ($type in $loadedTypes) {
            Write-Host "  $($type.FullName)" -ForegroundColor White
        }
    }
    
} catch {
    Write-Host "ERROR: Failed to load assembly: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.InnerException) {
        Write-Host "Inner Exception: $($_.Exception.InnerException.Message)" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "CHECKING DEPENDENCIES:" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

try {
    $assemblyName = [System.Reflection.AssemblyName]::GetAssemblyName($dllPath)
    Write-Host "Assembly Name: $($assemblyName.FullName)" -ForegroundColor White
    
    # Load assembly for dependency checking
    $assembly = [System.Reflection.Assembly]::LoadFrom($dllPath)
    $referencedAssemblies = $assembly.GetReferencedAssemblies()
    
    Write-Host ""
    Write-Host "REFERENCED ASSEMBLIES:" -ForegroundColor Cyan
    foreach ($refAssembly in $referencedAssemblies) {
        Write-Host "  $($refAssembly.FullName)" -ForegroundColor White
        
        # Try to load each referenced assembly
        try {
            $loadedRef = [System.Reflection.Assembly]::Load($refAssembly)
            Write-Host "    STATUS: OK" -ForegroundColor Green
        } catch {
            Write-Host "    STATUS: FAILED - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "ERROR: Failed to check dependencies: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "REVIT API CHECK:" -ForegroundColor Cyan
Write-Host "================" -ForegroundColor Cyan

$revitApiPath = "C:\Program Files\Autodesk\Revit 2025\RevitAPI.dll"
$revitApiUIPath = "C:\Program Files\Autodesk\Revit 2025\RevitAPIUI.dll"

if (Test-Path $revitApiPath) {
    Write-Host "RevitAPI.dll: FOUND" -ForegroundColor Green
} else {
    Write-Host "RevitAPI.dll: NOT FOUND" -ForegroundColor Red
}

if (Test-Path $revitApiUIPath) {
    Write-Host "RevitAPIUI.dll: FOUND" -ForegroundColor Green
} else {
    Write-Host "RevitAPIUI.dll: NOT FOUND" -ForegroundColor Red
}

Write-Host ""
Write-Host "DIAGNOSIS COMPLETE" -ForegroundColor Magenta
