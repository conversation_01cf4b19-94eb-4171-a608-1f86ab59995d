# 🔥 RevitAddIn2025 Hot Reload Development Workflow

## 🚀 Quick Start

### **Method 1: Automatic Hot Reload (Recommended)**
```powershell
# Navigate to project directory
cd src/RevitAddIn2025

# Start automatic file watching and building
.\Development\HotReloadBuild.ps1 -Watch
```

### **Method 2: Manual Hot Reload**
```powershell
# Build and deploy manually
.\Development\HotReloadBuild.ps1

# Or use VSCode task: Ctrl+Shift+P → "Tasks: Run Task" → "🔥 Hot Reload Build"
```

### **Method 3: In-Revit Hot Reload**
1. Start Revit 2025
2. Load your project
3. Click the **"🔄 Dev Reload"** button in the RevitAddIn2025 ribbon
4. Make code changes
5. Click **"🔄 Dev Reload"** again to refresh

---

## 🎯 Development Workflow

### **Step 1: Initial Setup**
```powershell
# One-time setup
cd src/RevitAddIn2025
dotnet restore
.\Development\HotReloadBuild.ps1
```

### **Step 2: Start Development Session**
```powershell
# Option A: Auto-watch mode (recommended)
.\Development\HotReloadBuild.ps1 -Watch

# Option B: VSCode integrated
# Press Ctrl+Shift+P → "Tasks: Run Task" → "👀 Hot Reload Watch"
```

### **Step 3: Development Cycle**
1. **Make code changes** in VSCode
2. **Save files** (Ctrl+S)
3. **Watch console** for build status
4. **Test in Revit** immediately (no restart needed!)
5. **Repeat** as needed

---

## 🛠️ Available Commands

### **PowerShell Scripts**
```powershell
# Basic build and deploy
.\Development\HotReloadBuild.ps1

# Auto-watch for changes
.\Development\HotReloadBuild.ps1 -Watch

# Clean build
.\Development\HotReloadBuild.ps1 -Clean

# Release build
.\Development\HotReloadBuild.ps1 -Configuration Release
```

### **VSCode Tasks** (Ctrl+Shift+P → "Tasks: Run Task")
- **🔥 Hot Reload Build** - Build and deploy once
- **👀 Hot Reload Watch** - Auto-build on file changes
- **🧹 Clean Build** - Clean and rebuild
- **🚀 Start Revit 2025** - Launch Revit
- **🔄 Kill Revit Processes** - Force close Revit
- **📁 Open Revit Addins Folder** - Open addins directory

### **In-Revit Commands**
- **🔄 Dev Reload** - Manual hot reload trigger (Debug builds only)
- **🎯 Test** - Verify plugin functionality
- **🧠 MEP AI** - Test AI features

---

## 🎨 Claude Desktop + VSCode Integration

### **Setup Claude Desktop**
1. Install Claude Desktop app
2. Copy `claude_desktop_config.json` to Claude config directory
3. Restart Claude Desktop
4. Enable MCP servers in settings

### **VSCode Integration**
- **Settings**: Enhanced with Claude-specific configurations
- **Tasks**: Integrated hot reload commands
- **Terminal**: PowerShell with development shortcuts
- **Extensions**: Recommended AI coding assistants

### **Usage**
1. Open VSCode in project root
2. Start Claude Desktop
3. Use Claude for code assistance with full project context
4. Make changes in VSCode
5. Hot reload automatically deploys to Revit

---

## 🔧 Troubleshooting

### **Build Fails**
```powershell
# Check for compilation errors
dotnet build src/RevitAddIn2025/RevitAddIn2025.csproj

# Clean and rebuild
.\Development\HotReloadBuild.ps1 -Clean
```

### **DLL Locked Error**
```powershell
# Kill Revit processes
Get-Process | Where-Object {$_.ProcessName -like "*Revit*"} | Stop-Process -Force

# Or use VSCode task: "🔄 Kill Revit Processes"
```

### **Hot Reload Not Working**
1. Check if **🔄 Dev Reload** button is visible (Debug builds only)
2. Verify file watcher is running
3. Check console for error messages
4. Restart Revit if needed

### **Claude Desktop Issues**
1. Verify MCP servers are running: `npx @modelcontextprotocol/server-filesystem --help`
2. Check Claude Desktop logs
3. Restart Claude Desktop app

---

## 📊 Performance Tips

### **Faster Builds**
- Use incremental builds (default)
- Only build Debug configuration during development
- Use `-Watch` mode to avoid repeated startup costs

### **Efficient Testing**
- Use **🔄 Dev Reload** for quick command updates
- Test individual features with specific buttons
- Use Revit's journal files for automated testing

### **Memory Management**
- Restart Revit periodically during heavy development
- Monitor memory usage with Task Manager
- Use Release builds for performance testing

---

## 🎯 Best Practices

### **Code Changes**
- Make small, incremental changes
- Test each change immediately
- Use proper Transaction attributes
- Handle exceptions gracefully

### **Hot Reload Workflow**
- Keep Revit open during development
- Use file watcher for automatic builds
- Test commands immediately after reload
- Monitor console for build status

### **Version Control**
- Commit working versions frequently
- Use meaningful commit messages
- Tag stable releases
- Keep development branches clean

---

## 🚀 Advanced Features

### **Custom Build Configurations**
```powershell
# Development with extra debugging
.\Development\HotReloadBuild.ps1 -Configuration Debug -Verbose

# Production release
.\Development\HotReloadBuild.ps1 -Configuration Release -Clean
```

### **Multiple Revit Versions**
- Modify `HotReloadBuild.ps1` for different Revit versions
- Use conditional compilation for version-specific code
- Test across multiple Revit installations

### **Automated Testing**
- Integrate with Revit's testing framework
- Use journal files for automated workflows
- Set up CI/CD pipelines for releases

---

**🎉 Happy Coding! Your RevitAddIn2025 development workflow is now optimized for maximum productivity!**
