using Autodesk.Revit.UI;
using RevitAddIn2025.Models;
using RevitAddIn2025.Utilities;
using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Effects;
using WinComboBox = System.Windows.Controls.ComboBox;
using WinColor = System.Windows.Media.Color;

namespace RevitAddIn2025.UI.Settings
{
    /// <summary>
    /// Programmatic Settings Window with Apple-inspired design
    /// </summary>
    public class SettingsWindow : Window
    {
        private readonly UIDocument _uiDoc;
        private AppSettings _settings;

        // UI Elements
        private CheckBox _autoRefreshCheckbox;
        private WinComboBox _unitsComboBox;
        private CheckBox _themeToggle;
        private WinComboBox _fontSizeComboBox;
        private CheckBox _dataCacheCheckbox;
        private WinComboBox _logLevelComboBox;
        private TextBlock _revitVersionText;
        private TextBlock _pluginVersionText;
        private TextBlock _systemInfoText;

        public SettingsWindow(UIDocument uiDoc)
        {
            // Store Revit document references
            _uiDoc = uiDoc;

            // Get settings instance
            _settings = AppSettings.Instance;

            // Log window creation
            Logger.Info("Settings window created");

            // Create the UI programmatically
            CreateUI();

            // Load settings when window opens
            Loaded += SettingsWindow_Loaded;
        }

        private void CreateUI()
        {
            // Window properties
            Title = "RevitAddIn2025 Settings";
            Width = 800;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            Background = new SolidColorBrush(WinColor.FromRgb(242, 242, 247));

            // Main container
            var mainGrid = new Grid { Margin = new Thickness(20) };
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Header
            CreateHeader(mainGrid);

            // Content
            CreateContent(mainGrid);

            // Footer
            CreateFooter(mainGrid);

            Content = mainGrid;
        }

        private void CreateHeader(Grid parent)
        {
            var headerStack = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };

            var titleText = new TextBlock
            {
                Text = "RevitAddIn2025 Settings",
                FontSize = 24,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 8)
            };
            headerStack.Children.Add(titleText);

            var subtitleText = new TextBlock
            {
                Text = "Configure application preferences and options.",
                FontSize = 14,
                Foreground = Brushes.Gray
            };
            headerStack.Children.Add(subtitleText);

            Grid.SetRow(headerStack, 0);
            parent.Children.Add(headerStack);
        }

        private void SettingsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            Logger.Info("Loading settings");

            // Load system information
            LoadSystemInformation();

            // Load current settings
            LoadSettings();
        }

        private void CreateContent(Grid parent)
        {
            var scrollViewer = new ScrollViewer { VerticalScrollBarVisibility = ScrollBarVisibility.Auto };
            var contentStack = new StackPanel();

            // General Settings
            CreateGeneralSettingsCard(contentStack);

            // Display Settings
            CreateDisplaySettingsCard(contentStack);

            // Advanced Settings
            CreateAdvancedSettingsCard(contentStack);

            scrollViewer.Content = contentStack;
            Grid.SetRow(scrollViewer, 1);
            parent.Children.Add(scrollViewer);
        }

        private Border CreateSettingsCard(string title)
        {
            var card = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(16),
                Margin = new Thickness(0, 0, 0, 16),
                Effect = new DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 2,
                    BlurRadius = 8,
                    Opacity = 0.2
                }
            };

            var stack = new StackPanel();

            var titleBlock = new TextBlock
            {
                Text = title,
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 12)
            };
            stack.Children.Add(titleBlock);

            card.Child = stack;
            return card;
        }

        private void CreateGeneralSettingsCard(StackPanel parent)
        {
            var card = CreateSettingsCard("General Settings");
            var stack = (StackPanel)card.Child;

            // Auto-refresh option
            var autoRefreshGrid = new Grid { Margin = new Thickness(0, 8, 0, 0) };
            autoRefreshGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            autoRefreshGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var autoRefreshStack = new StackPanel();
            autoRefreshStack.Children.Add(new TextBlock { Text = "Auto-refresh Dashboard", FontWeight = FontWeights.Medium });
            autoRefreshStack.Children.Add(new TextBlock { Text = "Automatically refresh dashboard data when opening", FontSize = 12, Foreground = Brushes.Gray });
            Grid.SetColumn(autoRefreshStack, 0);
            autoRefreshGrid.Children.Add(autoRefreshStack);

            _autoRefreshCheckbox = new CheckBox { IsChecked = true, VerticalAlignment = VerticalAlignment.Center };
            Grid.SetColumn(_autoRefreshCheckbox, 1);
            autoRefreshGrid.Children.Add(_autoRefreshCheckbox);

            stack.Children.Add(autoRefreshGrid);

            // Units option
            var unitsGrid = new Grid { Margin = new Thickness(0, 8, 0, 0) };
            unitsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            unitsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var unitsStack = new StackPanel();
            unitsStack.Children.Add(new TextBlock { Text = "Units", FontWeight = FontWeights.Medium });
            unitsStack.Children.Add(new TextBlock { Text = "Select units for measurements", FontSize = 12, Foreground = Brushes.Gray });
            Grid.SetColumn(unitsStack, 0);
            unitsGrid.Children.Add(unitsStack);

            _unitsComboBox = new WinComboBox { Width = 150 };
            _unitsComboBox.Items.Add(new ComboBoxItem { Content = "Imperial", IsSelected = true });
            _unitsComboBox.Items.Add(new ComboBoxItem { Content = "Metric" });
            Grid.SetColumn(_unitsComboBox, 1);
            unitsGrid.Children.Add(_unitsComboBox);

            stack.Children.Add(unitsGrid);

            parent.Children.Add(card);
        }

        private void CreateDisplaySettingsCard(StackPanel parent)
        {
            var card = CreateSettingsCard("Display Settings");
            var stack = (StackPanel)card.Child;

            // Theme option
            var themeGrid = new Grid { Margin = new Thickness(0, 8, 0, 0) };
            themeGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            themeGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var themeStack = new StackPanel();
            themeStack.Children.Add(new TextBlock { Text = "Dark Theme", FontWeight = FontWeights.Medium });
            themeStack.Children.Add(new TextBlock { Text = "Use dark mode for application", FontSize = 12, Foreground = Brushes.Gray });
            Grid.SetColumn(themeStack, 0);
            themeGrid.Children.Add(themeStack);

            _themeToggle = new CheckBox { VerticalAlignment = VerticalAlignment.Center };
            _themeToggle.Checked += ThemeToggle_CheckedChanged;
            _themeToggle.Unchecked += ThemeToggle_CheckedChanged;
            Grid.SetColumn(_themeToggle, 1);
            themeGrid.Children.Add(_themeToggle);

            stack.Children.Add(themeGrid);

            // Font size option
            var fontGrid = new Grid { Margin = new Thickness(0, 8, 0, 0) };
            fontGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            fontGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var fontStack = new StackPanel();
            fontStack.Children.Add(new TextBlock { Text = "Font Size", FontWeight = FontWeights.Medium });
            fontStack.Children.Add(new TextBlock { Text = "Adjust UI text size", FontSize = 12, Foreground = Brushes.Gray });
            Grid.SetColumn(fontStack, 0);
            fontGrid.Children.Add(fontStack);

            _fontSizeComboBox = new WinComboBox { Width = 150 };
            _fontSizeComboBox.Items.Add(new ComboBoxItem { Content = "Small" });
            _fontSizeComboBox.Items.Add(new ComboBoxItem { Content = "Medium", IsSelected = true });
            _fontSizeComboBox.Items.Add(new ComboBoxItem { Content = "Large" });
            Grid.SetColumn(_fontSizeComboBox, 1);
            fontGrid.Children.Add(_fontSizeComboBox);

            stack.Children.Add(fontGrid);

            parent.Children.Add(card);
        }

        private void CreateAdvancedSettingsCard(StackPanel parent)
        {
            var card = CreateSettingsCard("Advanced Settings");
            var stack = (StackPanel)card.Child;

            // Data cache option
            var cacheGrid = new Grid { Margin = new Thickness(0, 8, 0, 0) };
            cacheGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            cacheGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var cacheStack = new StackPanel();
            cacheStack.Children.Add(new TextBlock { Text = "Data Caching", FontWeight = FontWeights.Medium });
            cacheStack.Children.Add(new TextBlock { Text = "Enable data caching for faster performance", FontSize = 12, Foreground = Brushes.Gray });
            Grid.SetColumn(cacheStack, 0);
            cacheGrid.Children.Add(cacheStack);

            _dataCacheCheckbox = new CheckBox { IsChecked = true, VerticalAlignment = VerticalAlignment.Center };
            Grid.SetColumn(_dataCacheCheckbox, 1);
            cacheGrid.Children.Add(_dataCacheCheckbox);

            stack.Children.Add(cacheGrid);

            // Log level option
            var logGrid = new Grid { Margin = new Thickness(0, 8, 0, 0) };
            logGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            logGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var logStack = new StackPanel();
            logStack.Children.Add(new TextBlock { Text = "Logging Level", FontWeight = FontWeights.Medium });
            logStack.Children.Add(new TextBlock { Text = "Set application logging verbosity", FontSize = 12, Foreground = Brushes.Gray });
            Grid.SetColumn(logStack, 0);
            logGrid.Children.Add(logStack);

            _logLevelComboBox = new WinComboBox { Width = 150 };
            _logLevelComboBox.Items.Add(new ComboBoxItem { Content = "Error" });
            _logLevelComboBox.Items.Add(new ComboBoxItem { Content = "Warning" });
            _logLevelComboBox.Items.Add(new ComboBoxItem { Content = "Info", IsSelected = true });
            _logLevelComboBox.Items.Add(new ComboBoxItem { Content = "Debug" });
            _logLevelComboBox.Items.Add(new ComboBoxItem { Content = "Verbose" });
            Grid.SetColumn(_logLevelComboBox, 1);
            logGrid.Children.Add(_logLevelComboBox);

            stack.Children.Add(logGrid);

            // System info
            var infoBox = new Border
            {
                Margin = new Thickness(0, 16, 0, 0),
                Background = new SolidColorBrush(WinColor.FromRgb(245, 245, 247)),
                Padding = new Thickness(12, 12, 12, 12),
                CornerRadius = new CornerRadius(8)
            };

            var infoStack = new StackPanel();
            infoStack.Children.Add(new TextBlock { Text = "System Information", FontWeight = FontWeights.Medium });

            _revitVersionText = new TextBlock { Text = "Revit Version: 2025", Margin = new Thickness(0, 4, 0, 0) };
            _pluginVersionText = new TextBlock { Text = "Plugin Version: 1.0.0", Margin = new Thickness(0, 4, 0, 0) };
            _systemInfoText = new TextBlock { Text = "OS: Windows 11", Margin = new Thickness(0, 4, 0, 0) };

            infoStack.Children.Add(_revitVersionText);
            infoStack.Children.Add(_pluginVersionText);
            infoStack.Children.Add(_systemInfoText);

            infoBox.Child = infoStack;
            stack.Children.Add(infoBox);

            parent.Children.Add(card);
        }

        private void CreateFooter(Grid parent)
        {
            var footerGrid = new Grid { Margin = new Thickness(0, 20, 0, 0) };
            footerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            footerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            footerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });
            footerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            var resetBtn = CreateAppleButton("Reset to Default", WinColor.FromRgb(255, 45, 85), true);
            resetBtn.Click += ResetToDefault_Click;
            resetBtn.Margin = new Thickness(0, 0, 8, 0);
            Grid.SetColumn(resetBtn, 0);
            footerGrid.Children.Add(resetBtn);

            var cancelBtn = CreateAppleButton("Cancel", WinColor.FromRgb(0, 122, 255), true);
            cancelBtn.Click += Cancel_Click;
            cancelBtn.Margin = new Thickness(0, 0, 8, 0);
            Grid.SetColumn(cancelBtn, 2);
            footerGrid.Children.Add(cancelBtn);

            var saveBtn = CreateAppleButton("Save", WinColor.FromRgb(0, 122, 255), false);
            saveBtn.Click += Save_Click;
            Grid.SetColumn(saveBtn, 3);
            footerGrid.Children.Add(saveBtn);

            Grid.SetRow(footerGrid, 2);
            parent.Children.Add(footerGrid);
        }

        private Button CreateAppleButton(string text, WinColor color, bool isOutline)
        {
            var button = new Button
            {
                Content = text,
                Padding = new Thickness(12, 6, 12, 6),
                FontWeight = FontWeights.Medium
            };

            if (isOutline)
            {
                button.Background = Brushes.Transparent;
                button.Foreground = new SolidColorBrush(color);
                button.BorderBrush = new SolidColorBrush(color);
                button.BorderThickness = new Thickness(1);
            }
            else
            {
                button.Background = new SolidColorBrush(color);
                button.Foreground = Brushes.White;
                button.BorderThickness = new Thickness(0);
            }

            return button;
        }

        private void LoadSystemInformation()
        {
            try
            {
                Logger.Debug("Loading system information");

                if (_uiDoc?.Application != null)
                {
                    // Get Revit version
                    string revitVersion = _uiDoc.Application.Application.VersionName;
                    _revitVersionText.Text = $"Revit Version: {revitVersion}";

                    // Get plugin version
                    string pluginVersion = ResourceHelper.GetAssemblyProductVersion();
                    _pluginVersionText.Text = $"Plugin Version: {pluginVersion}";

                    // Get OS info
                    _systemInfoText.Text = $"OS: {Environment.OSVersion.VersionString}";

                    Logger.Debug("System information loaded successfully");
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading system information", ex);
                MessageBox.Show($"Error loading system information: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSettings()
        {
            try
            {
                Logger.Debug("Loading current settings");

                // Load general settings
                _autoRefreshCheckbox.IsChecked = _settings.AutoRefreshDashboard;
                _unitsComboBox.SelectedIndex = _settings.Units == "Metric" ? 1 : 0;

                // Load display settings
                _themeToggle.IsChecked = _settings.UseDarkTheme;
                _fontSizeComboBox.SelectedIndex = _settings.FontSize == "Small" ? 0 :
                                                _settings.FontSize == "Large" ? 2 : 1;

                // Load advanced settings
                _dataCacheCheckbox.IsChecked = _settings.EnableDataCache;

                // Set log level combobox
                switch (_settings.LoggingLevel.ToLower())
                {
                    case "error": _logLevelComboBox.SelectedIndex = 0; break;
                    case "warning": _logLevelComboBox.SelectedIndex = 1; break;
                    case "info": _logLevelComboBox.SelectedIndex = 2; break;
                    case "debug": _logLevelComboBox.SelectedIndex = 3; break;
                    case "verbose": _logLevelComboBox.SelectedIndex = 4; break;
                    default: _logLevelComboBox.SelectedIndex = 2; break;
                }

                Logger.Debug("Settings loaded successfully");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading settings", ex);
                MessageBox.Show($"Error loading settings: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveSettings()
        {
            try
            {
                Logger.Info("Saving settings");

                // Save general settings
                _settings.AutoRefreshDashboard = _autoRefreshCheckbox.IsChecked ?? true;
                _settings.Units = _unitsComboBox.SelectedIndex == 1 ? "Metric" : "Imperial";

                // Save display settings
                _settings.UseDarkTheme = _themeToggle.IsChecked ?? false;
                _settings.Theme = _settings.UseDarkTheme ? "Dark" : "Light";
                _settings.FontSize = _fontSizeComboBox.SelectedIndex == 0 ? "Small" :
                                    _fontSizeComboBox.SelectedIndex == 2 ? "Large" : "Medium";

                // Save advanced settings
                _settings.EnableDataCache = _dataCacheCheckbox.IsChecked ?? true;

                // Save logging level
                string[] logLevels = { "Error", "Warning", "Info", "Debug", "Verbose" };
                _settings.LoggingLevel = logLevels[_logLevelComboBox.SelectedIndex];

                // Apply the new logging level
                Logger.SetLogLevel(_settings.LoggingLevel);

                // Persist settings to disk
                bool success = _settings.Save();

                if (success)
                {
                    Logger.Info("Settings saved successfully");
                    MessageBox.Show("Settings saved successfully.", "Settings", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    Logger.Warning("Settings could not be saved to disk");
                    MessageBox.Show("Settings applied but could not be saved permanently.", "Settings", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error saving settings", ex);
                MessageBox.Show($"Error saving settings: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ThemeToggle_CheckedChanged(object sender, RoutedEventArgs e)
        {
            try
            {
                bool isDarkTheme = _themeToggle.IsChecked ?? false;
                Logger.Info($"Theme changed to {(isDarkTheme ? "Dark" : "Light")}");
            }
            catch (Exception ex)
            {
                Logger.Error("Error changing theme", ex);
                MessageBox.Show($"Error changing theme: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ResetToDefault_Click(object sender, RoutedEventArgs e)
        {
            MessageBoxResult result = MessageBox.Show(
                "Are you sure you want to reset all settings to default values?",
                "Confirm Reset",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Logger.Info("Resetting settings to defaults");
                _settings.ResetToDefaults();
                LoadSettings();
                MessageBox.Show("Settings have been reset to default values.", "Reset Complete", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("Settings cancelled");
            this.Close();
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveSettings();
                this.Close();
            }
            catch (Exception ex)
            {
                Logger.Error("Error saving settings", ex);
                MessageBox.Show($"Error saving settings: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
