using Autodesk.Revit.UI;
using RevitAddIn2025.Models;
using RevitAddIn2025.UI.Common;
using RevitAddIn2025.Utilities;
using System;
using System.Windows;

namespace RevitAddIn2025.UI.Settings
{
    /// <summary>
    /// Interaction logic for SettingsWindow.xaml
    /// </summary>
    public partial class SettingsWindow : Window
    {
        private readonly UIDocument _uiDoc;
        private AppSettings _settings;

        public SettingsWindow(UIDocument uiDoc)
        {
            InitializeComponent();

            // Store Revit document references
            _uiDoc = uiDoc;

            // Get settings instance
            _settings = AppSettings.Instance;

            // Log window creation
            Logger.Info("Settings window created");

            // Load settings when window opens
            Loaded += SettingsWindow_Loaded;
        }

        private void SettingsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            Logger.Info("Loading settings");

            // Load system information
            LoadSystemInformation();

            // Load current settings
            LoadSettings();
        }

        private void LoadSystemInformation()
        {
            try
            {
                Logger.Debug("Loading system information");

                if (_uiDoc?.Application != null)
                {
                    // Get Revit version
                    string revitVersion = _uiDoc.Application.Application.VersionName;
                    RevitVersionText.Text = $"Revit Version: {revitVersion}";

                    // Get plugin version
                    string pluginVersion = ResourceHelper.GetAssemblyProductVersion();
                    PluginVersionText.Text = $"Plugin Version: {pluginVersion}";

                    // Get OS info
                    SystemInfoText.Text = $"OS: {Environment.OSVersion.VersionString}";

                    Logger.Debug("System information loaded successfully");
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading system information", ex);
                MessageBox.Show($"Error loading system information: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSettings()
        {
            try
            {
                Logger.Debug("Loading current settings");

                // Load general settings
                AutoRefreshCheckbox.IsChecked = _settings.AutoRefreshDashboard;
                UnitsComboBox.SelectedIndex = _settings.Units == "Metric" ? 1 : 0;
                // Load display settings
                ThemeToggle.IsChecked = _settings.UseDarkTheme;

                FontSizeComboBox.SelectedIndex = _settings.FontSize == "Small" ? 0 :
                                                _settings.FontSize == "Large" ? 2 : 1;

                // Load advanced settings
                DataCacheCheckbox.IsChecked = _settings.EnableDataCache;

                // Set log level combobox
                switch (_settings.LoggingLevel.ToLower())
                {
                    case "error":
                        LogLevelComboBox.SelectedIndex = 0;
                        break;
                    case "warning":
                        LogLevelComboBox.SelectedIndex = 1;
                        break;
                    case "info":
                        LogLevelComboBox.SelectedIndex = 2;
                        break;
                    case "debug":
                        LogLevelComboBox.SelectedIndex = 3;
                        break;
                    case "verbose":
                        LogLevelComboBox.SelectedIndex = 4;
                        break;
                    default:
                        LogLevelComboBox.SelectedIndex = 2; // Default to Info
                        break;
                }

                Logger.Debug("Settings loaded successfully");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading settings", ex);
                MessageBox.Show($"Error loading settings: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveSettings()
        {
            try
            {
                Logger.Info("Saving settings");

                // Save general settings
                _settings.AutoRefreshDashboard = AutoRefreshCheckbox.IsChecked ?? true;
                _settings.Units = UnitsComboBox.SelectedIndex == 1 ? "Metric" : "Imperial";
                // Save display settings
                _settings.UseDarkTheme = ThemeToggle.IsChecked ?? false;
                _settings.Theme = _settings.UseDarkTheme ? "Dark" : "Light";

                _settings.FontSize = FontSizeComboBox.SelectedIndex == 0 ? "Small" :
                                    FontSizeComboBox.SelectedIndex == 2 ? "Large" : "Medium";

                // Save advanced settings
                _settings.EnableDataCache = DataCacheCheckbox.IsChecked ?? true;

                // Save logging level
                string[] logLevels = { "Error", "Warning", "Info", "Debug", "Verbose" };
                _settings.LoggingLevel = logLevels[LogLevelComboBox.SelectedIndex];

                // Apply the new logging level
                Logger.SetLogLevel(_settings.LoggingLevel);

                // Persist settings to disk
                bool success = _settings.Save();

                if (success)
                {
                    Logger.Info("Settings saved successfully");
                    ShowMessage("Settings saved successfully.", "Settings");
                }
                else
                {
                    Logger.Warning("Settings could not be saved to disk");
                    ShowMessage("Settings applied but could not be saved permanently.", "Settings");
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error saving settings", ex);
                ShowError($"Error saving settings: {ex.Message}", "Error");
            }
        }

        private void ThemeToggle_CheckedChanged(object sender, RoutedEventArgs e)
        {
            try
            {
                bool isDarkTheme = ThemeToggle.IsChecked ?? false;
                ThemeManager.SetTheme(isDarkTheme ? ThemeManager.ThemeType.Dark : ThemeManager.ThemeType.Light);

                // Log the theme change
                Logger.Info($"Theme changed to {(isDarkTheme ? "Dark" : "Light")}");
            }
            catch (Exception ex)
            {
                Logger.Error("Error changing theme", ex);
                MessageBox.Show($"Error changing theme: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region Button Event Handlers

        private void ResetToDefault_Click(object sender, RoutedEventArgs e)
        {
            // Ask for confirmation
            MessageBoxResult result = MessageBox.Show(
                "Are you sure you want to reset all settings to default values?",
                "Confirm Reset",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                Logger.Info("Resetting settings to defaults");

                // Reset settings to default values
                _settings.ResetToDefaults();

                // Reload settings in UI
                LoadSettings();

                // Notify user
                ShowMessage("Settings have been reset to default values.", "Reset Complete");
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("Settings cancelled");

            // Close without saving
            this.Close();
        }

        private void Save_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Save settings
                SaveSettings();

                // Close the window
                this.Close();
            }
            catch (Exception ex)
            {
                // Handle and display error
                Logger.Error("Error saving settings", ex);
                ShowError($"Error saving settings: {ex.Message}", "Error");
            }
        }

        #endregion
    }
}
