using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.Models;
using RevitAddIn2025.UI.Common;
using RevitAddIn2025.UI.Settings;
using RevitAddIn2025.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using WinGrid = System.Windows.Controls.Grid;
using WinComboBox = System.Windows.Controls.ComboBox;
using WinVisibility = System.Windows.Visibility;

namespace RevitAddIn2025.UI.Dashboard
{    /// <summary>
     /// Interaction logic for DashboardWindow.xaml
     /// </summary>
    public partial class DashboardWindow : Window
    {
        private readonly UIDocument _uiDoc;
        private readonly Document _doc;
        private ProjectAnalytics _projectAnalytics;

        public DashboardWindow(UIDocument uiDoc)
        {
            InitializeComponent();

            // Store Revit document references
            _uiDoc = uiDoc;
            _doc = uiDoc?.Document;

            // Initialize logger if not already done
            Logger.Initialize();
            Logger.Info("Dashboard window created");

            // Load data when window opens
            Loaded += DashboardWindow_Loaded;

            // Add keyboard shortcut handler
            this.KeyDown += DashboardWindow_KeyDown;
        }

        private void DashboardWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // Ctrl+R to refresh data
            if (e.Key == System.Windows.Input.Key.R &&
                (System.Windows.Input.Keyboard.Modifiers & System.Windows.Input.ModifierKeys.Control) == System.Windows.Input.ModifierKeys.Control)
            {
                RefreshData_Click(sender, e);
                e.Handled = true;
                Logger.Info("Data refresh triggered by keyboard shortcut (Ctrl+R)");
            }
        }

        private void DashboardWindow_Loaded(object sender, RoutedEventArgs e)
        {
            Logger.Info("Loading dashboard data");

            // Load initial data
            LoadAllData();

            // Add to recent activity
            RecentActivityList.Items.Insert(0, $"Dashboard opened at {DateTime.Now.ToString("HH:mm:ss")}");
        }
        private void LoadAllData(bool useCache = true)
        {
            try
            {
                // Start timing for performance measurement
                var startTime = DateTime.Now;

                // Store current document path for cache tracking
                string documentPath = _doc?.PathName;
                bool isFromCache = false;
                // Check if analytics exist in cache before loading
                if (useCache && !string.IsNullOrEmpty(documentPath) &&
                    AppSettings.Instance.EnableDataCache)
                {
                    // Method to check if data is cached without direct field access
                    isFromCache = ProjectAnalytics.IsCached(documentPath);
                }

                // Generate analytics for the current document
                _projectAnalytics = ProjectAnalytics.AnalyzeProject(_doc, useCache);

                // Update UI with data
                LoadProjectInformation();
                LoadElementCounts();
                LoadProjectStatistics();

                // Calculate and show performance stats
                var duration = DateTime.Now - startTime;
                UpdateCacheStatus(isFromCache, duration.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading dashboard data", ex);
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);

                // Update status on error
                UpdateCacheStatus(false, 0, true);
            }
        }

        /// <summary>
        /// Update the cache status indicator
        /// </summary>
        private void UpdateCacheStatus(bool fromCache, double durationMs, bool isError = false)
        {
            if (isError)
            {
                // Show error status
                CacheStatusText.Text = "Data Load Error";
                CacheStatusDot.Fill = System.Windows.Media.Brushes.Red;
                CacheStatusIndicator.BorderBrush = System.Windows.Media.Brushes.Red;
                CacheStatusIndicator.Background = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromArgb(50, 255, 0, 0));
                CacheStatusIndicator.ToolTip = "Error loading project data";
            }
            else if (fromCache)
            {
                // Show cached status with timing
                CacheStatusText.Text = $"Cached Data ({durationMs:0} ms)";
                CacheStatusDot.Fill = System.Windows.Media.Brushes.Green;
                CacheStatusIndicator.BorderBrush = System.Windows.Media.Brushes.Green;
                CacheStatusIndicator.Background = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromArgb(50, 0, 255, 0));
                CacheStatusIndicator.ToolTip = "Data loaded from cache for better performance";
            }
            else
            {
                // Show fresh data status with timing
                CacheStatusText.Text = $"Fresh Data ({durationMs:0} ms)";
                CacheStatusDot.Fill = System.Windows.Media.Brushes.Blue;
                CacheStatusIndicator.BorderBrush = System.Windows.Media.Brushes.Blue;
                CacheStatusIndicator.Background = new System.Windows.Media.SolidColorBrush(
                    System.Windows.Media.Color.FromArgb(50, 0, 0, 255));
                CacheStatusIndicator.ToolTip = "Data freshly analyzed from the current project";
            }
        }

        private void LoadProjectInformation()
        {
            try
            {
                // Update UI with project information from analytics
                ProjectNameText.Text = $"Project Name: {_projectAnalytics.ProjectName}";
                ProjectNumberText.Text = $"Project Number: {_projectAnalytics.ProjectNumber}";
                ProjectAddressText.Text = $"Project Address: {_projectAnalytics.ProjectAddress}";
                ProjectStatusText.Text = $"Project Status: {_projectAnalytics.ProjectStatus}";

                // Add to recent activity
                RecentActivityList.Items.Insert(0, $"Project information loaded: {_projectAnalytics.ProjectName}");
                Logger.Info($"Project information loaded: {_projectAnalytics.ProjectName}");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading project information", ex);
                MessageBox.Show($"Error loading project information: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadElementCounts()
        {
            try
            {
                // Update UI with element counts from analytics
                WallsCountText.Text = $"Walls: {_projectAnalytics.WallCount}";
                DoorsCountText.Text = $"Doors: {_projectAnalytics.DoorCount}";
                WindowsCountText.Text = $"Windows: {_projectAnalytics.WindowCount}";
                RoomsCountText.Text = $"Rooms: {_projectAnalytics.RoomCount}";

                // Add to recent activity
                RecentActivityList.Items.Insert(0, "Element counts loaded");
                Logger.Info("Element counts loaded");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading element counts", ex);
                MessageBox.Show($"Error loading element counts: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadProjectStatistics()
        {
            try
            {
                // Update UI with project statistics from analytics
                TotalElementsText.Text = _projectAnalytics.TotalElements.ToString();
                FileSizeText.Text = _projectAnalytics.FileSize;
                LastSaveText.Text = _projectAnalytics.LastSaveTime;
                WorksetsText.Text = _projectAnalytics.WorksetCount.ToString();

                // Add to recent activity
                RecentActivityList.Items.Insert(0, "Project statistics loaded");
                Logger.Info("Project statistics loaded");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading project statistics", ex);
                MessageBox.Show($"Error loading project statistics: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region Button Event Handlers

        private void ViewProjectDetails_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("View Project Details clicked");
            MessageBox.Show("Project details functionality to be implemented.", "Coming Soon");
        }

        private void AnalyzeElements_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("Analyze Elements clicked");
            MessageBox.Show("Element analysis functionality to be implemented.", "Coming Soon");
        }

        private void ViewAllActivity_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("View All Activity clicked");
            MessageBox.Show("Activity history functionality to be implemented.", "Coming Soon");
        }

        private void OpenSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Info("Opening settings window");
                SettingsWindow settingsWindow = new SettingsWindow(_uiDoc);
                settingsWindow.Owner = this;
                settingsWindow.ShowDialog();

                // Refresh data after settings change
                Logger.Info("Refreshing data after settings change");
                LoadAllData();
            }
            catch (Exception ex)
            {
                Logger.Error("Error opening settings", ex);
                MessageBox.Show($"Error opening settings: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenHelp_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("Help requested");
            MessageBox.Show("Help documentation will be displayed here.\n\nFor more information, visit our documentation website.", "Help");
        }

        private void GenerateReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create report generator options popup
                var reportWindow = new Window
                {
                    Title = "Generate Report",
                    Width = 400,
                    Height = 300,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = this,
                    ResizeMode = ResizeMode.NoResize,
                    Background = (System.Windows.Media.Brush)FindResource("BackgroundBrush")
                };

                // Create content
                var grid = new WinGrid { Margin = new Thickness(20) };
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

                // Title
                var title = new TextBlock
                {
                    Text = "Select Report Type",
                    FontSize = 18,
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 0, 0, 15)
                };
                WinGrid.SetRow(title, 0);
                grid.Children.Add(title);

                // Report options
                var reportTypesStack = new StackPanel { Margin = new Thickness(0, 0, 0, 15) };

                var summaryRadio = new RadioButton
                {
                    Content = "Project Summary Report",
                    IsChecked = true,
                    Margin = new Thickness(0, 0, 0, 10)
                };
                reportTypesStack.Children.Add(summaryRadio);

                var elementsRadio = new RadioButton
                {
                    Content = "Element Details Report",
                    Margin = new Thickness(0, 0, 0, 10)
                };
                reportTypesStack.Children.Add(elementsRadio);

                WinGrid.SetRow(reportTypesStack, 1);
                grid.Children.Add(reportTypesStack);

                // Category selection for element report
                var categoryLabel = new TextBlock
                {
                    Text = "Select Category (for Element Report):",
                    Margin = new Thickness(0, 0, 0, 5),
                    Visibility = WinVisibility.Collapsed
                };
                WinGrid.SetRow(categoryLabel, 2);
                grid.Children.Add(categoryLabel);

                var categoryCombo = new WinComboBox
                {
                    Margin = new Thickness(0, 0, 0, 15),
                    Visibility = WinVisibility.Collapsed
                };
                categoryCombo.Items.Add(new ComboBoxItem { Content = "All Categories" });
                categoryCombo.Items.Add(new ComboBoxItem { Content = "Walls" });
                categoryCombo.Items.Add(new ComboBoxItem { Content = "Doors" });
                categoryCombo.Items.Add(new ComboBoxItem { Content = "Windows" });
                categoryCombo.Items.Add(new ComboBoxItem { Content = "Rooms" });
                categoryCombo.Items.Add(new ComboBoxItem { Content = "Levels" });
                categoryCombo.SelectedIndex = 0;

                WinGrid.SetRow(categoryCombo, 3);
                grid.Children.Add(categoryCombo);

                // Hook up visibility toggling
                elementsRadio.Checked += (s, args) =>
                {
                    categoryLabel.Visibility = WinVisibility.Visible;
                    categoryCombo.Visibility = WinVisibility.Visible;
                };
                summaryRadio.Checked += (s, args) =>
                {
                    categoryLabel.Visibility = WinVisibility.Collapsed;
                    categoryCombo.Visibility = WinVisibility.Collapsed;
                };

                // Buttons
                var buttonPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, 15, 0, 0)
                };

                var cancelButton = new Button
                {
                    Content = "Cancel",
                    Width = 100,
                    Margin = new Thickness(0, 0, 10, 0),
                    Style = (Style)FindResource("AppleSecondaryButton")
                };
                cancelButton.Click += (s, args) => reportWindow.Close();

                var generateButton = new Button
                {
                    Content = "Generate",
                    Width = 100,
                    Style = (Style)FindResource("ApplePrimaryButton")
                };
                generateButton.Click += (s, args) =>
                {
                    try
                    {
                        if (summaryRadio.IsChecked == true)
                        {
                            ReportGenerator.GenerateProjectSummaryReport(_doc);
                            RecentActivityList.Items.Insert(0, $"Project summary report generated at {DateTime.Now.ToString("HH:mm:ss")}");
                        }
                        else
                        {
                            BuiltInCategory category = BuiltInCategory.INVALID;
                            switch (categoryCombo.SelectedIndex)
                            {
                                case 1: category = BuiltInCategory.OST_Walls; break;
                                case 2: category = BuiltInCategory.OST_Doors; break;
                                case 3: category = BuiltInCategory.OST_Windows; break;
                                case 4: category = BuiltInCategory.OST_Rooms; break;
                                case 5: category = BuiltInCategory.OST_Levels; break;
                            }
                            ReportGenerator.GenerateElementReport(_doc, category);
                            RecentActivityList.Items.Insert(0, $"Element report generated at {DateTime.Now.ToString("HH:mm:ss")}");
                        }
                        reportWindow.Close();
                    }
                    catch (Exception ex)
                    {
                        Logger.Error("Error generating report", ex);
                        MessageBox.Show($"Error generating report: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                };

                buttonPanel.Children.Add(cancelButton);
                buttonPanel.Children.Add(generateButton);

                WinGrid.SetRow(buttonPanel, 4);
                grid.Children.Add(buttonPanel);

                // Set content and show dialog
                reportWindow.Content = grid;
                reportWindow.ShowDialog();

                Logger.Info("Report generation dialog shown");
            }
            catch (Exception ex)
            {
                Logger.Error("Error showing report generation dialog", ex);
                MessageBox.Show($"Error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenAbout_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("About dialog opened");

            string version = ResourceHelper.GetAssemblyProductVersion();
            MessageBox.Show($"RevitAddIn2025\nVersion {version}\n\n© 2025 Your Company Name\n\nA modern Revit add-in with an Apple-inspired UI", "About RevitAddIn2025");
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Info("Manually refreshing data");

                // Reload all data
                LoadAllData();

                // Add to recent activity
                RecentActivityList.Items.Insert(0, $"Data refreshed at {DateTime.Now.ToString("HH:mm:ss")}");
            }
            catch (Exception ex)
            {
                Logger.Error("Error refreshing data", ex);
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("Dashboard closing");
            this.Close();
        }

        #endregion
    }
}
