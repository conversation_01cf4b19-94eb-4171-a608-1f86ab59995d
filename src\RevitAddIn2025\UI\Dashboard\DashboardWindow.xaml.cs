using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.Models;
using RevitAddIn2025.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Effects;
using WinGrid = System.Windows.Controls.Grid;
using WinColor = System.Windows.Media.Color;
using WinEllipse = System.Windows.Shapes.Ellipse;

namespace RevitAddIn2025.UI.Dashboard
{
    /// <summary>
    /// Programmatic Dashboard Window with Apple-inspired design
    /// </summary>
    public class DashboardWindow : Window
    {
        private readonly UIDocument _uiDoc;
        private readonly Document _doc;
        private ProjectAnalytics _projectAnalytics;

        // UI Elements
        private TextBlock _projectNameText;
        private TextBlock _projectNumberText;
        private TextBlock _projectAddressText;
        private TextBlock _projectStatusText;
        private TextBlock _wallsCountText;
        private TextBlock _doorsCountText;
        private TextBlock _windowsCountText;
        private TextBlock _roomsCountText;
        private TextBlock _totalElementsText;
        private TextBlock _fileSizeText;
        private TextBlock _lastSaveText;
        private TextBlock _worksetsText;
        private ListBox _recentActivityList;
        private Border _cacheStatusIndicator;
        private Ellipse _cacheStatusDot;
        private TextBlock _cacheStatusText;

        public DashboardWindow(UIDocument uiDoc)
        {
            // Store Revit document references
            _uiDoc = uiDoc;
            _doc = uiDoc?.Document;

            // Initialize logger if not already done
            Logger.Initialize();
            Logger.Info("Dashboard window created");

            // Create the UI programmatically
            CreateUI();

            // Load data when window opens
            Loaded += DashboardWindow_Loaded;

            // Add keyboard shortcut handler
            this.KeyDown += DashboardWindow_KeyDown;
        }

        private void CreateUI()
        {
            // Window properties
            Title = "RevitAddIn2025 Dashboard";
            Width = 1200;
            Height = 720;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Background = new SolidColorBrush(WinColor.FromRgb(242, 242, 247));

            // Main container
            var mainGrid = new WinGrid { Margin = new Thickness(20) };
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Header
            CreateHeader(mainGrid);

            // Content
            CreateContent(mainGrid);

            // Footer
            CreateFooter(mainGrid);

            Content = mainGrid;
        }

        private void CreateHeader(WinGrid parent)
        {
            var headerStack = new StackPanel { Margin = new Thickness(0, 0, 0, 20) };

            var titleText = new TextBlock
            {
                Text = "RevitAddIn2025 Dashboard",
                FontSize = 24,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 8)
            };
            headerStack.Children.Add(titleText);

            var subtitleText = new TextBlock
            {
                Text = "Welcome to RevitAddIn2025. This dashboard provides access to all features and tools.",
                FontSize = 14,
                Foreground = Brushes.Gray
            };
            headerStack.Children.Add(subtitleText);

            WinGrid.SetRow(headerStack, 0);
            parent.Children.Add(headerStack);
        }

        private void CreateContent(WinGrid parent)
        {
            var scrollViewer = new ScrollViewer { VerticalScrollBarVisibility = ScrollBarVisibility.Auto };

            var contentGrid = new WinGrid { Margin = new Thickness(0, 8) };
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            contentGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            contentGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Create cards
            CreateProjectCard(contentGrid, 0, 0);
            CreateElementsCard(contentGrid, 1, 0);
            CreateActivityCard(contentGrid, 2, 0);
            CreateStatisticsCard(contentGrid, 0, 1, 3);

            scrollViewer.Content = contentGrid;
            WinGrid.SetRow(scrollViewer, 1);
            parent.Children.Add(scrollViewer);
        }

        private Border CreateCard(string title)
        {
            var card = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(12),
                Padding = new Thickness(16),
                Margin = new Thickness(4),
                Effect = new DropShadowEffect
                {
                    Color = Colors.Gray,
                    Direction = 270,
                    ShadowDepth = 2,
                    BlurRadius = 8,
                    Opacity = 0.2
                }
            };

            var stack = new StackPanel();

            var titleBlock = new TextBlock
            {
                Text = title,
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 12)
            };
            stack.Children.Add(titleBlock);

            card.Child = stack;
            return card;
        }

        private void CreateProjectCard(WinGrid parent, int column, int row)
        {
            var card = CreateCard("Project Information");
            var stack = (StackPanel)card.Child;

            _projectNameText = new TextBlock { Text = "Project Name: [Not Available]", Margin = new Thickness(0, 4) };
            _projectNumberText = new TextBlock { Text = "Project Number: [Not Available]", Margin = new Thickness(0, 4) };
            _projectAddressText = new TextBlock { Text = "Project Address: [Not Available]", Margin = new Thickness(0, 4) };
            _projectStatusText = new TextBlock { Text = "Project Status: [Not Available]", Margin = new Thickness(0, 4) };

            stack.Children.Add(_projectNameText);
            stack.Children.Add(_projectNumberText);
            stack.Children.Add(_projectAddressText);
            stack.Children.Add(_projectStatusText);

            var button = CreateAppleButton("View Project Details", WinColor.FromRgb(0, 122, 255));
            button.Click += ViewProjectDetails_Click;
            button.Margin = new Thickness(0, 12, 0, 0);
            button.HorizontalAlignment = HorizontalAlignment.Left;
            stack.Children.Add(button);

            WinGrid.SetColumn(card, column);
            WinGrid.SetRow(card, row);
            parent.Children.Add(card);
        }

        private void CreateElementsCard(WinGrid parent, int column, int row)
        {
            var card = CreateCard("Element Summary");
            var stack = (StackPanel)card.Child;

            _wallsCountText = new TextBlock { Text = "Walls: 0", Margin = new Thickness(0, 4) };
            _doorsCountText = new TextBlock { Text = "Doors: 0", Margin = new Thickness(0, 4) };
            _windowsCountText = new TextBlock { Text = "Windows: 0", Margin = new Thickness(0, 4) };
            _roomsCountText = new TextBlock { Text = "Rooms: 0", Margin = new Thickness(0, 4) };

            stack.Children.Add(_wallsCountText);
            stack.Children.Add(_doorsCountText);
            stack.Children.Add(_windowsCountText);
            stack.Children.Add(_roomsCountText);

            var button = CreateAppleButton("Analyze Elements", WinColor.FromRgb(0, 122, 255));
            button.Click += AnalyzeElements_Click;
            button.Margin = new Thickness(0, 12, 0, 0);
            button.HorizontalAlignment = HorizontalAlignment.Left;
            stack.Children.Add(button);

            WinGrid.SetColumn(card, column);
            WinGrid.SetRow(card, row);
            parent.Children.Add(card);
        }

        private void CreateActivityCard(WinGrid parent, int column, int row)
        {
            var card = CreateCard("Recent Activity");
            var stack = (StackPanel)card.Child;

            _recentActivityList = new ListBox
            {
                BorderThickness = new Thickness(0),
                Background = Brushes.Transparent,
                Height = 100
            };
            _recentActivityList.Items.Add("Dashboard opened");
            _recentActivityList.Items.Add("Project loaded");

            stack.Children.Add(_recentActivityList);

            var button = CreateAppleButton("View All Activity", WinColor.FromRgb(0, 122, 255));
            button.Click += ViewAllActivity_Click;
            button.Margin = new Thickness(0, 12, 0, 0);
            button.HorizontalAlignment = HorizontalAlignment.Left;
            stack.Children.Add(button);

            WinGrid.SetColumn(card, column);
            WinGrid.SetRow(card, row);
            parent.Children.Add(card);
        }

        private Button CreateAppleButton(string text, WinColor backgroundColor)
        {
            return new Button
            {
                Content = text,
                Background = new SolidColorBrush(backgroundColor),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                Padding = new Thickness(12, 6),
                FontWeight = FontWeights.Medium
            };
        }

        private void CreateStatisticsCard(WinGrid parent, int column, int row, int columnSpan)
        {
            var card = CreateCard("Project Statistics");
            var stack = (StackPanel)card.Child;

            var statsGrid = new WinGrid();
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            statsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Create stat boxes
            CreateStatBox(statsGrid, "Total Elements", ref _totalElementsText, WinColor.FromRgb(0, 122, 255), 0);
            CreateStatBox(statsGrid, "File Size", ref _fileSizeText, WinColor.FromRgb(88, 86, 214), 1);
            CreateStatBox(statsGrid, "Last Save", ref _lastSaveText, WinColor.FromRgb(255, 45, 85), 2);
            CreateStatBox(statsGrid, "Worksets", ref _worksetsText, WinColor.FromRgb(52, 199, 89), 3);

            stack.Children.Add(statsGrid);

            WinGrid.SetColumn(card, column);
            WinGrid.SetRow(card, row);
            WinGrid.SetColumnSpan(card, columnSpan);
            parent.Children.Add(card);
        }

        private void CreateStatBox(WinGrid parent, string label, ref TextBlock valueText, WinColor backgroundColor, int column)
        {
            var border = new Border
            {
                Background = new SolidColorBrush(backgroundColor),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(2),
                Padding = new Thickness(12)
            };

            var stack = new StackPanel();

            var labelText = new TextBlock
            {
                Text = label,
                Foreground = Brushes.White,
                FontWeight = FontWeights.Medium
            };
            stack.Children.Add(labelText);

            valueText = new TextBlock
            {
                Text = "0",
                FontSize = 20,
                FontWeight = FontWeights.SemiBold,
                Foreground = Brushes.White
            };
            stack.Children.Add(valueText);

            border.Child = stack;
            WinGrid.SetColumn(border, column);
            parent.Children.Add(border);
        }

        private void CreateFooter(WinGrid parent)
        {
            var footerGrid = new WinGrid { Margin = new Thickness(0, 20, 0, 0) };
            footerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            footerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // Status indicator
            var statusStack = new StackPanel { Orientation = Orientation.Horizontal };

            _cacheStatusDot = new Ellipse
            {
                Width = 8,
                Height = 8,
                Fill = Brushes.Gray,
                Margin = new Thickness(0, 0, 6, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            statusStack.Children.Add(_cacheStatusDot);

            _cacheStatusText = new TextBlock
            {
                Text = "Data Status",
                FontSize = 12,
                Foreground = Brushes.Gray,
                VerticalAlignment = VerticalAlignment.Center
            };
            statusStack.Children.Add(_cacheStatusText);

            WinGrid.SetColumn(statusStack, 0);
            footerGrid.Children.Add(statusStack);

            // Action buttons
            var buttonStack = new StackPanel { Orientation = Orientation.Horizontal };

            var settingsBtn = CreateAppleButton("Settings", Colors.Transparent);
            settingsBtn.Foreground = new SolidColorBrush(WinColor.FromRgb(0, 122, 255));
            settingsBtn.BorderBrush = new SolidColorBrush(WinColor.FromRgb(0, 122, 255));
            settingsBtn.BorderThickness = new Thickness(1);
            settingsBtn.Margin = new Thickness(0, 0, 8, 0);
            settingsBtn.Click += OpenSettings_Click;
            buttonStack.Children.Add(settingsBtn);

            var refreshBtn = CreateAppleButton("Refresh Data", Colors.Transparent);
            refreshBtn.Foreground = new SolidColorBrush(WinColor.FromRgb(0, 122, 255));
            refreshBtn.BorderBrush = new SolidColorBrush(WinColor.FromRgb(0, 122, 255));
            refreshBtn.BorderThickness = new Thickness(1);
            refreshBtn.Margin = new Thickness(0, 0, 8, 0);
            refreshBtn.Click += RefreshData_Click;
            buttonStack.Children.Add(refreshBtn);

            var closeBtn = CreateAppleButton("Close", WinColor.FromRgb(0, 122, 255));
            closeBtn.Click += Close_Click;
            buttonStack.Children.Add(closeBtn);

            WinGrid.SetColumn(buttonStack, 1);
            footerGrid.Children.Add(buttonStack);

            WinGrid.SetRow(footerGrid, 2);
            parent.Children.Add(footerGrid);
        }

        private void DashboardWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // Ctrl+R to refresh data
            if (e.Key == System.Windows.Input.Key.R &&
                (System.Windows.Input.Keyboard.Modifiers & System.Windows.Input.ModifierKeys.Control) == System.Windows.Input.ModifierKeys.Control)
            {
                RefreshData_Click(sender, e);
                e.Handled = true;
                Logger.Info("Data refresh triggered by keyboard shortcut (Ctrl+R)");
            }
        }

        private void DashboardWindow_Loaded(object sender, RoutedEventArgs e)
        {
            Logger.Info("Loading dashboard data");

            // Load initial data
            LoadAllData();

            // Add to recent activity
            _recentActivityList.Items.Insert(0, $"Dashboard opened at {DateTime.Now.ToString("HH:mm:ss")}");
        }
        private void LoadAllData(bool useCache = true)
        {
            try
            {
                // Start timing for performance measurement
                var startTime = DateTime.Now;

                // Store current document path for cache tracking
                string documentPath = _doc?.PathName;
                bool isFromCache = false;
                // Check if analytics exist in cache before loading
                if (useCache && !string.IsNullOrEmpty(documentPath) &&
                    AppSettings.Instance.EnableDataCache)
                {
                    // Method to check if data is cached without direct field access
                    isFromCache = ProjectAnalytics.IsCached(documentPath);
                }

                // Generate analytics for the current document
                _projectAnalytics = ProjectAnalytics.AnalyzeProject(_doc, useCache);

                // Update UI with data
                LoadProjectInformation();
                LoadElementCounts();
                LoadProjectStatistics();

                // Calculate and show performance stats
                var duration = DateTime.Now - startTime;
                UpdateCacheStatus(isFromCache, duration.TotalMilliseconds);
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading dashboard data", ex);
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);

                // Update status on error
                UpdateCacheStatus(false, 0, true);
            }
        }

        /// <summary>
        /// Update the cache status indicator
        /// </summary>
        private void UpdateCacheStatus(bool fromCache, double durationMs, bool isError = false)
        {
            if (isError)
            {
                // Show error status
                _cacheStatusText.Text = "Data Load Error";
                _cacheStatusDot.Fill = Brushes.Red;
            }
            else if (fromCache)
            {
                // Show cached status with timing
                _cacheStatusText.Text = $"Cached Data ({durationMs:0} ms)";
                _cacheStatusDot.Fill = Brushes.Green;
            }
            else
            {
                // Show fresh data status with timing
                _cacheStatusText.Text = $"Fresh Data ({durationMs:0} ms)";
                _cacheStatusDot.Fill = Brushes.Blue;
            }
        }

        private void LoadProjectInformation()
        {
            try
            {
                // Update UI with project information from analytics
                _projectNameText.Text = $"Project Name: {_projectAnalytics.ProjectName}";
                _projectNumberText.Text = $"Project Number: {_projectAnalytics.ProjectNumber}";
                _projectAddressText.Text = $"Project Address: {_projectAnalytics.ProjectAddress}";
                _projectStatusText.Text = $"Project Status: {_projectAnalytics.ProjectStatus}";

                // Add to recent activity
                _recentActivityList.Items.Insert(0, $"Project information loaded: {_projectAnalytics.ProjectName}");
                Logger.Info($"Project information loaded: {_projectAnalytics.ProjectName}");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading project information", ex);
                MessageBox.Show($"Error loading project information: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadElementCounts()
        {
            try
            {
                // Update UI with element counts from analytics
                _wallsCountText.Text = $"Walls: {_projectAnalytics.WallCount}";
                _doorsCountText.Text = $"Doors: {_projectAnalytics.DoorCount}";
                _windowsCountText.Text = $"Windows: {_projectAnalytics.WindowCount}";
                _roomsCountText.Text = $"Rooms: {_projectAnalytics.RoomCount}";

                // Add to recent activity
                _recentActivityList.Items.Insert(0, "Element counts loaded");
                Logger.Info("Element counts loaded");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading element counts", ex);
                MessageBox.Show($"Error loading element counts: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadProjectStatistics()
        {
            try
            {
                // Update UI with project statistics from analytics
                _totalElementsText.Text = _projectAnalytics.TotalElements.ToString();
                _fileSizeText.Text = _projectAnalytics.FileSize;
                _lastSaveText.Text = _projectAnalytics.LastSaveTime;
                _worksetsText.Text = _projectAnalytics.WorksetCount.ToString();

                // Add to recent activity
                _recentActivityList.Items.Insert(0, "Project statistics loaded");
                Logger.Info("Project statistics loaded");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading project statistics", ex);
                MessageBox.Show($"Error loading project statistics: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #region Button Event Handlers

        private void ViewProjectDetails_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("View Project Details clicked");
            MessageBox.Show("Project details functionality to be implemented.", "Coming Soon");
        }

        private void AnalyzeElements_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("Analyze Elements clicked");
            MessageBox.Show("Element analysis functionality to be implemented.", "Coming Soon");
        }

        private void ViewAllActivity_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("View All Activity clicked");
            MessageBox.Show("Activity history functionality to be implemented.", "Coming Soon");
        }

        private void OpenSettings_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Info("Opening settings window");
                var settingsWindow = new SettingsWindow(_uiDoc);
                settingsWindow.Owner = this;
                settingsWindow.ShowDialog();

                // Refresh data after settings change
                Logger.Info("Refreshing data after settings change");
                LoadAllData();
            }
            catch (Exception ex)
            {
                Logger.Error("Error opening settings", ex);
                MessageBox.Show($"Error opening settings: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenHelp_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("Help requested");
            MessageBox.Show("Help documentation will be displayed here.\n\nFor more information, visit our documentation website.", "Help");
        }

        private void GenerateReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Info("Generating project summary report");
                ReportGenerator.GenerateProjectSummaryReport(_doc);
                _recentActivityList.Items.Insert(0, $"Project summary report generated at {DateTime.Now.ToString("HH:mm:ss")}");
                MessageBox.Show("Project summary report generated successfully!", "Report Generated", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Logger.Error("Error generating report", ex);
                MessageBox.Show($"Error generating report: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OpenAbout_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("About dialog opened");

            string version = ResourceHelper.GetAssemblyProductVersion();
            MessageBox.Show($"RevitAddIn2025\nVersion {version}\n\n© 2025 Your Company Name\n\nA modern Revit add-in with an Apple-inspired UI", "About RevitAddIn2025");
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Info("Manually refreshing data");

                // Reload all data
                LoadAllData();

                // Add to recent activity
                _recentActivityList.Items.Insert(0, $"Data refreshed at {DateTime.Now.ToString("HH:mm:ss")}");
            }
            catch (Exception ex)
            {
                Logger.Error("Error refreshing data", ex);
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            Logger.Info("Dashboard closing");
            this.Close();
        }

        #endregion
    }
}
