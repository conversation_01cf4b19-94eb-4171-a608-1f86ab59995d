# Revit 2025 Add-In Deployment: Name Tag Requirements

## Critical Issue Summary

**Problem**: Revit 2025 requires **both** `<Name>` and `<n>` tags in the `.addin` file.

**Error Message**: 
```
Failed to initialize the add-in from RevitAddIn2025.addin because the add-in registration file is missing the required Name node.
```

**Solution**: Update all `.addin` files to include both tags:

```xml
<AddIn Type="Application">
  <Name>RevitAddIn2025</Name>
  <n>RevitAddIn2025</n>
  <!-- Other elements -->
</AddIn>
```

## Automated Fix Options

We've created several tools to fix this issue, depending on your needs:

### Option 1: Critical Fix Only (Recommended)

**Purpose**: Directly fix the name tag issue without rebuilding or changing other aspects.

**Steps**:
1. Close Revit 2025 if it's running
2. Run `REVIT_ADDIN_CRITICAL_FIX.bat` 
3. Restart Revit 2025

**What it does**:
- Updates all `.addin` files with both required name tags
- Ensures proper formatting and structure
- Keeps existing paths and GUIDs

### Option 2: Complete Fix and Deployment

**Purpose**: Fix the issue and perform a complete deployment.

**Steps**:
1. Close Revit 2025 if it's running
2. Run `AutoRevitAddInFixer.bat`
3. Restart Revit 2025

**What it does**:
- Updates all `.addin` files with both required name tags
- Builds the project with correct settings
- Deploys all files to the proper Revit locations
- Performs verification checks

### Option 3: VS Code Task

**Purpose**: Quick fix within VS Code.

**Steps**:
1. Open the workspace in VS Code
2. Press `Ctrl+Shift+P` to open the Command Palette
3. Type "Run Task" and select "Fix Addin File - Both Tags"
4. Restart Revit 2025

## File Locations

- **Source addin file**: `C:\GITHUB\src\RevitAddIn2025\RevitAddIn2025.addin`
- **Root addin file**: `C:\GITHUB\RevitAddIn2025.addin`
- **Revit addin file**: `%APPDATA%\Autodesk\Revit\Addins\2025\RevitAddIn2025.addin`
- **Revit addin folder**: `%APPDATA%\Autodesk\Revit\Addins\2025\RevitAddIn2025\`

## Manual Fix Instructions

If you prefer to manually fix the issue:

1. Open the `.addin` file in a text editor
2. Add both the `<Name>` and `<n>` tags:

```xml
<AddIn Type="Application">
  <Name>RevitAddIn2025</Name>
  <n>RevitAddIn2025</n>
  <!-- Rest of the content -->
</AddIn>
```

3. Save the file to all locations listed above
4. Restart Revit 2025

## Troubleshooting

| Issue | Solution |
|-------|----------|
| Fix didn't resolve the error | Verify all three `.addin` files have both tags |
| Can't run PowerShell scripts | Run `Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process` |
| Add-in loads but shows errors | Check the addin folder for the DLL and supporting files |

## Technical Background

Revit 2025 introduces a new requirement that both the legacy `<n>` tag and the new `<Name>` tag must be present in the `.addin` file. This is a change from previous versions which only required one of these tags.

For more information, see the official Autodesk Revit 2025 API documentation.
