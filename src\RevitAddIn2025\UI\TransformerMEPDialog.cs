using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;
using System.Windows.Media.Effects;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.AI;
using RevitAddIn2025.AI.Transformer;
using RevitAddIn2025.Commands;
using RevitAddIn2025.Models;
using WinGrid = System.Windows.Controls.Grid;
using WinColor = System.Windows.Media.Color;
using RevitMEPSystemType = Autodesk.Revit.DB.MEPSystemType;

namespace RevitAddIn2025.UI
{
    /// <summary>
    /// Modern WPF dialog for MEP Transformer AI system
    /// </summary>
    public partial class TransformerMEPDialog : Window
    {
        private readonly UIApplication _uiApp;
        private readonly MEPTransformerModel _transformerModel;
        private DispatcherTimer _realTimeTimer;
        private bool _isAnalyzing = false;

        public TransformerMEPDialog(UIApplication uiApp, MEPTransformerModel transformerModel)
        {
            _uiApp = uiApp ?? throw new ArgumentNullException(nameof(uiApp));
            _transformerModel = transformerModel ?? throw new ArgumentNullException(nameof(transformerModel));

            InitializeComponent();
            SetupRealTimeUpdates();
            ApplyModernStyling();
        }

        private void InitializeComponent()
        {
            // Window properties
            Title = "MEP Transformer AI - Coordination & Optimization";
            Width = 1200;
            Height = 800;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            ResizeMode = ResizeMode.CanResize;
            WindowState = WindowState.Normal;

            // Create main grid
            var mainGrid = new WinGrid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) }); // Header
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // Content
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(80) }); // Footer

            // Header
            var headerPanel = CreateHeaderPanel();
            WinGrid.SetRow(headerPanel, 0);
            mainGrid.Children.Add(headerPanel);

            // Main content area
            var contentPanel = CreateContentPanel();
            WinGrid.SetRow(contentPanel, 1);
            mainGrid.Children.Add(contentPanel);

            // Footer with controls
            var footerPanel = CreateFooterPanel();
            WinGrid.SetRow(footerPanel, 2);
            mainGrid.Children.Add(footerPanel);

            Content = mainGrid;
        }

        private System.Windows.Controls.Panel CreateHeaderPanel()
        {
            var header = new WinGrid();
            header.Background = new SolidColorBrush(WinColor.FromRgb(30, 30, 30));

            var titleLabel = new Label
            {
                Content = "🧠 MEP Transformer AI",
                Foreground = Brushes.White,
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            var statusLabel = new Label
            {
                Name = "StatusLabel",
                Content = "Ready for AI Analysis",
                Foreground = new SolidColorBrush(WinColor.FromRgb(0, 255, 127)),
                FontSize = 14,
                HorizontalAlignment = HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 20, 0)
            };

            header.Children.Add(titleLabel);
            header.Children.Add(statusLabel);

            return header;
        }

        private System.Windows.Controls.Panel CreateContentPanel()
        {
            var contentPanel = new WinGrid();
            contentPanel.Background = Brushes.WhiteSmoke;

            // Create column definitions for main layout
            contentPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            contentPanel.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Left panel - System Overview
            var leftPanel = CreateSystemOverviewPanel();
            WinGrid.SetColumn(leftPanel, 0);

            // Right panel - Analysis Results
            var rightPanel = CreateAnalysisResultsPanel();
            WinGrid.SetColumn(rightPanel, 1);

            contentPanel.Children.Add(leftPanel);
            contentPanel.Children.Add(rightPanel);

            return contentPanel;
        }

        private System.Windows.Controls.Panel CreateSystemOverviewPanel()
        {
            // Create a stack panel with system overview
            var panel = new StackPanel
            {
                Margin = new Thickness(15),
                Background = Brushes.White
            };

            // Add title
            panel.Children.Add(new TextBlock
            {
                Text = "MEP System Classification",
                FontSize = 18,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 15),
                Foreground = new SolidColorBrush(WinColor.FromRgb(40, 40, 40))
            });

            // Add system type visualization
            var systemPanel = new StackPanel();

            // We'll populate this in UpdateSystemOverview()
            systemPanel.Name = "SystemOverviewPanel";

            panel.Children.Add(systemPanel);

            // Add stats section
            panel.Children.Add(new TextBlock
            {
                Text = "System Statistics",
                FontSize = 18,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 20, 0, 15),
                Foreground = new SolidColorBrush(WinColor.FromRgb(40, 40, 40))
            });

            // Stats grid - will be updated with data
            var statsGrid = new WinGrid();
            statsGrid.Name = "SystemStatsGrid";
            statsGrid.Margin = new Thickness(0, 0, 0, 15);
            panel.Children.Add(statsGrid);

            return panel;
        }

        private System.Windows.Controls.Panel CreateAnalysisResultsPanel()
        {
            // Create a stack panel with analysis results
            var panel = new StackPanel
            {
                Margin = new Thickness(15),
                Background = Brushes.White
            };

            // Add title
            panel.Children.Add(new TextBlock
            {
                Text = "AI Analysis Results",
                FontSize = 18,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 15),
                Foreground = new SolidColorBrush(WinColor.FromRgb(40, 40, 40))
            });

            // Add tab control for different analysis views
            var tabControl = new TabControl();
            tabControl.Name = "AnalysisTabControl";

            // Clash detection tab
            var clashTab = new TabItem { Header = "Clash Detection" };
            var clashPanel = new StackPanel();
            clashPanel.Name = "ClashPanel";
            clashTab.Content = clashPanel;

            // Energy efficiency tab
            var energyTab = new TabItem { Header = "Energy Efficiency" };
            var energyPanel = new StackPanel();
            energyPanel.Name = "EnergyPanel";
            energyTab.Content = energyPanel;

            // Code compliance tab
            var codeTab = new TabItem { Header = "Code Compliance" };
            var codePanel = new StackPanel();
            codePanel.Name = "CodePanel";
            codeTab.Content = codePanel;

            tabControl.Items.Add(clashTab);
            tabControl.Items.Add(energyTab);
            tabControl.Items.Add(codeTab);

            panel.Children.Add(tabControl);
            // Add status text
            var statusText = new TextBlock
            {
                Text = "Ready to analyze. Click 'Run Analysis' to begin.",
                FontStyle = FontStyles.Italic,
                Margin = new Thickness(0, 15, 0, 0),
                Name = "AnalysisStatusText"
            };
            panel.Children.Add(statusText);

            return panel;
        }

        private System.Windows.Controls.Panel CreateFooterPanel()
        {
            var footer = new WinGrid();
            footer.Background = new SolidColorBrush(WinColor.FromRgb(240, 240, 240));

            // Progress bar for analysis
            var progressBar = new ProgressBar
            {
                Name = "AnalysisProgressBar",
                Height = 10,
                Margin = new Thickness(20, 10, 20, 5),
                Visibility = System.Windows.Visibility.Collapsed
            };

            var progressText = new TextBlock
            {
                Name = "ProgressText",
                Text = "Ready",
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(20, 0, 0, 15),
                Foreground = Brushes.DimGray
            };

            // Button panel
            var buttonPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 0, 20, 15)
            };

            // Run Analysis button
            var analyzeButton = new Button
            {
                Name = "RunAnalysisButton",
                Content = "Run Analysis",
                Padding = new Thickness(15, 8, 15, 8),
                Margin = new Thickness(0, 0, 10, 0),
                Background = new SolidColorBrush(WinColor.FromRgb(0, 122, 204)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0)
            };
            analyzeButton.Click += OnRunAnalysisClicked;

            // Close button
            var closeButton = new Button
            {
                Content = "Close",
                Padding = new Thickness(15, 8, 15, 8),
                Background = new SolidColorBrush(WinColor.FromRgb(240, 240, 240)),
                Foreground = new SolidColorBrush(WinColor.FromRgb(60, 60, 60)),
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(200, 200, 200))
            };
            closeButton.Click += (sender, e) => Close();

            buttonPanel.Children.Add(analyzeButton);
            buttonPanel.Children.Add(closeButton);

            footer.Children.Add(progressBar);
            footer.Children.Add(progressText);
            footer.Children.Add(buttonPanel);

            return footer;
        }

        private void ApplyModernStyling()
        {
            // Add modern styling - similar to Apple's clean design
            var windowStyle = new Style(typeof(Window));
            windowStyle.Setters.Add(new Setter(BackgroundProperty, Brushes.WhiteSmoke));

            // Apply style
            Style = windowStyle;
        }



        private void SetupRealTimeUpdates()
        {
            _realTimeTimer = new DispatcherTimer();
            _realTimeTimer.Interval = TimeSpan.FromSeconds(5);
            _realTimeTimer.Tick += OnRealTimeUpdateTick;
            _realTimeTimer.Start();
        }

        private void OnRealTimeUpdateTick(object sender, EventArgs e)
        {
            // Update status if not actively analyzing
            if (!_isAnalyzing)
            {
                UpdateStatusText("Monitoring for changes...");
            }
        }

        private async void OnRunAnalysisClicked(object sender, RoutedEventArgs e)
        {
            if (_isAnalyzing)
            {
                MessageBox.Show("Analysis is already running. Please wait for it to complete.", "Operation in Progress");
                return;
            }

            try
            {
                _isAnalyzing = true;
                UpdateStatusText("Starting MEP analysis...");
                ShowProgressBar(true);

                // Get progress bar and button
                var progressBar = FindChildByName<ProgressBar>(this, "AnalysisProgressBar");
                var analyzeButton = FindChildByName<Button>(this, "RunAnalysisButton");
                if (analyzeButton != null)
                {
                    analyzeButton.IsEnabled = false;
                }

                // Run the analysis using the transformer model
                var elements = new FilteredElementCollector(_uiApp.ActiveUIDocument.Document)
                    .WhereElementIsNotElementType()
                    .ToElements()
                    .Take(100) // Limit for demo
                    .ToList();

                // Convert Elements to MEPElementData
                var mepElements = elements.Select(e => new Models.MEPElementData(e)).ToList();
                var analysisResults = await _transformerModel.AnalyzeMEPSystemsAsync(mepElements);

                // Update UI with placeholder results
                UpdateStatusText($"Analysis complete! Processed {elements.Count} elements.");

                UpdateStatusText("Analysis complete");
                ShowProgressBar(false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error during analysis: {ex.Message}", "Analysis Error");
                UpdateStatusText($"Error: {ex.Message}");
            }
            finally
            {
                _isAnalyzing = false;

                // Re-enable the analyze button
                var analyzeButton = FindChildByName<Button>(this, "RunAnalysisButton");
                if (analyzeButton != null)
                {
                    analyzeButton.IsEnabled = true;
                }
            }
        }

        private void UpdateSystemOverview()
        {
            // Find the system overview panel
            var systemPanel = FindChildByName<StackPanel>(this, "SystemOverviewPanel");
            if (systemPanel == null) return;

            systemPanel.Children.Clear();

            // Add placeholder content
            var summaryText = new TextBlock
            {
                Text = "System overview will be displayed here after analysis",
                Margin = new Thickness(0, 0, 0, 15),
                FontWeight = FontWeights.SemiBold
            };
            systemPanel.Children.Add(summaryText);
        }

        private SolidColorBrush GetSystemTypeColor(RevitAddIn2025.AI.Transformer.MEPSystemType systemType)
        {
            switch (systemType)
            {
                case RevitAddIn2025.AI.Transformer.MEPSystemType.Mechanical:
                    return new SolidColorBrush(WinColor.FromRgb(30, 144, 255)); // DodgerBlue
                case RevitAddIn2025.AI.Transformer.MEPSystemType.Plumbing:
                    return new SolidColorBrush(WinColor.FromRgb(60, 179, 113)); // MediumSeaGreen
                case RevitAddIn2025.AI.Transformer.MEPSystemType.Electrical:
                    return new SolidColorBrush(WinColor.FromRgb(255, 140, 0)); // DarkOrange
                case RevitAddIn2025.AI.Transformer.MEPSystemType.FireProtection:
                    return new SolidColorBrush(WinColor.FromRgb(255, 0, 0)); // Red
                default:
                    return new SolidColorBrush(WinColor.FromRgb(169, 169, 169)); // DarkGray
            }
        }

        private string FormatSystemTypeName(string systemTypeName)
        {
            if (string.IsNullOrEmpty(systemTypeName)) return "Unknown";

            // Split by uppercase letters and join with spaces
            return string.Concat(systemTypeName.Select(c => char.IsUpper(c) && systemTypeName.IndexOf(c) > 0 ? " " + c : c.ToString()));
        }

        private void AddStatsRow(WinGrid grid, int row, string label, string value, Brush valueBrush = null)
        {
            var labelText = new TextBlock
            {
                Text = label,
                Margin = new Thickness(0, 5, 0, 5),
                VerticalAlignment = VerticalAlignment.Center
            };
            WinGrid.SetRow(labelText, row);
            WinGrid.SetColumn(labelText, 0);

            var valueText = new TextBlock
            {
                Text = value,
                HorizontalAlignment = HorizontalAlignment.Right,
                Margin = new Thickness(0, 5, 0, 5),
                VerticalAlignment = VerticalAlignment.Center,
                FontWeight = FontWeights.Bold
            };

            if (valueBrush != null)
            {
                valueText.Foreground = valueBrush;
            }

            WinGrid.SetRow(valueText, row);
            WinGrid.SetColumn(valueText, 1);

            grid.Children.Add(labelText);
            grid.Children.Add(valueText);
        }

        private void UpdateClashResults()
        {
            var clashPanel = FindChildByName<StackPanel>(this, "ClashPanel");
            if (clashPanel == null) return;

            clashPanel.Children.Clear();

            // Add placeholder summary
            var summaryText = new TextBlock
            {
                Text = "Clash detection results will be displayed here after analysis",
                Margin = new Thickness(0, 0, 0, 15),
                FontWeight = FontWeights.SemiBold
            };
            clashPanel.Children.Add(summaryText);
        }

        private void UpdateEnergyEfficiency()
        {
            var energyPanel = FindChildByName<StackPanel>(this, "EnergyPanel");
            if (energyPanel == null) return;

            energyPanel.Children.Clear();

            // Add placeholder summary
            var summaryText = new TextBlock
            {
                Text = "Energy efficiency analysis will be displayed here after analysis",
                Margin = new Thickness(0, 0, 0, 15),
                FontWeight = FontWeights.SemiBold
            };
            energyPanel.Children.Add(summaryText);
        }

        private void AddSuggestionItem(StackPanel panel, string text, bool highlight = false)
        {
            var suggestionItem = new WinGrid();
            suggestionItem.Margin = new Thickness(0, 5, 0, 5);

            suggestionItem.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(20) });
            suggestionItem.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

            // Bullet point
            var bullet = new TextBlock
            {
                Text = "•",
                FontSize = 16,
                VerticalAlignment = VerticalAlignment.Top
            };
            WinGrid.SetColumn(bullet, 0);

            // Suggestion text
            var suggestionText = new TextBlock
            {
                Text = text,
                TextWrapping = TextWrapping.Wrap,
                VerticalAlignment = VerticalAlignment.Center
            };

            if (highlight)
            {
                suggestionText.FontWeight = FontWeights.SemiBold;
                suggestionText.Foreground = Brushes.DarkGreen;
            }

            WinGrid.SetColumn(suggestionText, 1);

            suggestionItem.Children.Add(bullet);
            suggestionItem.Children.Add(suggestionText);

            panel.Children.Add(suggestionItem);
        }

        private void UpdateCodeCompliance()
        {
            var codePanel = FindChildByName<StackPanel>(this, "CodePanel");
            if (codePanel == null) return;

            codePanel.Children.Clear();

            // Add placeholder summary
            var summaryText = new TextBlock
            {
                Text = "Code compliance analysis will be displayed here after analysis",
                Margin = new Thickness(0, 0, 0, 15),
                FontWeight = FontWeights.SemiBold
            };
            codePanel.Children.Add(summaryText);
        }

        private void AddComplianceIssueSection(StackPanel panel, string title)
        {
            var titleText = new TextBlock
            {
                Text = title,
                Margin = new Thickness(0, 15, 0, 10),
                FontSize = 14,
                FontWeight = FontWeights.Medium
            };
            panel.Children.Add(titleText);

            // Add placeholder content
            var placeholderText = new TextBlock
            {
                Text = "Compliance issues will be displayed here after analysis",
                Margin = new Thickness(0, 5, 0, 5),
                FontStyle = FontStyles.Italic
            };
            panel.Children.Add(placeholderText);
        }

        private void UpdateStatusText(string message)
        {
            var statusLabel = FindChildByName<Label>(this, "StatusLabel");
            var progressText = FindChildByName<TextBlock>(this, "ProgressText");

            if (statusLabel != null)
            {
                statusLabel.Content = message;
            }

            if (progressText != null)
            {
                progressText.Text = message;
            }
        }

        private void ShowProgressBar(bool show)
        {
            var progressBar = FindChildByName<ProgressBar>(this, "AnalysisProgressBar");
            if (progressBar != null)
            {
                progressBar.Visibility = show ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
                if (show)
                {
                    progressBar.Value = 0;
                }
            }
        }

        private T FindChildByName<T>(DependencyObject parent, string name) where T : DependencyObject
        {
            if (parent == null) return null;

            int childCount = VisualTreeHelper.GetChildrenCount(parent);

            for (int i = 0; i < childCount; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is FrameworkElement element && element.Name == name && child is T typedChild)
                {
                    return typedChild;
                }

                var result = FindChildByName<T>(child, name);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }
    }
}
