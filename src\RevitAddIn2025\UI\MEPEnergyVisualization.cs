using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using RevitAddIn2025.Models;
using RevitAddIn2025.AI.Transformer;

namespace RevitAddIn2025.UI
{
    // Placeholder classes for energy optimization
    public class OptimizationResults
    {
        public double OverallSavingsPotential { get; set; }
        public List<OptimizationRecommendation> Recommendations { get; set; } = new List<OptimizationRecommendation>();
        public List<string> AIRecommendations { get; set; } = new List<string>();
    }

    public class OptimizationRecommendation
    {
        public Models.MEPSystemType SystemType { get; set; }
        public string Description { get; set; } = "";
        public string RecommendedAction { get; set; } = "";
        public double EnergySavingsPotential { get; set; }
        public ImplementationDifficulty ImplementationDifficulty { get; set; }
        public bool AIRecommended { get; set; }
    }

    public enum ImplementationDifficulty
    {
        Easy = 0,
        Medium = 1,
        Hard = 2,
        VeryHard = 3
    }

    /// <summary>
    /// Visualization components for MEP energy analysis
    /// </summary>
    public static class MEPEnergyVisualization
    {
        /// <summary>
        /// Creates a panel displaying energy optimization recommendations
        /// </summary>
        public static StackPanel CreateEnergyRecommendationsPanel(OptimizationResults results)
        {
            var panel = new StackPanel
            {
                Margin = new Thickness(10)
            };

            // Overall savings potential
            var savingsPanel = new Grid();
            savingsPanel.Margin = new Thickness(0, 0, 0, 20);

            // Create circular progress indicator for savings
            var savingsIndicator = CreateSavingsIndicator(results.OverallSavingsPotential);
            savingsPanel.Children.Add(savingsIndicator);

            panel.Children.Add(savingsPanel);

            // Recommendations group
            var recommendationsTitle = new TextBlock
            {
                Text = "Energy Optimization Recommendations",
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            panel.Children.Add(recommendationsTitle);

            // Sort recommendations by savings potential
            var sortedRecommendations = results.Recommendations
                .OrderByDescending(r => r.EnergySavingsPotential)
                .ToList();

            // Create recommendation cards
            foreach (var recommendation in sortedRecommendations)
            {
                panel.Children.Add(CreateRecommendationCard(recommendation));
            }

            // Add AI recommendations if available
            if (results.AIRecommendations != null && results.AIRecommendations.Count > 0)
            {
                var aiTitle = new TextBlock
                {
                    Text = "AI-Generated Recommendations",
                    FontSize = 16,
                    FontWeight = FontWeights.SemiBold,
                    Margin = new Thickness(0, 20, 0, 10)
                };
                panel.Children.Add(aiTitle);

                foreach (var aiRecommendation in results.AIRecommendations)
                {
                    var aiCard = new Border
                    {
                        BorderBrush = new SolidColorBrush(Color.FromRgb(200, 200, 200)),
                        BorderThickness = new Thickness(1),
                        CornerRadius = new CornerRadius(4),
                        Padding = new Thickness(15),
                        Margin = new Thickness(0, 0, 0, 10),
                        Background = new SolidColorBrush(Color.FromRgb(245, 245, 255))
                    };

                    var aiStackPanel = new StackPanel();

                    // AI icon
                    var aiHeader = new StackPanel
                    {
                        Orientation = Orientation.Horizontal,
                        Margin = new Thickness(0, 0, 0, 5)
                    };

                    var aiIcon = new TextBlock
                    {
                        Text = "🧠",
                        FontSize = 16,
                        Margin = new Thickness(0, 0, 8, 0),
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    var aiLabel = new TextBlock
                    {
                        Text = "AI Recommendation",
                        FontWeight = FontWeights.SemiBold,
                        VerticalAlignment = VerticalAlignment.Center
                    };

                    aiHeader.Children.Add(aiIcon);
                    aiHeader.Children.Add(aiLabel);
                    aiStackPanel.Children.Add(aiHeader);

                    // AI recommendation text
                    var aiText = new TextBlock
                    {
                        Text = aiRecommendation,
                        TextWrapping = TextWrapping.Wrap
                    };
                    aiStackPanel.Children.Add(aiText);

                    aiCard.Child = aiStackPanel;
                    panel.Children.Add(aiCard);
                }
            }

            return panel;
        }

        /// <summary>
        /// Creates a visual savings indicator
        /// </summary>
        private static UIElement CreateSavingsIndicator(double savingsPotential)
        {
            // Create container
            var grid = new Grid
            {
                Width = 200,
                Height = 200,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // Background circle
            var backgroundCircle = new Ellipse
            {
                Width = 200,
                Height = 200,
                Fill = new SolidColorBrush(Color.FromRgb(240, 240, 240)),
                Stroke = new SolidColorBrush(Color.FromRgb(200, 200, 200)),
                StrokeThickness = 1
            };
            grid.Children.Add(backgroundCircle);

            // Progress arc
            const double startAngle = -90;
            double endAngle = startAngle + (360 * savingsPotential);

            bool isLargeArc = savingsPotential > 0.5;

            var center = new Point(100, 100);
            double radius = 90;

            var startPoint = new Point(
                center.X + radius * Math.Cos(startAngle * Math.PI / 180),
                center.Y + radius * Math.Sin(startAngle * Math.PI / 180));

            var endPoint = new Point(
                center.X + radius * Math.Cos(endAngle * Math.PI / 180),
                center.Y + radius * Math.Sin(endAngle * Math.PI / 180));

            var arcSegment = new ArcSegment
            {
                Point = endPoint,
                Size = new Size(radius, radius),
                IsLargeArc = isLargeArc,
                SweepDirection = SweepDirection.Clockwise
            };

            var pathFigure = new PathFigure
            {
                StartPoint = startPoint,
                IsClosed = false
            };
            pathFigure.Segments.Add(arcSegment);

            var pathGeometry = new PathGeometry();
            pathGeometry.Figures.Add(pathFigure);

            // Color based on savings potential
            Color progressColor;
            if (savingsPotential < 0.1)
                progressColor = Color.FromRgb(180, 180, 180); // Gray
            else if (savingsPotential < 0.2)
                progressColor = Color.FromRgb(255, 204, 0); // Gold
            else
                progressColor = Color.FromRgb(34, 139, 34); // Green

            var progressPath = new Path
            {
                Data = pathGeometry,
                Stroke = new SolidColorBrush(progressColor),
                StrokeThickness = 15,
                StrokeEndLineCap = PenLineCap.Round,
                StrokeStartLineCap = PenLineCap.Round
            };
            grid.Children.Add(progressPath);

            // Inner circle
            var innerCircle = new Ellipse
            {
                Width = 160,
                Height = 160,
                Fill = new SolidColorBrush(Colors.White),
                Margin = new Thickness(20)
            };
            grid.Children.Add(innerCircle);

            // Percentage text
            var textPanel = new StackPanel
            {
                VerticalAlignment = VerticalAlignment.Center,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            var percentageText = new TextBlock
            {
                Text = $"{savingsPotential:P0}",
                FontSize = 36,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Foreground = new SolidColorBrush(progressColor)
            };
            textPanel.Children.Add(percentageText);

            var savingsLabel = new TextBlock
            {
                Text = "Potential Energy Savings",
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center,
                Foreground = new SolidColorBrush(Color.FromRgb(100, 100, 100))
            };
            textPanel.Children.Add(savingsLabel);

            grid.Children.Add(textPanel);

            return grid;
        }

        /// <summary>
        /// Creates a recommendation card
        /// </summary>
        private static UIElement CreateRecommendationCard(OptimizationRecommendation recommendation)
        {
            var card = new Border
            {
                BorderBrush = new SolidColorBrush(Color.FromRgb(220, 220, 220)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(4),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 0, 10)
            };

            // Set background based on savings potential
            if (recommendation.EnergySavingsPotential >= 0.2)
                card.Background = new SolidColorBrush(Color.FromRgb(240, 255, 240)); // Light green
            else if (recommendation.EnergySavingsPotential >= 0.1)
                card.Background = new SolidColorBrush(Color.FromRgb(255, 255, 240)); // Light yellow
            else
                card.Background = new SolidColorBrush(Color.FromRgb(250, 250, 250)); // Almost white

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // Left side - recommendation details
            var detailsPanel = new StackPanel();

            // System type
            var systemTypeText = new TextBlock
            {
                Text = FormatSystemTypeName(recommendation.SystemType.ToString()),
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 5)
            };
            detailsPanel.Children.Add(systemTypeText);

            // Description
            var descriptionText = new TextBlock
            {
                Text = recommendation.Description,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 10)
            };
            detailsPanel.Children.Add(descriptionText);

            // Recommendation
            var recommendationPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal
            };

            var recommendationText = new TextBlock
            {
                Text = recommendation.RecommendedAction,
                TextWrapping = TextWrapping.Wrap,
                FontStyle = FontStyles.Italic
            };

            recommendationPanel.Children.Add(recommendationText);

            // AI badge if AI recommended
            if (recommendation.AIRecommended)
            {
                var aiBadge = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(100, 100, 240)),
                    CornerRadius = new CornerRadius(3),
                    Padding = new Thickness(5, 2, 5, 2),
                    Margin = new Thickness(10, 0, 0, 0)
                };

                aiBadge.Child = new TextBlock
                {
                    Text = "AI",
                    FontSize = 10,
                    FontWeight = FontWeights.Bold,
                    Foreground = Brushes.White
                };

                recommendationPanel.Children.Add(aiBadge);
            }

            detailsPanel.Children.Add(recommendationPanel);

            Grid.SetColumn(detailsPanel, 0);
            grid.Children.Add(detailsPanel);

            // Right side - savings and difficulty
            var statsPanel = new StackPanel
            {
                Margin = new Thickness(10, 0, 0, 0)
            };

            // Savings percentage
            var savingsText = new TextBlock
            {
                Text = $"{recommendation.EnergySavingsPotential:P0}",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Foreground = GetSavingsColor(recommendation.EnergySavingsPotential)
            };
            statsPanel.Children.Add(savingsText);

            // Difficulty indicator
            var difficultyPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 5, 0, 0)
            };

            var difficultyText = new TextBlock
            {
                Text = recommendation.ImplementationDifficulty.ToString(),
                FontSize = 10,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            difficultyPanel.Children.Add(difficultyText);
            statsPanel.Children.Add(difficultyPanel);

            // Difficulty dots
            var difficultyDotsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 3, 0, 0)
            };

            int difficultyLevel = (int)recommendation.ImplementationDifficulty + 1;
            for (int i = 1; i <= 4; i++)
            {
                var dot = new Ellipse
                {
                    Width = 6,
                    Height = 6,
                    Margin = new Thickness(2, 0, 2, 0),
                    Fill = i <= difficultyLevel ?
                        new SolidColorBrush(Color.FromRgb(100, 100, 100)) :
                        new SolidColorBrush(Color.FromRgb(220, 220, 220))
                };
                difficultyDotsPanel.Children.Add(dot);
            }

            statsPanel.Children.Add(difficultyDotsPanel);

            Grid.SetColumn(statsPanel, 1);
            grid.Children.Add(statsPanel);

            card.Child = grid;
            return card;
        }

        /// <summary>
        /// Creates a system savings chart
        /// </summary>
        public static UIElement CreateSystemSavingsChart(Dictionary<Models.MEPSystemType, double> systemSavings)
        {
            var panel = new StackPanel
            {
                Margin = new Thickness(10, 20, 10, 10)
            };

            var title = new TextBlock
            {
                Text = "Energy Savings by System Type",
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 15)
            };
            panel.Children.Add(title);

            // Sort by savings potential
            var sortedSavings = systemSavings
                .OrderByDescending(s => s.Value)
                .Take(6) // Show top systems
                .ToList();

            // Create bar chart
            double maxSavings = sortedSavings.Any() ? sortedSavings.Max(s => s.Value) : 0;
            maxSavings = Math.Max(maxSavings, 0.05); // Minimum scale

            foreach (var systemSaving in sortedSavings)
            {
                var chartRow = new Grid
                {
                    Margin = new Thickness(0, 0, 0, 10)
                };

                chartRow.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
                chartRow.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                chartRow.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(50) });

                // System name
                var systemName = new TextBlock
                {
                    Text = FormatSystemTypeName(systemSaving.Key.ToString()),
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetColumn(systemName, 0);
                chartRow.Children.Add(systemName);

                // Bar chart
                var barContainer = new Grid
                {
                    Height = 20,
                    VerticalAlignment = VerticalAlignment.Center
                };

                var barBackground = new Rectangle
                {
                    Fill = new SolidColorBrush(Color.FromRgb(240, 240, 240)),
                    RadiusX = 3,
                    RadiusY = 3,
                    Height = 20
                };
                barContainer.Children.Add(barBackground);

                var barWidth = (systemSaving.Value / maxSavings) * 100;

                var bar = new Rectangle
                {
                    Fill = GetSavingsColor(systemSaving.Value),
                    RadiusX = 3,
                    RadiusY = 3,
                    Height = 20,
                    HorizontalAlignment = HorizontalAlignment.Left,
                    Width = Math.Max(5, barWidth)
                };
                barContainer.Children.Add(bar);

                Grid.SetColumn(barContainer, 1);
                chartRow.Children.Add(barContainer);

                // Percentage
                var percentage = new TextBlock
                {
                    Text = $"{systemSaving.Value:P0}",
                    VerticalAlignment = VerticalAlignment.Center,
                    HorizontalAlignment = HorizontalAlignment.Right
                };
                Grid.SetColumn(percentage, 2);
                chartRow.Children.Add(percentage);

                panel.Children.Add(chartRow);
            }

            return panel;
        }

        /// <summary>
        /// Formats a system type name for display
        /// </summary>
        private static string FormatSystemTypeName(string systemTypeName)
        {
            if (string.IsNullOrEmpty(systemTypeName)) return "Unknown";

            // Split by uppercase letters and join with spaces
            return string.Concat(systemTypeName.Select(c => char.IsUpper(c) && systemTypeName.IndexOf(c) > 0 ? " " + c : c.ToString()));
        }

        /// <summary>
        /// Gets a color based on savings potential
        /// </summary>
        private static Brush GetSavingsColor(double savingsPotential)
        {
            if (savingsPotential >= 0.25)
                return new SolidColorBrush(Color.FromRgb(0, 128, 0)); // Green
            else if (savingsPotential >= 0.15)
                return new SolidColorBrush(Color.FromRgb(34, 139, 34)); // Forest Green
            else if (savingsPotential >= 0.05)
                return new SolidColorBrush(Color.FromRgb(255, 165, 0)); // Orange
            else
                return new SolidColorBrush(Color.FromRgb(100, 100, 100)); // Gray
        }
    }
}
