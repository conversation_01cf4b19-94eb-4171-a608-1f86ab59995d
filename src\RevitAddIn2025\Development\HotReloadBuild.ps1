# Hot Reload Build Script for RevitAddIn2025
# This script builds the project and triggers hot reload

param(
    [switch]$Watch,
    [switch]$Clean,
    [string]$Configuration = "Debug"
)

# Configuration
$ProjectPath = Split-Path -Parent $PSScriptRoot
$ProjectFile = Join-Path $ProjectPath "RevitAddIn2025.csproj"
$OutputPath = Join-Path $ProjectPath "bin\$Configuration"
$TargetDLL = Join-Path $OutputPath "RevitAddIn2025.dll"
$RevitAddinsPath = "C:\ProgramData\Autodesk\Revit\Addins\2025"
$TargetPath = Join-Path $RevitAddinsPath "RevitAddIn2025_FIXED.dll"

Write-Host "🚀 RevitAddIn2025 Hot Reload Build System" -ForegroundColor Cyan
Write-Host "=========================================" -ForegroundColor Cyan

function Build-Project {
    Write-Host "🔨 Building project..." -ForegroundColor Yellow
    
    if ($Clean) {
        Write-Host "🧹 Cleaning previous build..." -ForegroundColor Gray
        dotnet clean $ProjectFile -c $Configuration
    }
    
    # Build the project
    $buildResult = dotnet build $ProjectFile -c $Configuration --no-restore
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Build successful!" -ForegroundColor Green
        return $true
    } else {
        Write-Host "❌ Build failed!" -ForegroundColor Red
        return $false
    }
}

function Copy-ToRevit {
    Write-Host "📁 Copying DLL to Revit addins folder..." -ForegroundColor Yellow
    
    try {
        # Copy main DLL
        Copy-Item $TargetDLL $TargetPath -Force
        
        # Copy PDB for debugging
        $sourcePDB = $TargetDLL.Replace(".dll", ".pdb")
        $targetPDB = $TargetPath.Replace(".dll", ".pdb")
        if (Test-Path $sourcePDB) {
            Copy-Item $sourcePDB $targetPDB -Force
        }
        
        Write-Host "✅ Files copied successfully!" -ForegroundColor Green
        Write-Host "📍 Target: $TargetPath" -ForegroundColor Gray
        return $true
    }
    catch {
        Write-Host "❌ Failed to copy files: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Show-BuildInfo {
    if (Test-Path $TargetDLL) {
        $fileInfo = Get-Item $TargetDLL
        $sizeKB = [math]::Round($fileInfo.Length / 1KB, 2)
        
        Write-Host "📊 Build Information:" -ForegroundColor Cyan
        Write-Host "   📁 Size: $sizeKB KB" -ForegroundColor Gray
        Write-Host "   ⏰ Modified: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
        Write-Host "   🎯 Configuration: $Configuration" -ForegroundColor Gray
    }
}

function Start-FileWatcher {
    Write-Host "👀 Starting file watcher for hot reload..." -ForegroundColor Cyan
    Write-Host "   📁 Watching: $ProjectPath" -ForegroundColor Gray
    Write-Host "   🔄 Auto-build on changes enabled" -ForegroundColor Gray
    Write-Host "   ⏹️  Press Ctrl+C to stop watching" -ForegroundColor Yellow
    
    $watcher = New-Object System.IO.FileSystemWatcher
    $watcher.Path = $ProjectPath
    $watcher.Filter = "*.cs"
    $watcher.IncludeSubdirectories = $true
    $watcher.EnableRaisingEvents = $true
    
    $action = {
        $path = $Event.SourceEventArgs.FullPath
        $changeType = $Event.SourceEventArgs.ChangeType
        $fileName = Split-Path $path -Leaf
        
        Write-Host "🔄 File changed: $fileName" -ForegroundColor Yellow
        
        # Debounce - wait a bit for multiple changes
        Start-Sleep -Milliseconds 500
        
        # Build and deploy
        if (Build-Project) {
            Copy-ToRevit
            Show-BuildInfo
            Write-Host "🎯 Hot reload ready! Test your changes in Revit." -ForegroundColor Green
        }
    }
    
    Register-ObjectEvent -InputObject $watcher -EventName "Changed" -Action $action
    
    try {
        while ($true) {
            Start-Sleep -Seconds 1
        }
    }
    finally {
        $watcher.EnableRaisingEvents = $false
        $watcher.Dispose()
        Write-Host "👋 File watcher stopped." -ForegroundColor Yellow
    }
}

# Main execution
try {
    # Initial build
    if (Build-Project) {
        Copy-ToRevit
        Show-BuildInfo
        
        Write-Host ""
        Write-Host "🎯 Hot Reload Setup Complete!" -ForegroundColor Green
        Write-Host "   1. Your plugin is ready for testing in Revit" -ForegroundColor Gray
        Write-Host "   2. Make code changes and run this script again" -ForegroundColor Gray
        Write-Host "   3. Or use -Watch parameter for automatic rebuilds" -ForegroundColor Gray
        
        if ($Watch) {
            Write-Host ""
            Start-FileWatcher
        } else {
            Write-Host ""
            Write-Host "💡 Tip: Use -Watch parameter for automatic rebuilds:" -ForegroundColor Cyan
            Write-Host "   .\HotReloadBuild.ps1 -Watch" -ForegroundColor Gray
        }
    }
}
catch {
    Write-Host "❌ Script failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
