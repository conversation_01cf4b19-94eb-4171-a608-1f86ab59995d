# 🤖 Claude <PERSON> + VSCode Integration Setup

## 🚀 Complete Setup Guide for RevitAddIn2025 Development

### **PART 1: HOT RELOAD DEVELOPMENT WORKFLOW**

#### **✅ SETUP COMPLETE:**
Your RevitAddIn2025 project now includes:

1. **🔥 Hot Reload Manager** - Automatically reloads commands without restarting Revit
2. **🔄 Dev Reload Command** - Manual hot reload trigger (Debug builds only)
3. **👀 File Watcher** - Auto-builds on code changes
4. **🎯 VSCode Integration** - Tasks and settings optimized for development

#### **🎮 HOW TO USE:**

**Method 1: Automatic Hot Reload**
```bash
# In VSCode terminal (Ctrl+Shift+`)
cd src/RevitAddIn2025
powershell -ExecutionPolicy Bypass -File "Development\HotReloadBuild.ps1" -Watch
```

**Method 2: VSCode Tasks**
- Press `Ctrl+Shift+P`
- Type "Tasks: Run Task"
- Select "👀 Hot Reload Watch"

**Method 3: In-Revit Manual Reload**
- Start Revit 2025
- Look for "🔄 Dev Reload" button in RevitAddIn2025 ribbon
- Make code changes in VSCode
- Click "🔄 Dev Reload" to refresh

---

### **PART 2: CLAUDE DESKTOP INTEGRATION**

#### **Step 1: Install Claude Desktop**
1. Download Claude Desktop from Anthropic
2. Install and create account
3. Launch Claude Desktop

#### **Step 2: Configure MCP Servers**
1. **Find Claude config directory:**
   - Windows: `%APPDATA%\Claude\`
   - Create if doesn't exist

2. **Copy configuration:**
```json
{
  "mcpServers": {
    "filesystem": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:/GITHUB/src/RevitAddIn2025"],
      "env": {
        "NODE_ENV": "development"
      }
    },
    "git": {
      "command": "npx", 
      "args": ["-y", "@modelcontextprotocol/server-git", "--repository", "C:/GITHUB"],
      "env": {
        "NODE_ENV": "development"
      }
    }
  }
}
```

3. **Save as:** `%APPDATA%\Claude\claude_desktop_config.json`

#### **Step 3: Install Node.js (if needed)**
```bash
# Install Node.js from nodejs.org
# Or use winget:
winget install OpenJS.NodeJS
```

#### **Step 4: Test MCP Servers**
```bash
# Test filesystem server
npx @modelcontextprotocol/server-filesystem --help

# Test git server  
npx @modelcontextprotocol/server-git --help
```

#### **Step 5: Restart Claude Desktop**
- Close Claude Desktop completely
- Restart the application
- MCP servers should now be available

---

### **PART 3: VSCODE OPTIMIZATION**

#### **Enhanced Settings Applied:**
- ✅ Claude Desktop integration settings
- ✅ MCP server configurations
- ✅ Hot reload task definitions
- ✅ Revit-specific file associations
- ✅ C# development optimizations
- ✅ Terminal PowerShell configuration

#### **Available VSCode Tasks:**
- **🔥 Hot Reload Build** - Build and deploy once
- **👀 Hot Reload Watch** - Auto-build on file changes  
- **🧹 Clean Build** - Clean and rebuild
- **🚀 Start Revit 2025** - Launch Revit
- **🔄 Kill Revit Processes** - Force close Revit
- **📁 Open Revit Addins Folder** - Open addins directory

#### **Keyboard Shortcuts:**
- `Ctrl+Shift+P` → "Tasks: Run Task" → Select task
- `Ctrl+Shift+`` → Open integrated terminal
- `F5` → Start debugging (if configured)

---

### **PART 4: DEVELOPMENT WORKFLOW**

#### **🎯 Optimal Development Cycle:**

1. **Start Development Session:**
   ```bash
   # Option A: VSCode Task
   Ctrl+Shift+P → "Tasks: Run Task" → "👀 Hot Reload Watch"
   
   # Option B: Terminal
   cd src/RevitAddIn2025
   powershell -ExecutionPolicy Bypass -File "Development\HotReloadBuild.ps1" -Watch
   ```

2. **Start Revit:**
   - Use VSCode task "🚀 Start Revit 2025"
   - Or manually launch Revit 2025
   - Load your project

3. **Verify Plugin:**
   - Look for "RevitAddIn2025 Hot Reload" tab
   - Should see 7 buttons including "🔄 Dev Reload"
   - Test with "🎯 Test" button

4. **Development Loop:**
   - Make code changes in VSCode
   - Save files (auto-build triggers)
   - Test in Revit immediately
   - Use "🔄 Dev Reload" for manual refresh

5. **Claude Desktop Assistance:**
   - Open Claude Desktop
   - Ask questions about your code
   - Claude has full project context via MCP
   - Get intelligent suggestions and debugging help

---

### **PART 5: TROUBLESHOOTING**

#### **Hot Reload Issues:**
```bash
# Check if DLL is locked
Get-Process | Where-Object {$_.ProcessName -like "*Revit*"}

# Force kill Revit if needed
Get-Process | Where-Object {$_.ProcessName -like "*Revit*"} | Stop-Process -Force

# Rebuild manually
dotnet build src/RevitAddIn2025/RevitAddIn2025.csproj -c Debug
```

#### **Claude Desktop Issues:**
1. **MCP servers not working:**
   - Check Node.js installation: `node --version`
   - Test MCP servers manually
   - Restart Claude Desktop

2. **File access issues:**
   - Verify paths in config file
   - Check permissions on project directory

3. **Configuration not loading:**
   - Verify config file location
   - Check JSON syntax
   - Restart Claude Desktop

#### **VSCode Issues:**
1. **Tasks not appearing:**
   - Reload VSCode window: `Ctrl+Shift+P` → "Developer: Reload Window"
   - Check `.vscode/tasks.json` exists

2. **Terminal issues:**
   - Set PowerShell as default: `Ctrl+Shift+P` → "Terminal: Select Default Profile"

---

### **🎉 SUCCESS INDICATORS:**

**✅ Hot Reload Working:**
- File watcher shows build messages
- Revit plugin updates without restart
- "🔄 Dev Reload" button visible and functional

**✅ Claude Desktop Working:**
- MCP servers listed in Claude settings
- Claude can answer questions about your code
- File and git context available

**✅ VSCode Integration Working:**
- Tasks appear in command palette
- Terminal opens with PowerShell
- IntelliSense works for C# code

---

**🚀 You now have a complete hot reload development environment with AI assistance!**

**Next Steps:**
1. Test the hot reload workflow
2. Verify Claude Desktop integration
3. Start developing with instant feedback
4. Use Claude for intelligent code assistance

**Happy Coding! 🎯**
