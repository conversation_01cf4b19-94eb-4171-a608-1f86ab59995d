using Autodesk.Revit.ApplicationServices;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RevitAddIn2025.Models;
using RevitAddIn2025.AI;
using RevitAddIn2025.AI.Transformer;
using RevitAddIn2025.Utilities;
using RevitAddIn2025.UI;
using MEPSystemType = RevitAddIn2025.AI.Transformer.MEPSystemType;

namespace RevitAddIn2025.Commands
{
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class RevitTransformerMEPPlugin : IExternalCommand
    {
        private MEPTransformerModel _transformerModel;
        private bool _isAIProcessingActive = false;

        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                UIApplication uiApp = commandData.Application;
                UIDocument uiDoc = uiApp.ActiveUIDocument;
                Document doc = uiDoc.Document;

                // Initialize the transformer model
                InitializeTransformerModel();

                // Create main dialog with this plugin instance
                var dialog = new RevitAddIn2025.UI.TransformerMEPDialog(uiApp, this);
                dialog.ShowDialog();

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                message = $"Error in MEP Transformer Plugin: {ex.Message}";
                TaskDialog.Show("MEP AI Error", ex.ToString());
                return Result.Failed;
            }
        }

        private void InitializeTransformerModel()
        {
            try
            {
                // Initialize transformer with MEP-specific parameters
                int embeddingDim = 512;
                int numHeads = 8;
                int numLayers = 6;
                int feedForwardDim = 2048;
                float dropoutRate = 0.1f;

                _transformerModel = new MEPTransformerModel(
                    embeddingDim,
                    numHeads,
                    numLayers,
                    feedForwardDim,
                    dropoutRate
                );

                // Load pre-trained weights if available
                LoadPreTrainedModel();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"Failed to initialize transformer model: {ex.Message}", ex);
            }
        }

        private void LoadPreTrainedModel()
        {
            try
            {
                Logger.Info("Loading pre-trained MEP transformer model");

                // Initialize the model asynchronously
                Task.Run(async () => await _transformerModel.Initialize());

                Logger.Info("Pre-trained MEP transformer model loaded successfully");
            }
            catch (Exception ex)
            {
                Logger.Error("Failed to load pre-trained model", ex);
                throw new InvalidOperationException($"Model loading failed: {ex.Message}", ex);
            }
        }
    }    /// <summary>
         /// Real-time MEP analysis and optimization using transformer AI
         /// </summary>
    public class MEPTransformerAnalyzer
    {
        private readonly MEPTransformerModel _model;
        private readonly Document _document;

        public MEPTransformerAnalyzer(Document document, MEPTransformerModel model)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _model = model ?? throw new ArgumentNullException(nameof(model));
        }

        /// <summary>
        /// Analyzes current MEP layout and provides AI-driven optimization suggestions
        /// </summary>
        public async Task<Models.MEPOptimizationResult> AnalyzeMEPLayoutAsync()
        {
            try
            {
                Logger.Info("Starting MEP layout analysis with AI transformer");

                // Extract MEP elements from Revit document
                var mepElements = ExtractMEPElements();
                Logger.Info($"Extracted {mepElements.Count} MEP elements for analysis");

                if (!mepElements.Any())
                {
                    return new Models.MEPOptimizationResult
                    {
                        Success = false,
                        Message = "No MEP elements found in the current document",
                        Timestamp = DateTime.Now
                    };
                }

                // Process with AI transformer
                var analysisResult = await _transformerModel.ProcessMEPData(mepElements);

                // Convert to optimization result format
                var optimizationResult = ConvertToOptimizationResult(analysisResult, mepElements);

                Logger.Info($"MEP analysis completed successfully. Found {optimizationResult.Issues.Count} issues and {optimizationResult.Recommendations.Count} recommendations");

                return optimizationResult;
            }
            catch (Exception ex)
            {
                Logger.Error("MEP analysis failed", ex);
                return new Models.MEPOptimizationResult
                {
                    Success = false,
                    Message = $"Analysis failed: {ex.Message}",
                    Timestamp = DateTime.Now,
                    HasErrors = true
                };
            }
        }

        private Models.MEPOptimizationResult ConvertToOptimizationResult(MEPAnalysisResult analysisResult, List<MEPElement> mepElements)
        {
            var result = new Models.MEPOptimizationResult
            {
                Success = !analysisResult.HasErrors,
                Message = analysisResult.HasErrors ? analysisResult.ErrorMessage : "Analysis completed successfully",
                Timestamp = analysisResult.Timestamp,
                ElementCount = analysisResult.ElementCount,
                OverallEnergyScore = analysisResult.OverallEnergyScore,
                OverallComplianceScore = analysisResult.OverallComplianceScore,
                OptimizationPotential = analysisResult.OverallOptimizationPotential
            };

            // Convert issues
            foreach (var issue in analysisResult.Issues)
            {
                result.Issues.Add(new Models.OptimizationIssue
                {
                    Type = ConvertIssueType(issue.Type),
                    ElementId = issue.ElementId,
                    Severity = ConvertSeverity(issue.Severity),
                    Description = issue.Description,
                    Location = issue.Location,
                    Confidence = issue.Confidence
                });
            }

            // Convert recommendations
            foreach (var recommendation in analysisResult.Recommendations)
            {
                result.Recommendations.Add(new Models.OptimizationRecommendation
                {
                    Type = ConvertRecommendationType(recommendation.Type),
                    ElementId = recommendation.ElementId,
                    Priority = ConvertPriority(recommendation.Priority),
                    Description = recommendation.Description,
                    ExpectedImprovement = recommendation.ExpectedImprovement,
                    ImplementationCost = ConvertCost(recommendation.ImplementationCost)
                });
            }

            return result;
        }

        private Models.OptimizationIssueType ConvertIssueType(MEPIssueType issueType)
        {
            switch (issueType)
            {
                case MEPIssueType.Clash: return Models.OptimizationIssueType.Clash;
                case MEPIssueType.CodeViolation: return Models.OptimizationIssueType.CodeViolation;
                case MEPIssueType.EnergyInefficiency: return Models.OptimizationIssueType.EnergyInefficiency;
                case MEPIssueType.SystemFailure: return Models.OptimizationIssueType.SystemFailure;
                default: return Models.OptimizationIssueType.PerformanceDegradation;
            }
        }

        private Models.OptimizationSeverity ConvertSeverity(IssueSeverity severity)
        {
            switch (severity)
            {
                case IssueSeverity.Low: return Models.OptimizationSeverity.Low;
                case IssueSeverity.Medium: return Models.OptimizationSeverity.Medium;
                case IssueSeverity.High: return Models.OptimizationSeverity.High;
                case IssueSeverity.Critical: return Models.OptimizationSeverity.Critical;
                default: return Models.OptimizationSeverity.Medium;
            }
        }

        private Models.OptimizationRecommendationType ConvertRecommendationType(MEPRecommendationType recommendationType)
        {
            switch (recommendationType)
            {
                case MEPRecommendationType.EnergyOptimization: return Models.OptimizationRecommendationType.EnergyOptimization;
                case MEPRecommendationType.SystemOptimization: return Models.OptimizationRecommendationType.SystemOptimization;
                case MEPRecommendationType.CodeCompliance: return Models.OptimizationRecommendationType.CodeCompliance;
                case MEPRecommendationType.MaintenanceImprovement: return Models.OptimizationRecommendationType.MaintenanceImprovement;
                default: return Models.OptimizationRecommendationType.PerformanceEnhancement;
            }
        }

        private Models.OptimizationPriority ConvertPriority(RecommendationPriority priority)
        {
            switch (priority)
            {
                case RecommendationPriority.Low: return Models.OptimizationPriority.Low;
                case RecommendationPriority.Medium: return Models.OptimizationPriority.Medium;
                case RecommendationPriority.High: return Models.OptimizationPriority.High;
                case RecommendationPriority.Critical: return Models.OptimizationPriority.Critical;
                default: return Models.OptimizationPriority.Medium;
            }
        }

        private Models.OptimizationCost ConvertCost(ImplementationCost cost)
        {
            switch (cost)
            {
                case ImplementationCost.Low: return Models.OptimizationCost.Low;
                case ImplementationCost.Medium: return Models.OptimizationCost.Medium;
                case ImplementationCost.High: return Models.OptimizationCost.High;
                default: return Models.OptimizationCost.Medium;
            }
        }



        private List<Models.MEPElementData> ExtractMEPElements()
        {
            var mepElements = new List<Models.MEPElementData>();

            // Extract Mechanical elements
            var mechanicalElements = new FilteredElementCollector(_document)
                .OfCategory(BuiltInCategory.OST_DuctCurves)
                .WhereElementIsNotElementType()
                .ToElements();

            foreach (var element in mechanicalElements)
            {
                if (element is Duct duct)
                {
                    mepElements.Add(CreateMEPElementData(duct, MEPSystemType.Mechanical));
                }
            }

            // Extract Electrical elements
            var electricalElements = new FilteredElementCollector(_document)
                .OfCategory(BuiltInCategory.OST_CableTray)
                .WhereElementIsNotElementType()
                .ToElements();

            foreach (var element in electricalElements)
            {
                mepElements.Add(CreateMEPElementData(element, MEPSystemType.Electrical));
            }

            // Extract Plumbing elements
            var plumbingElements = new FilteredElementCollector(_document)
                .OfCategory(BuiltInCategory.OST_PipeCurves)
                .WhereElementIsNotElementType()
                .ToElements();

            foreach (var element in plumbingElements)
            {
                if (element is Pipe pipe)
                {
                    mepElements.Add(CreateMEPElementData(pipe, MEPSystemType.Plumbing));
                }
            }

            return mepElements;
        }
        private Models.MEPElementData CreateMEPElementData(Element element, MEPSystemType systemType)
        {
            var boundingBox = element.get_BoundingBox(null);

            // Use the proper constructor that takes an Element
            var mepElementData = new Models.MEPElementData(element);

            // The constructor handles most properties automatically
            // We can only set writable properties if needed

            return mepElementData;
        }

        private string GetElementTypeString(Element element)
        {
            return element.GetType().Name;
        }

        private int GetElementPriority(Element element, MEPSystemType systemType)
        {
            // Priority assignment based on system criticality
            return systemType switch
            {
                MEPSystemType.Electrical => 1, // Highest priority
                MEPSystemType.Plumbing => 2,   // Medium priority
                MEPSystemType.Mechanical => 3, // Lower priority
                _ => 4
            };
        }

        private double GetFlowRate(Element element)
        {
            // Extract flow rate parameter if available
            var flowParam = element.LookupParameter("Flow");
            return flowParam?.AsDouble() ?? 0.0;
        }

        private double GetPressure(Element element)
        {
            // Extract pressure parameter if available
            var pressureParam = element.LookupParameter("Pressure Drop");
            return pressureParam?.AsDouble() ?? 0.0;
        }

        private double GetTemperature(Element element)
        {
            // Extract temperature parameter if available
            var tempParam = element.LookupParameter("Temperature");
            return tempParam?.AsDouble() ?? 68.0; // Default room temperature
        }
        private List<MEPElementData> ConvertToTransformerInput(List<MEPElementData> mepElements)
        {
            // Normalize and prepare data for transformer
            var transformedElements = new List<MEPElementData>();

            foreach (var element in mepElements)
            {
                // Create a copy with normalized values
                // Since MEPElementData has read-only properties, we need to work with the existing object
                // or create a new one from the Element if we have access to it

                // For now, return the elements as-is since the transformer can handle them
                transformedElements.Add(element);
            }

            return transformedElements;
        }

        private MEPPosition3D NormalizePosition(MEPPosition3D position)
        {
            // Normalize coordinates to [0, 1] range
            return new MEPPosition3D(
                position.X / 1000.0, // Assuming building scale in feet
                position.Y / 1000.0,
                position.Z / 100.0    // Height normalization
            );
        }

        private MEPDimensions NormalizeDimensions(MEPDimensions dimensions)
        {
            return new MEPDimensions(
                Math.Min(dimensions.Width / 10.0, 1.0),
                Math.Min(dimensions.Height / 10.0, 1.0),
                Math.Min(dimensions.Depth / 100.0, 1.0)
            );
        }

        private double NormalizeFlowRate(double flowRate)
        {
            return Math.Min(flowRate / 1000.0, 1.0); // Normalize flow rate
        }

        private double NormalizePressure(double pressure)
        {
            return Math.Min(pressure / 50.0, 1.0); // Normalize pressure
        }

        private double NormalizeTemperature(double temperature)
        {
            return (temperature - 32.0) / 180.0; // Fahrenheit to normalized range
        }

    }
}
