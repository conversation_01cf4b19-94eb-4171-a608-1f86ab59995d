using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;

namespace RevitAddIn2025.Models
{
    /// <summary>
    /// 3D Vector class for precise spatial calculations
    /// </summary>
    public class Vector3D
    {
        public double X { get; set; }
        public double Y { get; set; }
        public double Z { get; set; }

        public Vector3D() { }

        public Vector3D(double x, double y, double z)
        {
            X = x;
            Y = y;
            Z = z;
        }

        public Vector3D(XYZ point)
        {
            X = point.X;
            Y = point.Y;
            Z = point.Z;
        }

        /// <summary>
        /// Calculates the magnitude of the vector
        /// </summary>
        public double Magnitude => Math.Sqrt(X * X + Y * Y + Z * Z);

        /// <summary>
        /// Normalizes the vector to unit length
        /// </summary>
        public Vector3D Normalize()
        {
            double mag = Magnitude;
            if (mag == 0) return new Vector3D(0, 0, 0);
            return new Vector3D(X / mag, Y / mag, Z / mag);
        }

        /// <summary>
        /// Calculates dot product with another vector
        /// </summary>
        public double DotProduct(Vector3D other)
        {
            return X * other.X + Y * other.Y + Z * other.Z;
        }

        /// <summary>
        /// Calculates cross product with another vector
        /// </summary>
        public Vector3D CrossProduct(Vector3D other)
        {
            return new Vector3D(
                Y * other.Z - Z * other.Y,
                Z * other.X - X * other.Z,
                X * other.Y - Y * other.X
            );
        }

        /// <summary>
        /// Converts to Revit XYZ
        /// </summary>
        public XYZ ToXYZ()
        {
            return new XYZ(X, Y, Z);
        }
    }

    /// <summary>
    /// Placement vector with position, orientation, and quality metrics
    /// </summary>
    public class PlacementVector
    {
        public Vector3D Position { get; set; }
        public Vector3D Orientation { get; set; }
        public MEPSystemType SystemType { get; set; }
        public double CoverageRadius { get; set; }
        public double QualityScore { get; set; }
        public string PlacementReason { get; set; }
        public List<string> ComplianceNotes { get; set; } = new List<string>();
        public bool IsOptimal { get; set; }
        public double EnergyEfficiencyScore { get; set; }
        public double ClashRiskScore { get; set; }
    }

    /// <summary>
    /// Ceiling space analysis data
    /// </summary>
    public class CeilingSpace
    {
        public string SpaceId { get; set; }
        public string SpaceName { get; set; }
        public Vector3D CenterPoint { get; set; }
        public double Width { get; set; }
        public double Length { get; set; }
        public double Area { get; set; }
        public double CeilingHeight { get; set; }
        public double WorkPlaneHeight { get; set; } = 2.5; // 2.5 feet default work plane
        public double AspectRatio => Length > 0 ? Width / Length : 1.0;
        public List<Vector3D> BoundaryPoints { get; set; } = new List<Vector3D>();
        public FalseCeilingType CeilingType { get; set; }
        public OfficeSpaceType SpaceType { get; set; }
        public List<ObstacleData> Obstacles { get; set; } = new List<ObstacleData>();
        public List<MEPElementData> ExistingMEPElements { get; set; } = new List<MEPElementData>();
    }

    /// <summary>
    /// Placement parameters for optimization
    /// </summary>
    public class PlacementParameters
    {
        public double CustomSpacing { get; set; }
        public double MinimumSpacing { get; set; } = 2.0;
        public bool EnergyOptimized { get; set; } = true;
        public bool ClashAvoidance { get; set; } = true;
        public bool CodeCompliance { get; set; } = true;
        public double MaxFixtureWeight { get; set; } = 50.0;
        public MountingMethod PreferredMounting { get; set; }
        public List<string> SelectedFamilies { get; set; } = new List<string>();
        public double IlluminationLevel { get; set; } = 50.0; // Foot-candles
        public double AirChangeRate { get; set; } = 6.0; // ACH for HVAC
        public bool ConsiderFurniture { get; set; } = true;
    }

    /// <summary>
    /// False ceiling type enumeration
    /// </summary>
    public enum FalseCeilingType
    {
        Unknown,
        GridCeiling,        // T-bar/suspended grid systems
        GypsumBoard,        // Drywall false ceilings
        MetalPanel,         // Metal panel ceilings
        OpenCell,           // Open cell/linear ceilings
        AcousticTile,       // Acoustic tile ceilings
        IntegratedService   // Integrated service ceilings
    }

    /// <summary>
    /// Office space type enumeration
    /// </summary>
    public enum OfficeSpaceType
    {
        OpenPlan,
        PrivateOffice,
        ConferenceRoom,
        Corridor,
        Reception,
        BreakRoom,
        ServerRoom,
        Storage,
        Restroom,
        Lobby
    }

    /// <summary>
    /// MEP placement system type enumeration (extends base MEPSystemType)
    /// </summary>
    public enum MEPPlacementSystemType
    {
        Lighting,
        HVAC,
        FireSafety,
        AudioVisual,
        Security,
        Electrical,
        Plumbing,
        DataComm
    }

    /// <summary>
    /// Mounting method enumeration
    /// </summary>
    public enum MountingMethod
    {
        GridClip,
        HangerWire,
        StructuralAttachment,
        ToggleBolt,
        HollowWallAnchor,
        MagneticMount,
        ScrewAttachment,
        ClipMount,
        IntegratedRail,
        ModularConnection
    }

    /// <summary>
    /// Ceiling analysis result
    /// </summary>
    public class CeilingAnalysisResult
    {
        public ElementId CeilingId { get; set; }
        public string CeilingName { get; set; }
        public FalseCeilingType CeilingType { get; set; }
        public CeilingStructuralProperties StructuralProperties { get; set; }
        public LoadBearingCapacity LoadBearingCapacity { get; set; }
        public MountingRequirements MountingRequirements { get; set; }
        public double AccessibilityRating { get; set; }
        public CeilingGeometryData GeometryData { get; set; }
        public MEPIntegrationCapabilities MEPIntegrationCapabilities { get; set; }
    }

    /// <summary>
    /// Ceiling structural properties
    /// </summary>
    public class CeilingStructuralProperties
    {
        public double Thickness { get; set; }
        public string StructuralComposition { get; set; }
        public double MaxSpanCapability { get; set; }
        public string DeflectionCharacteristics { get; set; }
        public List<string> ConnectionMethods { get; set; } = new List<string>();
        public double StructuralRating { get; set; }
    }

    /// <summary>
    /// Load bearing capacity data
    /// </summary>
    public class LoadBearingCapacity
    {
        public double UniformLoad { get; set; } // psf
        public double ConcentratedLoad { get; set; } // lbs per point
        public double LinearLoad { get; set; } // lbs per linear foot
        public double SafetyFactor { get; set; } = 2.0;
        public string LoadRating { get; set; }
        public List<string> Limitations { get; set; } = new List<string>();
    }

    /// <summary>
    /// Mounting requirements
    /// </summary>
    public class MountingRequirements
    {
        public MountingMethod PreferredMountingMethod { get; set; }
        public MountingMethod[] AlternateMountingMethods { get; set; }
        public bool RequiresStructuralSupport { get; set; }
        public double MaxFixtureWeight { get; set; }
        public double MinimumSpacing { get; set; }
        public List<string> SpecialRequirements { get; set; } = new List<string>();
    }

    /// <summary>
    /// Ceiling geometry data
    /// </summary>
    public class CeilingGeometryData
    {
        public double Area { get; set; }
        public double Perimeter { get; set; }
        public Vector3D CenterPoint { get; set; }
        public List<Vector3D> BoundaryPoints { get; set; } = new List<Vector3D>();
        public double Height { get; set; }
        public List<OpeningData> Openings { get; set; } = new List<OpeningData>();
    }

    /// <summary>
    /// MEP integration capabilities
    /// </summary>
    public class MEPIntegrationCapabilities
    {
        public bool SupportsLighting { get; set; }
        public bool SupportsHVAC { get; set; }
        public bool SupportsFireSafety { get; set; }
        public bool SupportsAudioVisual { get; set; }
        public bool SupportsSecurity { get; set; }
        public bool SupportsDataComm { get; set; }
        public double MaxIntegratedLoad { get; set; }
        public List<string> IntegrationLimitations { get; set; } = new List<string>();
    }

    /// <summary>
    /// Obstacle data for collision avoidance
    /// </summary>
    public class ObstacleData
    {
        public string ObstacleId { get; set; }
        public string ObstacleType { get; set; }
        public Vector3D Position { get; set; }
        public Vector3D Dimensions { get; set; }
        public double ClearanceRequired { get; set; }
        public bool IsMovable { get; set; }
    }

    /// <summary>
    /// Opening data in ceilings
    /// </summary>
    public class OpeningData
    {
        public string OpeningId { get; set; }
        public Vector3D Position { get; set; }
        public double Width { get; set; }
        public double Height { get; set; }
        public string OpeningType { get; set; }
    }

    /// <summary>
    /// Placement recommendation with scoring
    /// </summary>
    public class PlacementRecommendation
    {
        public PlacementVector PlacementVector { get; set; }
        public double OverallScore { get; set; }
        public double EnergyScore { get; set; }
        public double ClashScore { get; set; }
        public double ComplianceScore { get; set; }
        public double CostScore { get; set; }
        public string RecommendationReason { get; set; }
        public List<string> Benefits { get; set; } = new List<string>();
        public List<string> Considerations { get; set; } = new List<string>();
        public bool IsRecommended { get; set; }
    }

    /// <summary>
    /// Comprehensive placement analysis result
    /// </summary>
    public class PlacementAnalysisResult
    {
        public string AnalysisId { get; set; }
        public DateTime AnalysisTimestamp { get; set; }
        public CeilingSpace AnalyzedSpace { get; set; }
        public MEPPlacementSystemType SystemType { get; set; }
        public List<PlacementRecommendation> Recommendations { get; set; } = new List<PlacementRecommendation>();
        public PlacementStatistics Statistics { get; set; }
        public List<string> AnalysisNotes { get; set; } = new List<string>();
        public bool AnalysisSuccessful { get; set; }
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Placement statistics
    /// </summary>
    public class PlacementStatistics
    {
        public int TotalRecommendations { get; set; }
        public int OptimalPlacements { get; set; }
        public int AcceptablePlacements { get; set; }
        public int ProblematicPlacements { get; set; }
        public double AverageQualityScore { get; set; }
        public double CoveragePercentage { get; set; }
        public double EnergyEfficiencyRating { get; set; }
        public double ComplianceRating { get; set; }
        public double EstimatedCost { get; set; }
        public double EstimatedInstallationTime { get; set; }
    }

    /// <summary>
    /// AI processing result for MEP analysis
    /// </summary>
    public class AIProcessingResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<PlacementRecommendation> Recommendations { get; set; } = new List<PlacementRecommendation>();
        public Dictionary<string, double> Metrics { get; set; } = new Dictionary<string, double>();
        public TimeSpan ProcessingTime { get; set; }
        public string ErrorDetails { get; set; }
    }
}
