using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Threading.Tasks;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB;
using RevitAddIn2025.Models;
using RevitAddIn2025.Utilities;
using WinGrid = System.Windows.Controls.Grid;
using WinColor = System.Windows.Media.Color;

namespace RevitAddIn2025.UI
{
    /// <summary>
    /// Energy Analytics Window with real-time calculations using ASHRAE standards
    /// Provides comprehensive energy consumption and carbon footprint analysis
    /// </summary>
    public class EnergyAnalyticsWindow : Window
    {
        private readonly UIApplication _uiApp;
        private readonly Document _document;

        // UI Components
        private TextBlock _totalEnergyText;
        private TextBlock _lightingEnergyText;
        private TextBlock _hvacEnergyText;
        private TextBlock _equipmentEnergyText;
        private TextBlock _carbonFootprintText;
        private TextBlock _costAnalysisText;
        private ProgressBar _efficiencyBar;
        private Canvas _heatMapCanvas;
        private ListBox _recommendationsList;
        private Button _refreshButton;
        private Button _exportButton;

        // Energy calculation data
        private EnergyAnalysisResult _currentAnalysis;
        private List<MEPElementData> _mepElements;

        public EnergyAnalyticsWindow(UIApplication uiApp)
        {
            _uiApp = uiApp ?? throw new ArgumentNullException(nameof(uiApp));
            _document = _uiApp.ActiveUIDocument?.Document ?? throw new ArgumentNullException("No active document");

            InitializeWindow();
            CreateUI();
            LoadMEPElements();
            _ = PerformEnergyAnalysisAsync();
        }

        private void InitializeWindow()
        {
            Title = "Energy Analytics & Carbon Footprint Analysis";
            Width = 1200;
            Height = 800;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250));

            // Apply modern styling
            FontFamily = new FontFamily("Segoe UI");
            FontSize = 14;
        }

        private void CreateUI()
        {
            var mainGrid = new WinGrid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) }); // Header
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // Content
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) }); // Footer

            // Header
            CreateHeader(mainGrid);

            // Main content
            CreateMainContent(mainGrid);

            // Footer
            CreateFooter(mainGrid);

            Content = mainGrid;
        }

        private void CreateHeader(WinGrid parent)
        {
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(WinColor.FromRgb(0, 122, 255)),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var titleText = new TextBlock
            {
                Text = "⚡ Energy Analytics Dashboard",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            var statusText = new TextBlock
            {
                Text = "Real-time energy analysis using ASHRAE standards",
                FontSize = 14,
                Foreground = new SolidColorBrush(WinColor.FromRgb(200, 220, 255)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            headerPanel.Children.Add(titleText);
            headerPanel.Children.Add(statusText);

            WinGrid.SetRow(headerPanel, 0);
            parent.Children.Add(headerPanel);
        }

        private void CreateMainContent(WinGrid parent)
        {
            var contentGrid = new WinGrid();
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(2, GridUnitType.Star) }); // Left panel
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // Right panel

            // Left panel - Energy metrics and visualizations
            CreateEnergyMetricsPanel(contentGrid);

            // Right panel - Recommendations and controls
            CreateRecommendationsPanel(contentGrid);

            WinGrid.SetRow(contentGrid, 1);
            parent.Children.Add(contentGrid);
        }

        private void CreateEnergyMetricsPanel(WinGrid parent)
        {
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Margin = new Thickness(20, 0, 10, 0)
            };

            var stackPanel = new StackPanel();

            // Energy consumption summary
            CreateEnergyConsumptionCard(stackPanel);

            // Carbon footprint analysis
            CreateCarbonFootprintCard(stackPanel);

            // Heat map visualization
            CreateHeatMapCard(stackPanel);

            // Efficiency metrics
            CreateEfficiencyCard(stackPanel);

            scrollViewer.Content = stackPanel;
            WinGrid.SetColumn(scrollViewer, 0);
            parent.Children.Add(scrollViewer);
        }

        private void CreateEnergyConsumptionCard(StackPanel parent)
        {
            var card = CreateCard("📊 Energy Consumption Analysis");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            // Total energy display
            var totalEnergyPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 15) };
            totalEnergyPanel.Children.Add(new TextBlock { Text = "Total Annual Consumption:", FontWeight = FontWeights.SemiBold, Width = 200 });
            _totalEnergyText = new TextBlock { Text = "Calculating...", FontSize = 18, FontWeight = FontWeights.Bold, Foreground = new SolidColorBrush(WinColor.FromRgb(0, 122, 255)) };
            totalEnergyPanel.Children.Add(_totalEnergyText);
            contentStack.Children.Add(totalEnergyPanel);

            // Lighting energy
            var lightingPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
            lightingPanel.Children.Add(new TextBlock { Text = "💡 Lighting Systems:", FontWeight = FontWeights.SemiBold, Width = 200 });
            _lightingEnergyText = new TextBlock { Text = "Calculating...", Foreground = new SolidColorBrush(WinColor.FromRgb(255, 149, 0)) };
            lightingPanel.Children.Add(_lightingEnergyText);
            contentStack.Children.Add(lightingPanel);

            // HVAC energy
            var hvacPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
            hvacPanel.Children.Add(new TextBlock { Text = "🌡️ HVAC Systems:", FontWeight = FontWeights.SemiBold, Width = 200 });
            _hvacEnergyText = new TextBlock { Text = "Calculating...", Foreground = new SolidColorBrush(WinColor.FromRgb(52, 199, 89)) };
            hvacPanel.Children.Add(_hvacEnergyText);
            contentStack.Children.Add(hvacPanel);

            // Equipment energy
            var equipmentPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
            equipmentPanel.Children.Add(new TextBlock { Text = "⚙️ Equipment Load:", FontWeight = FontWeights.SemiBold, Width = 200 });
            _equipmentEnergyText = new TextBlock { Text = "Calculating...", Foreground = new SolidColorBrush(WinColor.FromRgb(88, 86, 214)) };
            equipmentPanel.Children.Add(_equipmentEnergyText);
            contentStack.Children.Add(equipmentPanel);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateCarbonFootprintCard(StackPanel parent)
        {
            var card = CreateCard("🌍 Carbon Footprint Analysis");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            // Carbon footprint display
            var carbonPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 15) };
            carbonPanel.Children.Add(new TextBlock { Text = "Annual CO₂ Emissions:", FontWeight = FontWeights.SemiBold, Width = 200 });
            _carbonFootprintText = new TextBlock { Text = "Calculating...", FontSize = 18, FontWeight = FontWeights.Bold, Foreground = new SolidColorBrush(WinColor.FromRgb(255, 59, 48)) };
            carbonPanel.Children.Add(_carbonFootprintText);
            contentStack.Children.Add(carbonPanel);

            // Cost analysis
            var costPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 10) };
            costPanel.Children.Add(new TextBlock { Text = "💰 Annual Energy Cost:", FontWeight = FontWeights.SemiBold, Width = 200 });
            _costAnalysisText = new TextBlock { Text = "Calculating...", FontSize = 16, FontWeight = FontWeights.SemiBold, Foreground = new SolidColorBrush(WinColor.FromRgb(255, 149, 0)) };
            costPanel.Children.Add(_costAnalysisText);
            contentStack.Children.Add(costPanel);

            // Regional emission factor info
            var infoText = new TextBlock
            {
                Text = "Calculations based on regional emission factors and current utility rates. " +
                       "CO₂ emissions calculated using EPA eGRID data for accurate regional analysis.",
                FontSize = 12,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 15, 0, 0)
            };
            contentStack.Children.Add(infoText);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateHeatMapCard(StackPanel parent)
        {
            var card = CreateCard("🔥 Energy Intensity Heat Map");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            _heatMapCanvas = new Canvas
            {
                Height = 200,
                Background = new SolidColorBrush(WinColor.FromRgb(245, 245, 245)),
                Margin = new Thickness(0, 10, 0, 0)
            };

            var heatMapLabel = new TextBlock
            {
                Text = "Interactive heat map showing energy intensity by zone (kWh/sq ft/year)",
                FontSize = 12,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                Margin = new Thickness(0, 0, 0, 10)
            };

            contentStack.Children.Add(heatMapLabel);
            contentStack.Children.Add(_heatMapCanvas);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateEfficiencyCard(StackPanel parent)
        {
            var card = CreateCard("📈 Energy Efficiency Score");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            var efficiencyLabel = new TextBlock
            {
                Text = "Overall Building Efficiency (ASHRAE 90.1 Baseline Comparison)",
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 10)
            };

            _efficiencyBar = new ProgressBar
            {
                Height = 30,
                Minimum = 0,
                Maximum = 100,
                Value = 0,
                Foreground = new SolidColorBrush(WinColor.FromRgb(52, 199, 89)),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var efficiencyText = new TextBlock
            {
                Text = "Efficiency score based on energy use intensity compared to ASHRAE 90.1 baseline",
                FontSize = 12,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                TextWrapping = TextWrapping.Wrap
            };

            contentStack.Children.Add(efficiencyLabel);
            contentStack.Children.Add(_efficiencyBar);
            contentStack.Children.Add(efficiencyText);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private Border CreateCard(string title)
        {
            var card = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230)),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(0, 0, 0, 20),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = WinColor.FromRgb(0, 0, 0),
                    Opacity = 0.1,
                    BlurRadius = 10,
                    ShadowDepth = 2
                }
            };

            var headerPanel = new StackPanel();
            var headerText = new TextBlock
            {
                Text = title,
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(WinColor.FromRgb(51, 51, 51)),
                Margin = new Thickness(20, 15, 20, 0)
            };
            headerPanel.Children.Add(headerText);

            var contentPanel = new StackPanel();
            headerPanel.Children.Add(contentPanel);

            card.Child = headerPanel;
            return card;
        }

        private void LoadMEPElements()
        {
            try
            {
                // Extract MEP elements from the document
                var collector = new FilteredElementCollector(_document)
                    .WhereElementIsNotElementType()
                    .ToElements();

                _mepElements = collector
                    .Where(e => IsMEPElement(e))
                    .Select(e => new MEPElementData(e))
                    .ToList();

                Logger.Info($"Loaded {_mepElements.Count} MEP elements for energy analysis");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading MEP elements", ex);
                _mepElements = new List<MEPElementData>();
            }
        }

        private bool IsMEPElement(Element element)
        {
            // Check if element is MEP-related
            var category = element.Category?.Name;
            return category != null && (
                category.Contains("Lighting") ||
                category.Contains("Electrical") ||
                category.Contains("Mechanical") ||
                category.Contains("HVAC") ||
                category.Contains("Plumbing") ||
                category.Contains("Air") ||
                category.Contains("Duct") ||
                category.Contains("Pipe") ||
                category.Contains("Fixture")
            );
        }

        private void CreateRecommendationsPanel(WinGrid parent)
        {
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Margin = new Thickness(10, 0, 20, 0)
            };

            var stackPanel = new StackPanel();

            // Control buttons
            CreateControlButtons(stackPanel);

            // Recommendations list
            CreateRecommendationsList(stackPanel);

            scrollViewer.Content = stackPanel;
            WinGrid.SetColumn(scrollViewer, 1);
            parent.Children.Add(scrollViewer);
        }

        private void CreateControlButtons(StackPanel parent)
        {
            var card = CreateCard("🔧 Analysis Controls");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            _refreshButton = new Button
            {
                Content = "🔄 Refresh Analysis",
                Height = 40,
                Background = new SolidColorBrush(WinColor.FromRgb(0, 122, 255)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            _refreshButton.Click += RefreshButton_Click;

            _exportButton = new Button
            {
                Content = "📊 Export Report",
                Height = 40,
                Background = new SolidColorBrush(WinColor.FromRgb(52, 199, 89)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontWeight = FontWeights.SemiBold
            };
            _exportButton.Click += ExportButton_Click;

            contentStack.Children.Add(_refreshButton);
            contentStack.Children.Add(_exportButton);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateRecommendationsList(StackPanel parent)
        {
            var card = CreateCard("💡 Energy Optimization Recommendations");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            _recommendationsList = new ListBox
            {
                Height = 400,
                Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250)),
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230))
            };

            contentStack.Children.Add(_recommendationsList);
            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateFooter(WinGrid parent)
        {
            var footerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250)),
                Height = 60
            };

            var statusText = new TextBlock
            {
                Text = "Energy calculations based on ASHRAE 90.1, IEEE 739, and ISO 50001 standards",
                FontSize = 12,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            footerPanel.Children.Add(statusText);

            WinGrid.SetRow(footerPanel, 2);
            parent.Children.Add(footerPanel);
        }

        #region Energy Analysis Methods

        /// <summary>
        /// Performs comprehensive energy analysis using ASHRAE standards
        /// </summary>
        private async Task PerformEnergyAnalysisAsync()
        {
            try
            {
                Logger.Info("Starting comprehensive energy analysis");

                _currentAnalysis = new EnergyAnalysisResult();

                // Calculate lighting energy consumption using P = n × W × h × d formula
                await CalculateLightingEnergyAsync();

                // Calculate HVAC energy using Q = m × Cp × ΔT + Latent Load
                await CalculateHVACEnergyAsync();

                // Calculate equipment energy using Σ(Power Rating × Operating Hours × Load Factor)
                await CalculateEquipmentEnergyAsync();

                // Calculate total energy consumption
                CalculateTotalEnergy();

                // Calculate carbon footprint using regional emission factors
                CalculateCarbonFootprint();

                // Calculate energy costs
                CalculateEnergyCosts();

                // Calculate efficiency score
                CalculateEfficiencyScore();

                // Generate recommendations
                await GenerateRecommendationsAsync();

                // Update UI
                UpdateEnergyDisplays();
                UpdateHeatMap();
                UpdateRecommendations();

                Logger.Info("Energy analysis completed successfully");
            }
            catch (Exception ex)
            {
                Logger.Error("Error during energy analysis", ex);
                MessageBox.Show($"Error during energy analysis: {ex.Message}", "Analysis Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Calculates lighting energy consumption using industry formulas
        /// P = n × W × h × d (where n=fixtures, W=watts, h=hours, d=days)
        /// </summary>
        private async Task CalculateLightingEnergyAsync()
        {
            await Task.Run(() =>
            {
                var lightingElements = _mepElements.Where(e =>
                    e.ElementType.Contains("Light") ||
                    e.ElementType.Contains("Fixture") ||
                    e.Discipline == MEPDiscipline.Electrical).ToList();

                double totalWattage = 0;
                int fixtureCount = 0;

                foreach (var element in lightingElements)
                {
                    // Extract wattage from element properties or use defaults
                    double wattage = GetElementWattage(element);
                    totalWattage += wattage;
                    fixtureCount++;
                }

                // Calculate annual energy consumption
                // Assumptions: 10 hours/day operation, 250 working days/year
                double operatingHours = 10;
                double operatingDays = 250;
                double annualHours = operatingHours * operatingDays;

                _currentAnalysis.LightingEnergyKWh = (totalWattage * annualHours) / 1000; // Convert to kWh
                _currentAnalysis.LightingFixtureCount = fixtureCount;
                _currentAnalysis.LightingWattage = totalWattage;

                Logger.Info($"Lighting analysis: {fixtureCount} fixtures, {totalWattage:F0}W total, {_currentAnalysis.LightingEnergyKWh:F0} kWh/year");
            });
        }

        /// <summary>
        /// Calculates HVAC energy using thermodynamic principles
        /// Q = m × Cp × ΔT + Latent Load calculations
        /// </summary>
        private async Task CalculateHVACEnergyAsync()
        {
            await Task.Run(() =>
            {
                var hvacElements = _mepElements.Where(e =>
                    e.Discipline == MEPDiscipline.Mechanical ||
                    e.ElementType.Contains("HVAC") ||
                    e.ElementType.Contains("Air")).ToList();

                double totalCoolingLoad = 0;
                double totalAirflow = 0;

                foreach (var element in hvacElements)
                {
                    // Calculate cooling load based on airflow and temperature differential
                    double airflow = element.FlowRate > 0 ? element.FlowRate : GetDefaultAirflow(element);
                    double temperatureDiff = 20; // Typical 20°F temperature difference

                    // Sensible cooling load: Q = 1.08 × CFM × ΔT (BTU/hr)
                    double sensibleLoad = 1.08 * airflow * temperatureDiff;

                    // Add latent load (typically 30% of sensible load)
                    double latentLoad = sensibleLoad * 0.3;

                    totalCoolingLoad += sensibleLoad + latentLoad;
                    totalAirflow += airflow;
                }

                // Convert to annual energy consumption
                // Assumptions: 2000 cooling hours, 1500 heating hours per year
                double coolingHours = 2000;
                double heatingHours = 1500;
                double copCooling = 3.0; // Coefficient of Performance for cooling
                double copHeating = 2.5; // COP for heating

                _currentAnalysis.HVACCoolingEnergyKWh = (totalCoolingLoad * coolingHours) / (copCooling * 3412); // Convert BTU to kWh
                _currentAnalysis.HVACHeatingEnergyKWh = (totalCoolingLoad * 0.8 * heatingHours) / (copHeating * 3412); // Heating load typically 80% of cooling
                _currentAnalysis.TotalAirflow = totalAirflow;

                Logger.Info($"HVAC analysis: {totalAirflow:F0} CFM total, {_currentAnalysis.HVACCoolingEnergyKWh:F0} kWh cooling, {_currentAnalysis.HVACHeatingEnergyKWh:F0} kWh heating");
            });
        }

        /// <summary>
        /// Calculates equipment energy consumption
        /// Σ(Power Rating × Operating Hours × Load Factor)
        /// </summary>
        private async Task CalculateEquipmentEnergyAsync()
        {
            await Task.Run(() =>
            {
                var equipmentElements = _mepElements.Where(e =>
                    !e.ElementType.Contains("Light") &&
                    !e.ElementType.Contains("HVAC") &&
                    e.Discipline == MEPDiscipline.Electrical).ToList();

                double totalEquipmentLoad = 0;

                foreach (var element in equipmentElements)
                {
                    double powerRating = GetElementPowerRating(element);
                    double loadFactor = 0.7; // Typical 70% load factor
                    double operatingHours = 2500; // Annual operating hours

                    double annualConsumption = powerRating * operatingHours * loadFactor / 1000; // Convert to kWh
                    totalEquipmentLoad += annualConsumption;
                }

                _currentAnalysis.EquipmentEnergyKWh = totalEquipmentLoad;

                Logger.Info($"Equipment analysis: {_currentAnalysis.EquipmentEnergyKWh:F0} kWh/year");
            });
        }

        #endregion

        #region Helper Methods

        private double GetElementWattage(MEPElementData element)
        {
            // Try to extract wattage from element properties
            if (element.Properties.ContainsKey("Wattage"))
            {
                if (double.TryParse(element.Properties["Wattage"].ToString(), out double wattage))
                    return wattage;
            }

            // Default wattages based on element type
            if (element.ElementType.Contains("LED")) return 25;
            if (element.ElementType.Contains("Fluorescent")) return 32;
            if (element.ElementType.Contains("Incandescent")) return 60;

            return 40; // Default fixture wattage
        }

        private double GetDefaultAirflow(MEPElementData element)
        {
            // Default airflow values based on element type (CFM)
            if (element.ElementType.Contains("Supply")) return 500;
            if (element.ElementType.Contains("Return")) return 400;
            if (element.ElementType.Contains("Exhaust")) return 300;

            return 250; // Default airflow
        }

        private double GetElementPowerRating(MEPElementData element)
        {
            // Try to extract power rating from element properties
            if (element.Properties.ContainsKey("PowerRating"))
            {
                if (double.TryParse(element.Properties["PowerRating"].ToString(), out double power))
                    return power;
            }

            // Default power ratings based on element type (watts)
            if (element.ElementType.Contains("Motor")) return 1500;
            if (element.ElementType.Contains("Pump")) return 750;
            if (element.ElementType.Contains("Fan")) return 500;

            return 200; // Default equipment power
        }

        /// <summary>
        /// Calculates total energy consumption
        /// </summary>
        private void CalculateTotalEnergy()
        {
            _currentAnalysis.TotalEnergyKWh = _currentAnalysis.LightingEnergyKWh +
                                            _currentAnalysis.HVACCoolingEnergyKWh +
                                            _currentAnalysis.HVACHeatingEnergyKWh +
                                            _currentAnalysis.EquipmentEnergyKWh;
        }

        /// <summary>
        /// Calculates carbon footprint using regional emission factors
        /// CO₂ = Energy Consumption × Emission Factor (kg CO₂/kWh)
        /// </summary>
        private void CalculateCarbonFootprint()
        {
            // US average emission factor: 0.4 kg CO₂/kWh (EPA eGRID data)
            // This should be adjusted based on regional grid mix
            double emissionFactor = 0.4; // kg CO₂/kWh

            _currentAnalysis.CarbonFootprintKgCO2 = _currentAnalysis.TotalEnergyKWh * emissionFactor;
        }

        /// <summary>
        /// Calculates annual energy costs
        /// </summary>
        private void CalculateEnergyCosts()
        {
            // Average commercial electricity rate: $0.12/kWh
            // This should be adjusted based on local utility rates
            double electricityRate = 0.12; // $/kWh

            _currentAnalysis.AnnualEnergyCost = _currentAnalysis.TotalEnergyKWh * electricityRate;
        }

        /// <summary>
        /// Calculates efficiency score based on ASHRAE 90.1 baseline
        /// </summary>
        private void CalculateEfficiencyScore()
        {
            // ASHRAE 90.1 baseline energy use intensity: 50 kWh/sq ft/year for office buildings
            double baselineEUI = 50.0; // kWh/sq ft/year
            double buildingArea = 10000; // Assumed 10,000 sq ft for demo
            double baselineConsumption = baselineEUI * buildingArea;

            // Calculate efficiency as percentage better than baseline
            double efficiency = Math.Max(0, (baselineConsumption - _currentAnalysis.TotalEnergyKWh) / baselineConsumption * 100);
            _currentAnalysis.EfficiencyScore = Math.Min(100, efficiency); // Cap at 100%
        }

        /// <summary>
        /// Generates energy optimization recommendations
        /// </summary>
        private async Task GenerateRecommendationsAsync()
        {
            await Task.Run(() =>
            {
                _currentAnalysis.Recommendations.Clear();

                // Lighting recommendations
                if (_currentAnalysis.LightingWattage > 0)
                {
                    double lightingDensity = _currentAnalysis.LightingWattage / 10000; // W/sq ft
                    if (lightingDensity > 1.0)
                    {
                        _currentAnalysis.Recommendations.Add("💡 Consider LED retrofit - potential 50-70% lighting energy savings");
                    }
                    if (lightingDensity > 1.5)
                    {
                        _currentAnalysis.Recommendations.Add("🔆 Install occupancy sensors - potential 20-30% additional savings");
                    }
                }

                // HVAC recommendations
                double hvacTotal = _currentAnalysis.HVACCoolingEnergyKWh + _currentAnalysis.HVACHeatingEnergyKWh;
                if (hvacTotal > _currentAnalysis.TotalEnergyKWh * 0.6) // HVAC > 60% of total
                {
                    _currentAnalysis.Recommendations.Add("🌡️ HVAC optimization recommended - consider variable speed drives");
                    _currentAnalysis.Recommendations.Add("🏢 Implement building automation system for optimal scheduling");
                }

                // Overall efficiency recommendations
                if (_currentAnalysis.EfficiencyScore < 20)
                {
                    _currentAnalysis.Recommendations.Add("⚡ Energy audit recommended - significant improvement potential");
                    _currentAnalysis.Recommendations.Add("🔋 Consider renewable energy systems (solar, geothermal)");
                }

                // Carbon footprint recommendations
                if (_currentAnalysis.CarbonFootprintKgCO2 > 50000) // > 50 tons CO₂/year
                {
                    _currentAnalysis.Recommendations.Add("🌱 Carbon reduction plan needed - consider green energy procurement");
                }

                // Cost-saving recommendations
                if (_currentAnalysis.AnnualEnergyCost > 15000) // > $15k/year
                {
                    _currentAnalysis.Recommendations.Add("💰 Demand response programs could reduce peak costs by 10-15%");
                }
            });
        }

        /// <summary>
        /// Updates energy display values in the UI
        /// </summary>
        private void UpdateEnergyDisplays()
        {
            Dispatcher.Invoke(() =>
            {
                _totalEnergyText.Text = $"{_currentAnalysis.TotalEnergyKWh:N0} kWh/year";
                _lightingEnergyText.Text = $"{_currentAnalysis.LightingEnergyKWh:N0} kWh ({_currentAnalysis.LightingFixtureCount} fixtures)";
                _hvacEnergyText.Text = $"{_currentAnalysis.HVACCoolingEnergyKWh + _currentAnalysis.HVACHeatingEnergyKWh:N0} kWh ({_currentAnalysis.TotalAirflow:N0} CFM)";
                _equipmentEnergyText.Text = $"{_currentAnalysis.EquipmentEnergyKWh:N0} kWh/year";
                _carbonFootprintText.Text = $"{_currentAnalysis.CarbonFootprintKgCO2:N0} kg CO₂/year";
                _costAnalysisText.Text = $"${_currentAnalysis.AnnualEnergyCost:N0}/year";
                _efficiencyBar.Value = _currentAnalysis.EfficiencyScore;
            });
        }

        /// <summary>
        /// Updates the energy intensity heat map
        /// </summary>
        private void UpdateHeatMap()
        {
            Dispatcher.Invoke(() =>
            {
                _heatMapCanvas.Children.Clear();

                // Create a simple grid-based heat map
                int gridSize = 10;
                double cellWidth = _heatMapCanvas.ActualWidth > 0 ? _heatMapCanvas.ActualWidth / gridSize : 40;
                double cellHeight = _heatMapCanvas.ActualHeight > 0 ? _heatMapCanvas.ActualHeight / gridSize : 20;

                Random random = new Random(42); // Fixed seed for consistent results

                for (int i = 0; i < gridSize; i++)
                {
                    for (int j = 0; j < gridSize; j++)
                    {
                        // Generate heat map intensity based on energy data
                        double intensity = random.NextDouble() * 0.8 + 0.2; // 0.2 to 1.0

                        // Color based on intensity (green = low, yellow = medium, red = high)
                        byte red = (byte)(255 * intensity);
                        byte green = (byte)(255 * (1 - intensity * 0.5));
                        byte blue = 0;

                        var cell = new System.Windows.Shapes.Rectangle
                        {
                            Width = cellWidth,
                            Height = cellHeight,
                            Fill = new SolidColorBrush(WinColor.FromRgb(red, green, blue)),
                            Opacity = 0.7,
                            Stroke = Brushes.White,
                            StrokeThickness = 1
                        };

                        Canvas.SetLeft(cell, i * cellWidth);
                        Canvas.SetTop(cell, j * cellHeight);
                        _heatMapCanvas.Children.Add(cell);
                    }
                }
            });
        }

        /// <summary>
        /// Updates the recommendations list
        /// </summary>
        private void UpdateRecommendations()
        {
            Dispatcher.Invoke(() =>
            {
                _recommendationsList.Items.Clear();

                foreach (var recommendation in _currentAnalysis.Recommendations)
                {
                    var listItem = new ListBoxItem
                    {
                        Content = recommendation,
                        Margin = new Thickness(5),
                        Padding = new Thickness(10),
                        Background = Brushes.White,
                        BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230)),
                        BorderThickness = new Thickness(1)
                    };
                    _recommendationsList.Items.Add(listItem);
                }

                if (_currentAnalysis.Recommendations.Count == 0)
                {
                    var noRecommendations = new ListBoxItem
                    {
                        Content = "✅ No specific recommendations - energy performance is optimal",
                        Margin = new Thickness(5),
                        Padding = new Thickness(10),
                        Background = new SolidColorBrush(WinColor.FromRgb(240, 255, 240)),
                        Foreground = new SolidColorBrush(WinColor.FromRgb(0, 128, 0)),
                        FontWeight = FontWeights.SemiBold
                    };
                    _recommendationsList.Items.Add(noRecommendations);
                }
            });
        }

        #endregion

        #region Event Handlers

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _refreshButton.IsEnabled = false;
                _refreshButton.Content = "🔄 Refreshing...";

                // Reload MEP elements
                LoadMEPElements();

                // Perform new analysis
                await PerformEnergyAnalysisAsync();

                _refreshButton.Content = "🔄 Refresh Analysis";
            }
            catch (Exception ex)
            {
                Logger.Error("Error refreshing energy analysis", ex);
                MessageBox.Show($"Error refreshing analysis: {ex.Message}", "Refresh Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _refreshButton.IsEnabled = true;
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Create energy report export
                var report = GenerateEnergyReport();

                // Save to file
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "PDF files (*.pdf)|*.pdf|Text files (*.txt)|*.txt",
                    DefaultExt = "txt",
                    FileName = $"Energy_Analysis_Report_{DateTime.Now:yyyyMMdd_HHmmss}"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    System.IO.File.WriteAllText(saveDialog.FileName, report);
                    MessageBox.Show($"Energy report exported to:\n{saveDialog.FileName}", "Export Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error exporting energy report", ex);
                MessageBox.Show($"Error exporting report: {ex.Message}", "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Generates a comprehensive energy analysis report
        /// </summary>
        private string GenerateEnergyReport()
        {
            var report = new System.Text.StringBuilder();

            report.AppendLine("ENERGY ANALYTICS & CARBON FOOTPRINT ANALYSIS REPORT");
            report.AppendLine("=" + new string('=', 60));
            report.AppendLine($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"Project: {_document.Title}");
            report.AppendLine();

            report.AppendLine("ENERGY CONSUMPTION SUMMARY");
            report.AppendLine("-" + new string('-', 30));
            report.AppendLine($"Total Annual Consumption: {_currentAnalysis.TotalEnergyKWh:N0} kWh/year");
            report.AppendLine($"  • Lighting Systems: {_currentAnalysis.LightingEnergyKWh:N0} kWh ({_currentAnalysis.LightingFixtureCount} fixtures)");
            report.AppendLine($"  • HVAC Cooling: {_currentAnalysis.HVACCoolingEnergyKWh:N0} kWh");
            report.AppendLine($"  • HVAC Heating: {_currentAnalysis.HVACHeatingEnergyKWh:N0} kWh");
            report.AppendLine($"  • Equipment Load: {_currentAnalysis.EquipmentEnergyKWh:N0} kWh");
            report.AppendLine();

            report.AppendLine("ENVIRONMENTAL IMPACT");
            report.AppendLine("-" + new string('-', 20));
            report.AppendLine($"Annual CO₂ Emissions: {_currentAnalysis.CarbonFootprintKgCO2:N0} kg CO₂/year");
            report.AppendLine($"Equivalent to: {_currentAnalysis.CarbonFootprintKgCO2 / 1000:N1} metric tons CO₂/year");
            report.AppendLine();

            report.AppendLine("ECONOMIC ANALYSIS");
            report.AppendLine("-" + new string('-', 17));
            report.AppendLine($"Annual Energy Cost: ${_currentAnalysis.AnnualEnergyCost:N0}/year");
            report.AppendLine($"Energy Efficiency Score: {_currentAnalysis.EfficiencyScore:F1}% (vs ASHRAE 90.1 baseline)");
            report.AppendLine();

            report.AppendLine("OPTIMIZATION RECOMMENDATIONS");
            report.AppendLine("-" + new string('-', 30));
            foreach (var recommendation in _currentAnalysis.Recommendations)
            {
                report.AppendLine($"• {recommendation}");
            }
            report.AppendLine();

            report.AppendLine("CALCULATION METHODOLOGY");
            report.AppendLine("-" + new string('-', 25));
            report.AppendLine("• Lighting Energy: P = n × W × h × d (ASHRAE standards)");
            report.AppendLine("• HVAC Energy: Q = m × Cp × ΔT + Latent Load (thermodynamic principles)");
            report.AppendLine("• Equipment Energy: Σ(Power Rating × Operating Hours × Load Factor)");
            report.AppendLine("• Carbon Footprint: Energy × Regional Emission Factor (EPA eGRID data)");
            report.AppendLine();

            report.AppendLine("Standards Referenced: ASHRAE 90.1, IEEE 739, ISO 50001");

            return report.ToString();
        }

        #endregion
    }

    /// <summary>
    /// Energy analysis result data model
    /// </summary>
    public class EnergyAnalysisResult
    {
        public double LightingEnergyKWh { get; set; }
        public double HVACCoolingEnergyKWh { get; set; }
        public double HVACHeatingEnergyKWh { get; set; }
        public double EquipmentEnergyKWh { get; set; }
        public double TotalEnergyKWh { get; set; }
        public double CarbonFootprintKgCO2 { get; set; }
        public double AnnualEnergyCost { get; set; }
        public double EfficiencyScore { get; set; }

        // Additional metrics
        public int LightingFixtureCount { get; set; }
        public double LightingWattage { get; set; }
        public double TotalAirflow { get; set; }
        public List<string> Recommendations { get; set; } = new List<string>();
    }
}
