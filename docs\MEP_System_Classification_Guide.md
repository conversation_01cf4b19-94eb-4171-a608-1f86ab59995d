# MEP System Classification Guide

## Overview

The MEP System Classification feature in RevitAddIn2025 provides an advanced method for categorizing and analyzing MEP (Mechanical, Electrical, Plumbing) systems using AI-powered transformer models. This guide explains how the system works and how to get the most from this feature.

## System Types

The MEP Transformer can identify and classify the following detailed system types:

### Mechanical Systems
- **Supply Air** - Systems delivering conditioned air to spaces
- **Return Air** - Systems returning air from spaces to air handlers
- **Exhaust Air** - Systems removing air from spaces to the exterior
- **Outdoor Air** - Systems bringing fresh air into the building
- **Mixed Air** - Systems where multiple air streams are combined

### Plumbing Systems
- **Domestic Cold Water** - Cold water distribution systems
- **Domestic Hot Water** - Heated water distribution systems
- **Sanitary Sewer** - Waste water drainage systems
- **Storm Drainage** - Rainwater/storm drainage systems
- **Natural Gas** - Gas distribution systems
- **Medical Gas** - Specialized medical gas systems
- **Fire Protection** - Sprinkler and other fire suppression systems

### Electrical Systems
- **Power Distribution** - Main electrical distribution systems
- **Lighting** - Lighting circuits and systems
- **DataComm** - Data and communication systems
- **Fire Alarm** - Fire detection and alarm systems
- **Security System** - Access control and security systems
- **Emergency Power** - Backup power and emergency systems

## System Classification Methods

The classifier uses several methods to determine the system type:

1. **System Name Analysis** - Examines system names for keywords like "Supply", "Return", "Cold Water", etc.
2. **Parameter Analysis** - Checks Revit parameters including system type parameters
3. **Element Type Analysis** - Uses element types and categories as context
4. **Element Name Analysis** - Examines element names for system indicators

## System Priority Levels

Each system type is assigned a priority level for clash detection and resolution:

| Priority | System Types |
|----------|-------------|
| 10 (Highest) | Fire Protection, Fire Alarm, Emergency Power |
| 9 | Medical Gas |
| 8 | Supply Air, Return Air |
| 7 | Sanitary Sewer, Domestic Cold Water |
| 6 | Exhaust Air, Outdoor Air |
| 5 | Domestic Hot Water, Storm Drainage, Natural Gas |
| 4 | Power Distribution |
| 3 | Lighting |
| 2 | DataComm, Security System |
| 1 | Mixed Air |
| 0 (Lowest) | Unknown |

These priorities are used when resolving clashes between systems, with higher priority systems generally maintained in position while lower priority systems are rerouted.

## Recommended Clearances

Each system type has recommended minimum clearances for maintenance and code compliance:

| System Type | Recommended Clearance |
|------------|----------------------|
| Fire Protection | 450mm |
| Supply Air, Return Air, Domestic Cold/Hot Water | 300mm |
| Exhaust Air, Outdoor Air, Sanitary, Storm, Power, Emergency | 200mm |
| Gas, Medical Gas, Lighting, DataComm, Fire Alarm, Security | 150mm |
| Other Systems | 100mm |

These clearances are used during clash detection to identify insufficient spacing between systems.

## Using the System Classification in Analysis

The system classification enhances several analysis types:

1. **Clash Detection** - Prioritizes clashes based on system types
2. **Energy Efficiency** - Analyzes efficiency based on system-specific criteria
3. **Code Compliance** - Checks systems against applicable code requirements
4. **Layout Optimization** - Recommends improved layouts based on system priorities

## Visualization

The MEP Transformer dialog visualizes systems using a color-coding scheme:

- **Mechanical** - Blue shades
- **Plumbing** - Green shades
- **Fire Protection** - Red
- **Electrical** - Orange/Yellow shades

This color coding makes it easy to identify system types in the analysis results.

## Implementation Benefits

The enhanced system classification offers several benefits:

1. **More Accurate Clash Detection** - Better prioritization of clashes
2. **Improved Code Compliance** - More targeted compliance checks
3. **Enhanced Energy Analysis** - System-specific efficiency calculations
4. **Better AI Training** - Enables the transformer model to learn system-specific patterns
5. **More Detailed Reporting** - Breakdown of issues by system type

## Integration with AI Transformer

The system classification data feeds into the AI transformer model to enhance:

1. **Element Embedding** - More meaningful representation of elements
2. **Spatial Relationship Analysis** - Better understanding of system interactions
3. **Pattern Recognition** - Identification of common issues by system type
4. **Optimization Suggestions** - More targeted recommendations

## Future Enhancements

Future versions will include:

1. **Machine Learning Integration** - Self-improving classification based on project data
2. **Additional System Types** - More granular classification
3. **Custom System Definitions** - User-defined system types and priorities
4. **Historical Analysis** - Learning from past projects
5. **Predictive Analysis** - Anticipating potential issues before they occur

For more information on using the MEP System Classification feature, see the full [MEP Transformer User Guide](./MEP_Transformer_User_Guide.md).
