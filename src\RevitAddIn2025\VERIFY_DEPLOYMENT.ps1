# Quick deployment verification
$correctDll = "C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2025\RevitAddIn2025\RevitAddIn2025.dll"
$correctAddin = "C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2025\RevitAddIn2025.addin"

Write-Host "DEPLOYMENT VERIFICATION" -ForegroundColor Cyan
if (Test-Path $correctDll) {
    $size = [math]::Round((Get-Item $correctDll).Length / 1024, 2)
    Write-Host "✅ DLL exists ($size KB)" -ForegroundColor Green
} else {
    Write-Host "❌ DLL missing" -ForegroundColor Red
}

if (Test-Path $correctAddin) {
    Write-Host "✅ .addin file exists" -ForegroundColor Green
} else {
    Write-Host "❌ .addin file missing" -ForegroundColor Red
}
