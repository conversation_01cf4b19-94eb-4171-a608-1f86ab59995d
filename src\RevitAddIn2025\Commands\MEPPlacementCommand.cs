using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.UI;
using RevitAddIn2025.AI;
using RevitAddIn2025.Models;
using RevitAddIn2025.Utilities;

namespace RevitAddIn2025.Commands
{
    /// <summary>
    /// Command to launch the Advanced MEP Placement System
    /// Integrates vector-based placement with Energy Analytics and Clash Detection
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class MEPPlacementCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                Logger.Info("Launching Advanced MEP Placement System");

                var uiApp = commandData.Application;
                var document = uiApp.ActiveUIDocument?.Document;

                if (document == null)
                {
                    message = "No active document found. Please open a Revit project.";
                    Logger.Warning("No active document for MEP Placement");
                    return Result.Failed;
                }

                // Validate document has MEP elements or rooms
                if (!ValidateDocumentForPlacement(document))
                {
                    message = "Document must contain rooms or spaces for MEP placement analysis.";
                    Logger.Warning("Document validation failed for MEP placement");
                    return Result.Failed;
                }

                // Initialize AI model
                var aiModel = new MEPTransformerModel();

                // Launch MEP Placement Window
                var placementWindow = new MEPPlacementWindow(uiApp, aiModel);
                placementWindow.ShowDialog();

                Logger.Info("MEP Placement System completed successfully");
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                Logger.Error("Error in MEP Placement Command", ex);
                message = $"Error launching MEP Placement System: {ex.Message}";
                return Result.Failed;
            }
        }

        /// <summary>
        /// Validates that the document is suitable for MEP placement analysis
        /// </summary>
        private bool ValidateDocumentForPlacement(Document document)
        {
            try
            {
                // Check for rooms/spaces
                var roomCollector = new FilteredElementCollector(document)
                    .OfCategory(BuiltInCategory.OST_Rooms)
                    .WhereElementIsNotElementType();

                if (roomCollector.GetElementCount() > 0)
                {
                    Logger.Info($"Found {roomCollector.GetElementCount()} rooms for placement analysis");
                    return true;
                }

                // Check for areas/spaces
                var areaCollector = new FilteredElementCollector(document)
                    .OfCategory(BuiltInCategory.OST_Areas)
                    .WhereElementIsNotElementType();

                if (areaCollector.GetElementCount() > 0)
                {
                    Logger.Info($"Found {areaCollector.GetElementCount()} areas for placement analysis");
                    return true;
                }

                // Check for ceilings
                var ceilingCollector = new FilteredElementCollector(document)
                    .OfClass(typeof(Ceiling))
                    .WhereElementIsNotElementType();

                if (ceilingCollector.GetElementCount() > 0)
                {
                    Logger.Info($"Found {ceilingCollector.GetElementCount()} ceilings for placement analysis");
                    return true;
                }

                Logger.Warning("No suitable elements found for MEP placement analysis");
                return false;
            }
            catch (Exception ex)
            {
                Logger.Error("Error validating document for placement", ex);
                return false;
            }
        }
    }

    /// <summary>
    /// Command to launch Energy Analytics integration
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class EnergyAnalyticsCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                Logger.Info("Launching Energy Analytics Window");

                var uiApp = commandData.Application;
                var document = uiApp.ActiveUIDocument?.Document;

                if (document == null)
                {
                    message = "No active document found. Please open a Revit project.";
                    Logger.Warning("No active document for Energy Analytics");
                    return Result.Failed;
                }

                // Launch Energy Analytics Window
                var energyWindow = new EnergyAnalyticsWindow(uiApp);
                energyWindow.ShowDialog();

                Logger.Info("Energy Analytics completed successfully");
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                Logger.Error("Error in Energy Analytics Command", ex);
                message = $"Error launching Energy Analytics: {ex.Message}";
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command to launch integrated MEP Dashboard
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class MEPDashboardCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                Logger.Info("Launching Integrated MEP Dashboard");

                var uiApp = commandData.Application;
                var document = uiApp.ActiveUIDocument?.Document;

                if (document == null)
                {
                    message = "No active document found. Please open a Revit project.";
                    Logger.Warning("No active document for MEP Dashboard");
                    return Result.Failed;
                }

                // Launch Dashboard Window
                var dashboardWindow = new DashboardWindow(uiApp);
                dashboardWindow.ShowDialog();

                Logger.Info("MEP Dashboard completed successfully");
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                Logger.Error("Error in MEP Dashboard Command", ex);
                message = $"Error launching MEP Dashboard: {ex.Message}";
                return Result.Failed;
            }
        }
    }

    /// <summary>
    /// Command for comprehensive MEP analysis workflow
    /// </summary>
    [Transaction(TransactionMode.Manual)]
    [Regeneration(RegenerationOption.Manual)]
    public class ComprehensiveMEPAnalysisCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                Logger.Info("Starting Comprehensive MEP Analysis Workflow");

                var uiApp = commandData.Application;
                var document = uiApp.ActiveUIDocument?.Document;

                if (document == null)
                {
                    message = "No active document found. Please open a Revit project.";
                    Logger.Warning("No active document for comprehensive analysis");
                    return Result.Failed;
                }

                // Run comprehensive analysis workflow
                Task.Run(async () =>
                {
                    await RunComprehensiveAnalysis(uiApp, document);
                });

                Logger.Info("Comprehensive MEP Analysis initiated successfully");
                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                Logger.Error("Error in Comprehensive MEP Analysis Command", ex);
                message = $"Error launching comprehensive analysis: {ex.Message}";
                return Result.Failed;
            }
        }

        private async Task RunComprehensiveAnalysis(UIApplication uiApp, Document document)
        {
            try
            {
                Logger.Info("Running comprehensive MEP analysis workflow");

                // Step 1: Initialize AI models and engines
                var aiModel = new MEPTransformerModel();
                var ceilingEngine = new CeilingAnalysisEngine(document);
                var vectorEngine = new VectorPlacementEngine(document, aiModel);
                var smartIntegrator = new SmartAnalysisIntegrator(document, aiModel, vectorEngine, ceilingEngine);

                // Step 2: Analyze ceiling types
                Logger.Info("Analyzing ceiling types...");
                var ceilingAnalysis = await ceilingEngine.AnalyzeCeilingTypes();

                // Step 3: Detect MEP elements
                Logger.Info("Detecting MEP elements...");
                var mepElements = await GetMEPElements(document);

                // Step 4: Process through AI model
                Logger.Info("Processing through AI model...");
                var aiResults = await aiModel.ProcessMEPElements(mepElements);

                // Step 5: Generate comprehensive report
                Logger.Info("Generating comprehensive report...");
                await GenerateComprehensiveReport(ceilingAnalysis, mepElements, aiResults);

                Logger.Info("Comprehensive MEP analysis completed successfully");
            }
            catch (Exception ex)
            {
                Logger.Error("Error during comprehensive analysis", ex);
            }
        }

        private async Task<List<Models.MEPElementData>> GetMEPElements(Document document)
        {
            var elements = new List<Models.MEPElementData>();

            await Task.Run(() =>
            {
                try
                {
                    // Get all MEP elements
                    var collector = new FilteredElementCollector(document)
                        .OfClass(typeof(FamilyInstance))
                        .WhereElementIsNotElementType();

                    foreach (FamilyInstance element in collector)
                    {
                        if (IsMEPElement(element))
                        {
                            var mepData = Models.MEPElementData.FromRevitElement(element);
                            if (mepData != null)
                            {
                                elements.Add(mepData);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error("Error getting MEP elements", ex);
                }
            });

            return elements;
        }

        private bool IsMEPElement(FamilyInstance element)
        {
            try
            {
                var category = element.Category;
                if (category == null) return false;

                var categoryName = category.Name.ToLower();

                return categoryName.Contains("lighting") ||
                       categoryName.Contains("electrical") ||
                       categoryName.Contains("mechanical") ||
                       categoryName.Contains("plumbing") ||
                       categoryName.Contains("fire") ||
                       categoryName.Contains("hvac") ||
                       categoryName.Contains("air") ||
                       categoryName.Contains("duct") ||
                       categoryName.Contains("pipe");
            }
            catch
            {
                return false;
            }
        }

        private async Task GenerateComprehensiveReport(
            List<Models.CeilingAnalysisResult> ceilingAnalysis,
            List<Models.MEPElementData> mepElements,
            Models.AIProcessingResult aiResults)
        {
            await Task.Run(() =>
            {
                try
                {
                    Logger.Info("Generating comprehensive MEP analysis report");

                    // Create comprehensive report with all analysis results
                    var reportData = new
                    {
                        Timestamp = DateTime.Now,
                        CeilingAnalysis = ceilingAnalysis,
                        MEPElements = mepElements,
                        AIResults = aiResults,
                        Summary = new
                        {
                            TotalCeilings = ceilingAnalysis.Count,
                            TotalMEPElements = mepElements.Count,
                            AnalysisScore = aiResults?.OverallScore ?? 0.0
                        }
                    };

                    // Save report (implementation would save to file or database)
                    Logger.Info($"Comprehensive report generated with {ceilingAnalysis.Count} ceilings and {mepElements.Count} MEP elements");
                }
                catch (Exception ex)
                {
                    Logger.Error("Error generating comprehensive report", ex);
                }
            });
        }
    }
}
