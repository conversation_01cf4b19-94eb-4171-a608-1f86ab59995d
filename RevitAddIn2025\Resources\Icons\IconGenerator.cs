using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;

namespace RevitAddIn2025.Resources.Icons
{
    /// <summary>
    /// Professional Apple-inspired icon generator for RevitAddIn2025
    /// Creates high-quality 32x32 PNG icons with modern design
    /// </summary>
    public static class IconGenerator
    {
        public static void GenerateAllIcons()
        {
            string iconsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Icons");
            Directory.CreateDirectory(iconsPath);

            // Dashboard Icon - Analytics theme
            CreateAppleIcon("📊", Color.FromArgb(0, 122, 255), Color.White, Path.Combine(iconsPath, "dashboard_icon.png"));
            
            // Settings Icon - Configuration theme  
            CreateAppleIcon("⚙", Color.FromArgb(142, 142, 147), Color.White, Path.Combine(iconsPath, "settings_icon.png"));
            
            // Test Icon - Verification theme
            CreateAppleIcon("🎯", Color.FromArgb(52, 199, 89), Color.White, Path.Combine(iconsPath, "test_icon.png"));
            
            // MEP AI Icon - Brain/AI theme
            CreateAppleIcon("🧠", Color.FromArgb(255, 59, 48), Color.White, Path.Combine(iconsPath, "mep_icon.png"));
            
            // Help Icon - Support theme
            CreateAppleIcon("?", Color.FromArgb(255, 149, 0), Color.White, Path.Combine(iconsPath, "help_icon.png"));
            
            // About Icon - Info theme
            CreateAppleIcon("i", Color.FromArgb(88, 86, 214), Color.White, Path.Combine(iconsPath, "about_icon.png"));
            
            // Dev Reload Icon - Refresh theme
            CreateAppleIcon("↻", Color.FromArgb(0, 199, 190), Color.White, Path.Combine(iconsPath, "reload_icon.png"));
        }

        private static void CreateAppleIcon(string symbol, Color backgroundColor, Color iconColor, string outputPath)
        {
            using (var bitmap = new Bitmap(32, 32))
            using (var graphics = Graphics.FromImage(bitmap))
            {
                // Enable high-quality rendering
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;
                graphics.CompositingQuality = CompositingQuality.HighQuality;

                // Create gradient background (Apple-style)
                var rect = new Rectangle(0, 0, 32, 32);
                var lightColor = Color.FromArgb(Math.Min(255, backgroundColor.R + 30), 
                                              Math.Min(255, backgroundColor.G + 30), 
                                              Math.Min(255, backgroundColor.B + 30));
                
                using (var brush = new LinearGradientBrush(rect, lightColor, backgroundColor, 45f))
                {
                    // Draw rounded rectangle background
                    using (var path = CreateRoundedRectanglePath(rect, 6))
                    {
                        graphics.FillPath(brush, path);
                        
                        // Add subtle border
                        using (var borderPen = new Pen(Color.FromArgb(50, 0, 0, 0), 1))
                        {
                            graphics.DrawPath(borderPen, path);
                        }
                    }
                }

                // Draw icon symbol
                using (var font = new Font("Segoe UI", 14, FontStyle.Bold))
                using (var textBrush = new SolidBrush(iconColor))
                {
                    // Center the text
                    var textSize = graphics.MeasureString(symbol, font);
                    var x = (32 - textSize.Width) / 2;
                    var y = (32 - textSize.Height) / 2;
                    
                    graphics.DrawString(symbol, font, textBrush, x, y);
                }

                // Add subtle highlight
                using (var highlightBrush = new SolidBrush(Color.FromArgb(30, 255, 255, 255)))
                {
                    var highlightRect = new Rectangle(2, 2, 28, 14);
                    graphics.FillEllipse(highlightBrush, highlightRect);
                }

                // Save the icon
                bitmap.Save(outputPath, ImageFormat.Png);
            }
        }

        private static GraphicsPath CreateRoundedRectanglePath(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            int diameter = radius * 2;
            
            // Top-left arc
            path.AddArc(rect.X, rect.Y, diameter, diameter, 180, 90);
            
            // Top-right arc
            path.AddArc(rect.Right - diameter, rect.Y, diameter, diameter, 270, 90);
            
            // Bottom-right arc
            path.AddArc(rect.Right - diameter, rect.Bottom - diameter, diameter, diameter, 0, 90);
            
            // Bottom-left arc
            path.AddArc(rect.X, rect.Bottom - diameter, diameter, diameter, 90, 90);
            
            path.CloseFigure();
            return path;
        }
    }
}
