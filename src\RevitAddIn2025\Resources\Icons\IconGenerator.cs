using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;

namespace RevitAddIn2025.Resources.Icons
{
    /// <summary>
    /// Professional Apple-inspired icon generator for RevitAddIn2025
    /// Creates high-quality 32x32 PNG icons with modern design
    /// </summary>
    public static class IconGenerator
    {
        public static void GenerateAllIcons()
        {
            string iconsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Icons");
            Directory.CreateDirectory(iconsPath);

            // Dashboard Icon - Analytics theme
            CreateAppleIcon("📊", Color.FromArgb(0, 122, 255), Color.White, Path.Combine(iconsPath, "dashboard_icon.png"));
            
            // Settings Icon - Configuration theme  
            CreateAppleIcon("⚙", Color.FromArgb(142, 142, 147), Color.White, Path.Combine(iconsPath, "settings_icon.png"));
            
            // Test Icon - Verification theme
            CreateAppleIcon("🎯", Color.FromArgb(52, 199, 89), Color.White, Path.Combine(iconsPath, "test_icon.png"));
            
            // Help Icon - Support theme
            CreateAppleIcon("❓", Color.FromArgb(255, 149, 0), Color.White, Path.Combine(iconsPath, "help_icon.png"));
            
            // About Icon - Information theme
            CreateAppleIcon("ℹ", Color.FromArgb(175, 82, 222), Color.White, Path.Combine(iconsPath, "about_icon.png"));
            
            // MEP AI Icon - Brain/AI theme
            CreateAppleIcon("🧠", Color.FromArgb(255, 45, 85), Color.White, Path.Combine(iconsPath, "mep_icon.png"));
            
            // Dev Reload Icon - Refresh theme
            CreateAppleIcon("🔄", Color.FromArgb(90, 200, 250), Color.White, Path.Combine(iconsPath, "reload_icon.png"));
        }

        private static void CreateAppleIcon(string emoji, Color backgroundColor, Color textColor, string filePath)
        {
            try
            {
                const int size = 32;
                using (var bitmap = new Bitmap(size, size))
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    // Enable high-quality rendering
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;
                    graphics.CompositingQuality = CompositingQuality.HighQuality;
                    graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;

                    // Create rounded rectangle background with gradient
                    using (var path = CreateRoundedRectanglePath(0, 0, size, size, 6))
                    {
                        // Create gradient brush
                        using (var gradientBrush = new LinearGradientBrush(
                            new Rectangle(0, 0, size, size),
                            Color.FromArgb(255, backgroundColor.R + 20, backgroundColor.G + 20, backgroundColor.B + 20),
                            backgroundColor,
                            LinearGradientMode.Vertical))
                        {
                            graphics.FillPath(gradientBrush, path);
                        }

                        // Add subtle border
                        using (var borderPen = new Pen(Color.FromArgb(50, Color.Black), 1))
                        {
                            graphics.DrawPath(borderPen, path);
                        }
                    }

                    // Draw emoji/text
                    using (var font = new Font("Segoe UI Emoji", 16, FontStyle.Regular))
                    {
                        var textBrush = new SolidBrush(textColor);
                        var textSize = graphics.MeasureString(emoji, font);
                        var x = (size - textSize.Width) / 2;
                        var y = (size - textSize.Height) / 2;
                        
                        graphics.DrawString(emoji, font, textBrush, x, y);
                    }

                    // Save as PNG
                    bitmap.Save(filePath, System.Drawing.Imaging.ImageFormat.Png);
                }
            }
            catch (Exception ex)
            {
                // Fallback to simple colored square
                CreateSimpleIcon(backgroundColor, filePath);
                System.Diagnostics.Debug.WriteLine($"Icon generation failed for {filePath}: {ex.Message}");
            }
        }

        private static void CreateSimpleIcon(Color color, string filePath)
        {
            try
            {
                const int size = 32;
                using (var bitmap = new Bitmap(size, size))
                using (var graphics = Graphics.FromImage(bitmap))
                {
                    graphics.SmoothingMode = SmoothingMode.AntiAlias;
                    
                    // Create rounded rectangle
                    using (var path = CreateRoundedRectanglePath(2, 2, size - 4, size - 4, 4))
                    {
                        using (var brush = new SolidBrush(color))
                        {
                            graphics.FillPath(brush, path);
                        }
                        
                        using (var pen = new Pen(Color.White, 2))
                        {
                            graphics.DrawPath(pen, path);
                        }
                    }

                    bitmap.Save(filePath, System.Drawing.Imaging.ImageFormat.Png);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Simple icon creation failed for {filePath}: {ex.Message}");
            }
        }

        private static GraphicsPath CreateRoundedRectanglePath(int x, int y, int width, int height, int radius)
        {
            var path = new GraphicsPath();
            
            // Top-left corner
            path.AddArc(x, y, radius * 2, radius * 2, 180, 90);
            
            // Top edge
            path.AddLine(x + radius, y, x + width - radius, y);
            
            // Top-right corner
            path.AddArc(x + width - radius * 2, y, radius * 2, radius * 2, 270, 90);
            
            // Right edge
            path.AddLine(x + width, y + radius, x + width, y + height - radius);
            
            // Bottom-right corner
            path.AddArc(x + width - radius * 2, y + height - radius * 2, radius * 2, radius * 2, 0, 90);
            
            // Bottom edge
            path.AddLine(x + width - radius, y + height, x + radius, y + height);
            
            // Bottom-left corner
            path.AddArc(x, y + height - radius * 2, radius * 2, radius * 2, 90, 90);
            
            // Left edge
            path.AddLine(x, y + height - radius, x, y + radius);
            
            path.CloseFigure();
            return path;
        }
    }
}
