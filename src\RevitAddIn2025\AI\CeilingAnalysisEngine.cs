using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using RevitAddIn2025.Models;
using RevitAddIn2025.Utilities;

namespace RevitAddIn2025.AI
{
    /// <summary>
    /// Advanced False Ceiling Recognition and Analysis Engine
    /// Detects, categorizes, and analyzes different ceiling types for optimal MEP placement
    /// </summary>
    public class CeilingAnalysisEngine
    {
        private readonly Document _document;
        
        // Ceiling analysis constants
        private const double GRID_DETECTION_TOLERANCE = 0.5; // 6 inches
        private const double MIN_CEILING_HEIGHT = 7.0; // 7 feet minimum
        private const double MAX_CEILING_HEIGHT = 15.0; // 15 feet maximum
        
        public CeilingAnalysisEngine(Document document)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
        }

        /// <summary>
        /// Analyzes and categorizes ceiling types in the building
        /// </summary>
        public async Task<List<CeilingAnalysisResult>> AnalyzeCeilingTypes()
        {
            var results = new List<CeilingAnalysisResult>();

            try
            {
                Logger.Info("Starting comprehensive ceiling analysis");

                // Get all ceiling elements
                var ceilings = await GetAllCeilings();
                
                foreach (var ceiling in ceilings)
                {
                    var analysisResult = await AnalyzeCeilingElement(ceiling);
                    if (analysisResult != null)
                    {
                        results.Add(analysisResult);
                    }
                }

                Logger.Info($"Analyzed {results.Count} ceiling elements");
                return results;
            }
            catch (Exception ex)
            {
                Logger.Error("Error during ceiling analysis", ex);
                throw;
            }
        }

        /// <summary>
        /// Analyzes a specific ceiling element and determines its type and properties
        /// </summary>
        private async Task<CeilingAnalysisResult> AnalyzeCeilingElement(Ceiling ceiling)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var result = new CeilingAnalysisResult
                    {
                        CeilingId = ceiling.Id,
                        CeilingName = ceiling.Name
                    };

                    // Determine ceiling type
                    result.CeilingType = DetermineCeilingType(ceiling);
                    
                    // Analyze structural properties
                    result.StructuralProperties = AnalyzeStructuralProperties(ceiling);
                    
                    // Calculate load-bearing capacity
                    result.LoadBearingCapacity = CalculateLoadBearingCapacity(ceiling, result.CeilingType);
                    
                    // Determine mounting requirements
                    result.MountingRequirements = DetermineMountingRequirements(ceiling, result.CeilingType);
                    
                    // Analyze accessibility
                    result.AccessibilityRating = AnalyzeAccessibility(ceiling, result.CeilingType);
                    
                    // Calculate ceiling geometry
                    result.GeometryData = AnalyzeCeilingGeometry(ceiling);
                    
                    // Determine MEP integration capabilities
                    result.MEPIntegrationCapabilities = AnalyzeMEPIntegration(ceiling, result.CeilingType);

                    Logger.Info($"Analyzed ceiling: {result.CeilingName} - Type: {result.CeilingType}");
                    return result;
                }
                catch (Exception ex)
                {
                    Logger.Error($"Error analyzing ceiling {ceiling.Id}", ex);
                    return null;
                }
            });
        }

        /// <summary>
        /// Determines the specific type of false ceiling
        /// </summary>
        private FalseCeilingType DetermineCeilingType(Ceiling ceiling)
        {
            try
            {
                // Get ceiling type and material information
                var ceilingType = ceiling.CeilingType;
                var materialIds = ceilingType.GetMaterialIds(false);
                
                // Analyze ceiling name and type for keywords
                string ceilingName = ceiling.Name?.ToLower() ?? "";
                string typeName = ceilingType.Name?.ToLower() ?? "";
                
                // Grid ceiling detection
                if (ContainsKeywords(ceilingName, typeName, new[] { "grid", "t-bar", "suspended", "lay-in", "drop" }))
                {
                    return FalseCeilingType.GridCeiling;
                }
                
                // Gypsum board detection
                if (ContainsKeywords(ceilingName, typeName, new[] { "gypsum", "drywall", "plasterboard", "sheetrock" }))
                {
                    return FalseCeilingType.GypsumBoard;
                }
                
                // Metal panel detection
                if (ContainsKeywords(ceilingName, typeName, new[] { "metal", "aluminum", "steel", "panel" }))
                {
                    return FalseCeilingType.MetalPanel;
                }
                
                // Open cell/linear detection
                if (ContainsKeywords(ceilingName, typeName, new[] { "open", "linear", "baffle", "cell" }))
                {
                    return FalseCeilingType.OpenCell;
                }
                
                // Acoustic tile detection
                if (ContainsKeywords(ceilingName, typeName, new[] { "acoustic", "sound", "tile", "mineral" }))
                {
                    return FalseCeilingType.AcousticTile;
                }
                
                // Integrated service ceiling detection
                if (ContainsKeywords(ceilingName, typeName, new[] { "integrated", "service", "modular", "access" }))
                {
                    return FalseCeilingType.IntegratedService;
                }
                
                // Analyze material properties for further classification
                return AnalyzeMaterialProperties(ceiling, materialIds);
            }
            catch (Exception ex)
            {
                Logger.Error("Error determining ceiling type", ex);
                return FalseCeilingType.Unknown;
            }
        }

        /// <summary>
        /// Analyzes material properties to determine ceiling type
        /// </summary>
        private FalseCeilingType AnalyzeMaterialProperties(Ceiling ceiling, ICollection<ElementId> materialIds)
        {
            try
            {
                foreach (var materialId in materialIds)
                {
                    var material = _document.GetElement(materialId) as Material;
                    if (material == null) continue;
                    
                    string materialName = material.Name?.ToLower() ?? "";
                    
                    // Check material thermal properties
                    var thermalAsset = material.ThermalAsset;
                    if (thermalAsset != null)
                    {
                        // Analyze thermal conductivity for material type
                        var conductivity = thermalAsset.ThermalConductivity;
                        
                        if (conductivity > 200) // High conductivity suggests metal
                            return FalseCeilingType.MetalPanel;
                        else if (conductivity < 1) // Low conductivity suggests insulation/acoustic
                            return FalseCeilingType.AcousticTile;
                    }
                    
                    // Check material appearance for visual cues
                    var appearance = material.AppearanceAssetId;
                    if (appearance != null && appearance != ElementId.InvalidElementId)
                    {
                        // Additional material analysis could be performed here
                    }
                }
                
                return FalseCeilingType.Unknown;
            }
            catch (Exception ex)
            {
                Logger.Error("Error analyzing material properties", ex);
                return FalseCeilingType.Unknown;
            }
        }

        /// <summary>
        /// Analyzes structural properties of the ceiling
        /// </summary>
        private CeilingStructuralProperties AnalyzeStructuralProperties(Ceiling ceiling)
        {
            var properties = new CeilingStructuralProperties();
            
            try
            {
                // Get ceiling thickness
                var ceilingType = ceiling.CeilingType;
                properties.Thickness = GetCeilingThickness(ceilingType);
                
                // Analyze structural composition
                properties.StructuralComposition = AnalyzeStructuralComposition(ceiling);
                
                // Calculate span capabilities
                properties.MaxSpanCapability = CalculateMaxSpan(ceiling, properties.StructuralComposition);
                
                // Determine deflection characteristics
                properties.DeflectionCharacteristics = CalculateDeflectionCharacteristics(ceiling);
                
                // Analyze connection methods
                properties.ConnectionMethods = AnalyzeConnectionMethods(ceiling);
                
                Logger.Info($"Structural analysis completed for ceiling {ceiling.Id}");
            }
            catch (Exception ex)
            {
                Logger.Error("Error analyzing structural properties", ex);
            }
            
            return properties;
        }

        /// <summary>
        /// Calculates load-bearing capacity based on ceiling type and structure
        /// </summary>
        private LoadBearingCapacity CalculateLoadBearingCapacity(Ceiling ceiling, FalseCeilingType ceilingType)
        {
            var capacity = new LoadBearingCapacity();
            
            try
            {
                // Base load capacities by ceiling type (psf - pounds per square foot)
                switch (ceilingType)
                {
                    case FalseCeilingType.GridCeiling:
                        capacity.UniformLoad = 2.0; // 2 psf typical for grid systems
                        capacity.ConcentratedLoad = 10.0; // 10 lbs per point
                        capacity.LinearLoad = 5.0; // 5 lbs per linear foot
                        break;
                        
                    case FalseCeilingType.GypsumBoard:
                        capacity.UniformLoad = 5.0; // 5 psf for gypsum board
                        capacity.ConcentratedLoad = 25.0; // 25 lbs per point
                        capacity.LinearLoad = 10.0; // 10 lbs per linear foot
                        break;
                        
                    case FalseCeilingType.MetalPanel:
                        capacity.UniformLoad = 3.0; // 3 psf for metal panels
                        capacity.ConcentratedLoad = 15.0; // 15 lbs per point
                        capacity.LinearLoad = 8.0; // 8 lbs per linear foot
                        break;
                        
                    case FalseCeilingType.OpenCell:
                        capacity.UniformLoad = 1.5; // 1.5 psf for open cell
                        capacity.ConcentratedLoad = 8.0; // 8 lbs per point
                        capacity.LinearLoad = 4.0; // 4 lbs per linear foot
                        break;
                        
                    case FalseCeilingType.AcousticTile:
                        capacity.UniformLoad = 2.5; // 2.5 psf for acoustic tiles
                        capacity.ConcentratedLoad = 12.0; // 12 lbs per point
                        capacity.LinearLoad = 6.0; // 6 lbs per linear foot
                        break;
                        
                    case FalseCeilingType.IntegratedService:
                        capacity.UniformLoad = 8.0; // 8 psf for integrated service
                        capacity.ConcentratedLoad = 50.0; // 50 lbs per point
                        capacity.LinearLoad = 20.0; // 20 lbs per linear foot
                        break;
                        
                    default:
                        capacity.UniformLoad = 1.0; // Conservative default
                        capacity.ConcentratedLoad = 5.0;
                        capacity.LinearLoad = 2.0;
                        break;
                }
                
                // Apply safety factors
                capacity.SafetyFactor = 2.0; // 2:1 safety factor
                capacity.UniformLoad /= capacity.SafetyFactor;
                capacity.ConcentratedLoad /= capacity.SafetyFactor;
                capacity.LinearLoad /= capacity.SafetyFactor;
                
                // Adjust for ceiling condition and age
                capacity = AdjustForCeilingCondition(capacity, ceiling);
                
                Logger.Info($"Load bearing capacity calculated: Uniform={capacity.UniformLoad:F1} psf, Concentrated={capacity.ConcentratedLoad:F1} lbs");
            }
            catch (Exception ex)
            {
                Logger.Error("Error calculating load bearing capacity", ex);
            }
            
            return capacity;
        }

        /// <summary>
        /// Determines mounting requirements for different ceiling types
        /// </summary>
        private MountingRequirements DetermineMountingRequirements(Ceiling ceiling, FalseCeilingType ceilingType)
        {
            var requirements = new MountingRequirements();
            
            try
            {
                switch (ceilingType)
                {
                    case FalseCeilingType.GridCeiling:
                        requirements.PreferredMountingMethod = MountingMethod.GridClip;
                        requirements.AlternateMountingMethods = new[] { MountingMethod.HangerWire, MountingMethod.StructuralAttachment };
                        requirements.RequiresStructuralSupport = false;
                        requirements.MaxFixtureWeight = 10.0; // 10 lbs per fixture
                        requirements.MinimumSpacing = 2.0; // 2 feet minimum
                        break;
                        
                    case FalseCeilingType.GypsumBoard:
                        requirements.PreferredMountingMethod = MountingMethod.StructuralAttachment;
                        requirements.AlternateMountingMethods = new[] { MountingMethod.ToggleBolt, MountingMethod.HollowWallAnchor };
                        requirements.RequiresStructuralSupport = true;
                        requirements.MaxFixtureWeight = 25.0; // 25 lbs per fixture
                        requirements.MinimumSpacing = 1.5; // 1.5 feet minimum
                        break;
                        
                    case FalseCeilingType.MetalPanel:
                        requirements.PreferredMountingMethod = MountingMethod.MagneticMount;
                        requirements.AlternateMountingMethods = new[] { MountingMethod.ScrewAttachment, MountingMethod.ClipMount };
                        requirements.RequiresStructuralSupport = false;
                        requirements.MaxFixtureWeight = 15.0; // 15 lbs per fixture
                        requirements.MinimumSpacing = 2.0; // 2 feet minimum
                        break;
                        
                    case FalseCeilingType.IntegratedService:
                        requirements.PreferredMountingMethod = MountingMethod.IntegratedRail;
                        requirements.AlternateMountingMethods = new[] { MountingMethod.ModularConnection, MountingMethod.StructuralAttachment };
                        requirements.RequiresStructuralSupport = false;
                        requirements.MaxFixtureWeight = 50.0; // 50 lbs per fixture
                        requirements.MinimumSpacing = 1.0; // 1 foot minimum
                        break;
                        
                    default:
                        requirements.PreferredMountingMethod = MountingMethod.StructuralAttachment;
                        requirements.AlternateMountingMethods = new[] { MountingMethod.HangerWire };
                        requirements.RequiresStructuralSupport = true;
                        requirements.MaxFixtureWeight = 5.0; // Conservative default
                        requirements.MinimumSpacing = 3.0; // 3 feet minimum
                        break;
                }
                
                Logger.Info($"Mounting requirements determined: Method={requirements.PreferredMountingMethod}, MaxWeight={requirements.MaxFixtureWeight} lbs");
            }
            catch (Exception ex)
            {
                Logger.Error("Error determining mounting requirements", ex);
            }
            
            return requirements;
        }

        #region Helper Methods

        private bool ContainsKeywords(string ceilingName, string typeName, string[] keywords)
        {
            return keywords.Any(keyword => 
                ceilingName.Contains(keyword) || typeName.Contains(keyword));
        }

        private async Task<List<Ceiling>> GetAllCeilings()
        {
            return await Task.Run(() =>
            {
                var collector = new FilteredElementCollector(_document)
                    .OfClass(typeof(Ceiling))
                    .WhereElementIsNotElementType();
                
                return collector.Cast<Ceiling>().ToList();
            });
        }

        private double GetCeilingThickness(CeilingType ceilingType)
        {
            try
            {
                // Try to get thickness from compound structure
                var compoundStructure = ceilingType.GetCompoundStructure();
                if (compoundStructure != null)
                {
                    return compoundStructure.GetWidth();
                }
                
                // Fallback to default thickness based on type
                return 0.5; // 6 inches default
            }
            catch
            {
                return 0.5; // Default thickness
            }
        }

        #endregion
    }
}
