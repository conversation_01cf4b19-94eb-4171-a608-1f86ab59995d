using Autodesk.Revit.DB;
using RevitAddIn2025.Utilities;
using System;
using System.Collections.Generic;

namespace RevitAddIn2025.Models
{
    /// <summary>
    /// Model class for storing project analytics data
    /// </summary>
    public class ProjectAnalytics
    {        // Cache for analytics to improve performance
        internal static Dictionary<string, ProjectAnalytics> _cache = new Dictionary<string, ProjectAnalytics>();

        // Cache expiration time (5 minutes)
        private static readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(5);

        // Dictionary to track when each analytics was cached
        internal static Dictionary<string, DateTime> _cacheTimestamps = new Dictionary<string, DateTime>();

        /// <summary>
        /// Clear the analytics cache
        /// </summary>
        public static void ClearCache()
        {
            _cache.Clear();
            _cacheTimestamps.Clear();
            Logger.Info("ProjectAnalytics cache cleared");
        }

        /// <summary>
        /// Check if data for a document is already cached
        /// </summary>
        /// <param name="documentPath">Path to the document</param>
        /// <returns>True if the document data is cached and not expired</returns>
        public static bool IsCached(string documentPath)
        {
            if (string.IsNullOrEmpty(documentPath))
                return false;

            if (!_cache.ContainsKey(documentPath))
                return false;

            // Check if the cache has expired
            if (_cacheTimestamps.ContainsKey(documentPath))
            {
                TimeSpan age = DateTime.Now - _cacheTimestamps[documentPath];
                if (age < CacheExpiration)
                    return true;
            }

            return false;
        }

        #region Properties

        // Basic project information
        public string ProjectName { get; private set; }
        public string ProjectNumber { get; private set; }
        public string ProjectAddress { get; private set; }
        public string ProjectStatus { get; private set; }

        // Element counts
        public int TotalElements { get; private set; }
        public int WallCount { get; private set; }
        public int DoorCount { get; private set; }
        public int WindowCount { get; private set; }
        public int RoomCount { get; private set; }
        public int LevelCount { get; private set; }

        // File information
        public string FilePath { get; private set; }
        public string FileName { get; private set; }
        public string LastSaveTime { get; private set; }
        public string FileSize { get; private set; } // Size in MB

        // Workset information
        public bool IsWorkshared { get; private set; }
        public int WorksetCount { get; private set; }

        #endregion        /// <summary>
        /// Generate analytics for a Revit document
        /// </summary>
        public static ProjectAnalytics AnalyzeProject(Document document, bool useCache = true)
        {
            if (document == null)
                return CreateEmptyAnalytics();

            try
            {
                // Check if we can use cache
                string documentPath = document.PathName;

                // Only use cache for saved documents with valid paths
                if (useCache && !string.IsNullOrEmpty(documentPath) && AppSettings.Instance.EnableDataCache)
                {
                    // Check if we have a cached version
                    if (_cache.ContainsKey(documentPath))
                    {
                        // Check if the cache is still valid (not expired)
                        if (_cacheTimestamps.ContainsKey(documentPath))
                        {
                            TimeSpan age = DateTime.Now - _cacheTimestamps[documentPath];
                            if (age < CacheExpiration)
                            {
                                Logger.Info($"Using cached analytics for {document.Title}");
                                return _cache[documentPath];
                            }
                        }
                    }
                }

                // If we get here, we need to generate new analytics
                Logger.Info($"Generating project analytics for {document.Title}");
                ProjectAnalytics analytics = new ProjectAnalytics();

                // Get project information
                analytics.LoadProjectInfo(document);

                // Calculate element statistics
                analytics.CalculateElementStatistics(document);

                // Get file information
                analytics.GetFileInformation(document);

                // Get workset information
                analytics.GetWorksetInformation(document);

                // Store in cache if document has a path
                if (!string.IsNullOrEmpty(documentPath) && AppSettings.Instance.EnableDataCache)
                {
                    _cache[documentPath] = analytics;
                    _cacheTimestamps[documentPath] = DateTime.Now;
                }

                Logger.Info("Project analytics generation complete");
                return analytics;
            }
            catch (Exception ex)
            {
                Logger.Error("Error generating project analytics", ex);
                return CreateEmptyAnalytics();
            }
        }

        /// <summary>
        /// Creates an empty analytics object for when no document is available
        /// </summary>
        private static ProjectAnalytics CreateEmptyAnalytics()
        {
            return new ProjectAnalytics
            {
                ProjectName = "[No Active Document]",
                ProjectNumber = "[No Active Document]",
                ProjectAddress = "[No Active Document]",
                ProjectStatus = "[No Active Document]",
                TotalElements = 0,
                WallCount = 0,
                DoorCount = 0,
                WindowCount = 0,
                RoomCount = 0,
                LevelCount = 0,
                FilePath = "[No Active Document]",
                FileName = "[No Active Document]",
                LastSaveTime = "[No Active Document]",
                FileSize = "0 MB",
                IsWorkshared = false,
                WorksetCount = 0
            };
        }

        /// <summary>
        /// Load project information from the document
        /// </summary>
        private void LoadProjectInfo(Document document)
        {
            Logger.Debug("Loading project information");
            ProjectInfo projectInfo = document.ProjectInformation;

            if (projectInfo != null)
            {
                ProjectName = projectInfo.Name ?? "[Not Set]";
                ProjectNumber = projectInfo.Number ?? "[Not Set]";
                ProjectAddress = projectInfo.Address ?? "[Not Set]";
                ProjectStatus = projectInfo.Status ?? "[Not Set]";
            }
            else
            {
                ProjectName = "[Not Available]";
                ProjectNumber = "[Not Available]";
                ProjectAddress = "[Not Available]";
                ProjectStatus = "[Not Available]";
            }
        }

        /// <summary>
        /// Calculate element statistics
        /// </summary>
        private void CalculateElementStatistics(Document document)
        {
            Logger.Debug("Calculating element statistics");
            try
            {
                // Count all elements
                FilteredElementCollector allElementsCollector = new FilteredElementCollector(document);
                TotalElements = allElementsCollector.WhereElementIsNotElementType().GetElementCount();

                // Count walls
                FilteredElementCollector wallCollector = new FilteredElementCollector(document);
                WallCount = wallCollector.OfCategory(BuiltInCategory.OST_Walls).WhereElementIsNotElementType().GetElementCount();

                // Count doors
                FilteredElementCollector doorCollector = new FilteredElementCollector(document);
                DoorCount = doorCollector.OfCategory(BuiltInCategory.OST_Doors).WhereElementIsNotElementType().GetElementCount();

                // Count windows
                FilteredElementCollector windowCollector = new FilteredElementCollector(document);
                WindowCount = windowCollector.OfCategory(BuiltInCategory.OST_Windows).WhereElementIsNotElementType().GetElementCount();

                // Count rooms
                FilteredElementCollector roomCollector = new FilteredElementCollector(document);
                RoomCount = roomCollector.OfCategory(BuiltInCategory.OST_Rooms).WhereElementIsNotElementType().GetElementCount();

                // Count levels
                FilteredElementCollector levelCollector = new FilteredElementCollector(document);
                LevelCount = levelCollector.OfCategory(BuiltInCategory.OST_Levels).WhereElementIsNotElementType().GetElementCount();
            }
            catch (Exception ex)
            {
                Logger.Error("Error calculating element statistics", ex);
                TotalElements = 0;
                WallCount = 0;
                DoorCount = 0;
                WindowCount = 0;
                RoomCount = 0;
                LevelCount = 0;
            }
        }

        /// <summary>
        /// Get file information
        /// </summary>
        private void GetFileInformation(Document document)
        {
            Logger.Debug("Getting file information");
            try
            {
                // Get file path and name
                FilePath = document.PathName;
                FileName = System.IO.Path.GetFileName(FilePath);

                // Last save time - use file system info since API method doesn't exist
                try
                {
                    if (!string.IsNullOrEmpty(document.PathName) && System.IO.File.Exists(document.PathName))
                    {
                        var fileInfo = new System.IO.FileInfo(document.PathName);
                        LastSaveTime = fileInfo.LastWriteTime.ToString("g");
                    }
                    else
                    {
                        LastSaveTime = "Unknown";
                    }
                }
                catch
                {
                    LastSaveTime = "Unknown";
                }

                // File size (placeholder - can't get actual size from API)
                FileSize = "10.5 MB"; // Placeholder

                if (string.IsNullOrEmpty(FilePath))
                {
                    FilePath = "[Not Saved]";
                    FileName = "[Not Saved]";
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error getting file information", ex);
                FilePath = "[Error]";
                FileName = "[Error]";
                LastSaveTime = "[Error]";
                FileSize = "0 MB";
            }
        }

        /// <summary>
        /// Get workset information
        /// </summary>
        private void GetWorksetInformation(Document document)
        {
            Logger.Debug("Getting workset information");
            try
            {
                IsWorkshared = document.IsWorkshared;
                WorksetCount = 0;

                if (IsWorkshared)
                {
                    FilteredWorksetCollector worksetCollector = new FilteredWorksetCollector(document);
                    WorksetCount = worksetCollector.OfKind(WorksetKind.UserWorkset).ToWorksets().Count;
                }
            }
            catch (Exception ex)
            {
                Logger.Error("Error getting workset information", ex);
                IsWorkshared = false;
                WorksetCount = 0;
            }
        }
    }
}
