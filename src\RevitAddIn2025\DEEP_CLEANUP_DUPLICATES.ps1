# DEEP CLEANUP DUPLICATES - RevitAddIn2025
# Comprehensive cleanup of all duplicate and unnecessary files now that Revit is closed

$ErrorActionPreference = "Stop"

Write-Host ""
Write-Host "DEEP CLEANUP DUPLICATES - RevitAddIn2025" -ForegroundColor Magenta
Write-Host "========================================" -ForegroundColor Magenta
Write-Host "Revit is closed - performing thorough cleanup" -ForegroundColor Yellow

# Configuration
$revitAddinsFolder = "$env:APPDATA\Autodesk\Revit\Addins\2025"
$revitProgramDataFolder = "$env:PROGRAMDATA\Autodesk\Revit\Addins\2025"
$allRevitVersions = @(
    "$env:APPDATA\Autodesk\Revit\Addins",
    "$env:PROGRAMDATA\Autodesk\Revit\Addins"
)

Write-Host ""
Write-Host "STEP 1: SCAN FOR ALL REVITADDIN2025 FILES" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan

$allFoundFiles = @()
$searchPatterns = @(
    "*RevitAddIn2025*",
    "*RevitAddIn*",
    "RevitAddIn2025*"
)

foreach ($baseLocation in $allRevitVersions) {
    if (Test-Path $baseLocation) {
        Write-Host "Scanning: $baseLocation" -ForegroundColor Gray
        
        foreach ($pattern in $searchPatterns) {
            $foundItems = Get-ChildItem -Path $baseLocation -Recurse -Filter $pattern -ErrorAction SilentlyContinue
            foreach ($item in $foundItems) {
                $allFoundFiles += [PSCustomObject]@{
                    Path = $item.FullName
                    Name = $item.Name
                    Type = if ($item.PSIsContainer) { "Folder" } else { "File" }
                    Size = if ($item.PSIsContainer) { 0 } else { $item.Length }
                    LastModified = $item.LastWriteTime
                    Location = $baseLocation
                }
            }
        }
    }
}

Write-Host ""
Write-Host "FOUND FILES AND FOLDERS:" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

if ($allFoundFiles.Count -eq 0) {
    Write-Host "No RevitAddIn2025 files found" -ForegroundColor Green
} else {
    $allFoundFiles | Sort-Object Location, Type, Name | ForEach-Object {
        $sizeText = if ($_.Type -eq "File") { " ($([math]::Round($_.Size / 1024, 2)) KB)" } else { "" }
        Write-Host "  [$($_.Type)] $($_.Name)$sizeText" -ForegroundColor White
        Write-Host "    Location: $($_.Path)" -ForegroundColor Gray
        Write-Host "    Modified: $($_.LastModified)" -ForegroundColor Gray
        Write-Host ""
    }
}

Write-Host ""
Write-Host "STEP 2: IDENTIFY CURRENT DEPLOYMENT" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

# The current correct deployment should be:
$correctDeploymentFolder = Join-Path $revitAddinsFolder "RevitAddIn2025"
$correctAddinFile = Join-Path $revitAddinsFolder "RevitAddIn2025.addin"
$correctDllFile = Join-Path $correctDeploymentFolder "RevitAddIn2025.dll"

Write-Host "Current correct deployment:" -ForegroundColor Green
Write-Host "  Folder: $correctDeploymentFolder" -ForegroundColor White
Write-Host "  .addin: $correctAddinFile" -ForegroundColor White
Write-Host "  DLL: $correctDllFile" -ForegroundColor White

$keepFiles = @($correctDeploymentFolder, $correctAddinFile, $correctDllFile)

Write-Host ""
Write-Host "STEP 3: REMOVE DUPLICATES AND OLD VERSIONS" -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan

$removedCount = 0
$keptCount = 0

foreach ($file in $allFoundFiles) {
    $shouldKeep = $false
    
    # Check if this file is part of the current correct deployment
    foreach ($keepPath in $keepFiles) {
        if ($file.Path -eq $keepPath -or $file.Path.StartsWith($correctDeploymentFolder)) {
            $shouldKeep = $true
            break
        }
    }
    
    if ($shouldKeep) {
        Write-Host "  KEEPING: $($file.Name)" -ForegroundColor Green
        $keptCount++
    } else {
        try {
            if ($file.Type -eq "Folder") {
                Remove-Item -Path $file.Path -Recurse -Force
                Write-Host "  REMOVED FOLDER: $($file.Name)" -ForegroundColor Red
            } else {
                Remove-Item -Path $file.Path -Force
                Write-Host "  REMOVED FILE: $($file.Name)" -ForegroundColor Red
            }
            $removedCount++
        } catch {
            Write-Host "  FAILED TO REMOVE: $($file.Name) - $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

Write-Host ""
Write-Host "CLEANUP SUMMARY:" -ForegroundColor Cyan
Write-Host "Files/Folders Removed: $removedCount" -ForegroundColor Red
Write-Host "Files/Folders Kept: $keptCount" -ForegroundColor Green

Write-Host ""
Write-Host "STEP 4: CLEAN BUILD OUTPUT DUPLICATES" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# Clean up build output folder duplicates
$scriptDir = $PSScriptRoot
$projectRoot = Split-Path -Parent $scriptDir
$buildFolders = @(
    (Join-Path $projectRoot "bin"),
    (Join-Path $projectRoot "obj"),
    (Join-Path $scriptDir "bin"),
    (Join-Path $scriptDir "obj")
)

foreach ($buildFolder in $buildFolders) {
    if (Test-Path $buildFolder) {
        Write-Host "Cleaning build folder: $buildFolder" -ForegroundColor Gray
        
        # Keep only the latest Release build, remove everything else
        $debugFolders = Get-ChildItem -Path $buildFolder -Recurse -Directory -Filter "Debug" -ErrorAction SilentlyContinue
        foreach ($debugFolder in $debugFolders) {
            try {
                Remove-Item -Path $debugFolder.FullName -Recurse -Force
                Write-Host "  REMOVED DEBUG: $($debugFolder.FullName)" -ForegroundColor Yellow
            } catch {
                Write-Host "  FAILED TO REMOVE DEBUG: $($debugFolder.FullName)" -ForegroundColor Red
            }
        }
        
        # Remove old obj files
        $objFiles = Get-ChildItem -Path $buildFolder -Recurse -File -Filter "*.obj" -ErrorAction SilentlyContinue
        foreach ($objFile in $objFiles) {
            try {
                Remove-Item -Path $objFile.FullName -Force
                Write-Host "  REMOVED OBJ: $($objFile.Name)" -ForegroundColor Yellow
            } catch {
                Write-Host "  FAILED TO REMOVE OBJ: $($objFile.Name)" -ForegroundColor Red
            }
        }
    }
}

Write-Host ""
Write-Host "STEP 5: VERIFY FINAL STATE" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan

# Verify the correct deployment is still intact
if (Test-Path $correctDllFile) {
    $dllInfo = Get-Item $correctDllFile
    $sizeKB = [math]::Round($dllInfo.Length / 1024, 2)
    Write-Host "SUCCESS: Correct DLL still exists ($sizeKB KB)" -ForegroundColor Green
} else {
    Write-Host "ERROR: Correct DLL was accidentally removed!" -ForegroundColor Red
}

if (Test-Path $correctAddinFile) {
    Write-Host "SUCCESS: Correct .addin file still exists" -ForegroundColor Green
} else {
    Write-Host "ERROR: Correct .addin file was accidentally removed!" -ForegroundColor Red
}

# Show final deployment contents
if (Test-Path $correctDeploymentFolder) {
    Write-Host ""
    Write-Host "FINAL DEPLOYMENT CONTENTS:" -ForegroundColor Green
    $finalFiles = Get-ChildItem -Path $correctDeploymentFolder
    foreach ($file in $finalFiles) {
        $sizeKB = [math]::Round($file.Length / 1024, 2)
        Write-Host "  $($file.Name) ($sizeKB KB)" -ForegroundColor White
    }
}

Write-Host ""
Write-Host "STEP 6: SCAN FOR REMAINING DUPLICATES" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# Final scan to ensure no duplicates remain
$remainingFiles = @()
foreach ($baseLocation in $allRevitVersions) {
    if (Test-Path $baseLocation) {
        foreach ($pattern in $searchPatterns) {
            $foundItems = Get-ChildItem -Path $baseLocation -Recurse -Filter $pattern -ErrorAction SilentlyContinue
            $remainingFiles += $foundItems
        }
    }
}

if ($remainingFiles.Count -eq 0) {
    Write-Host "SUCCESS: No RevitAddIn2025 files found outside correct deployment" -ForegroundColor Green
} else {
    Write-Host "REMAINING FILES:" -ForegroundColor Yellow
    foreach ($file in $remainingFiles) {
        if (-not $file.FullName.StartsWith($correctDeploymentFolder) -and $file.FullName -ne $correctAddinFile) {
            Write-Host "  UNEXPECTED: $($file.FullName)" -ForegroundColor Red
        } else {
            Write-Host "  EXPECTED: $($file.Name)" -ForegroundColor Green
        }
    }
}

Write-Host ""
Write-Host "DEEP CLEANUP COMPLETED!" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green
Write-Host ""
Write-Host "SUMMARY:" -ForegroundColor Cyan
Write-Host "- Removed $removedCount duplicate/old files" -ForegroundColor White
Write-Host "- Kept $keptCount necessary files" -ForegroundColor White
Write-Host "- Cleaned build output folders" -ForegroundColor White
Write-Host "- Verified correct deployment integrity" -ForegroundColor White
Write-Host ""
Write-Host "The plugin is now ready for testing with a clean deployment!" -ForegroundColor Green
