# RevitAddIn2025 Quick Start Guide

This guide will help you get started with the RevitAddIn2025 add-in quickly.

## Installation

1. Close all running instances of Revit 2025
2. Run the RevitAddIn2025 installer
3. Follow the installation wizard prompts
4. Start Revit 2025

## Accessing the Add-in

The RevitAddIn2025 can be accessed from the Add-ins tab in the Revit ribbon.

1. Open Revit 2025
2. Open any project
3. Navigate to the Add-ins tab
4. Find the "RevitAddIn2025" panel
5. Click on the "Dashboard" button

## First Look at the Dashboard

The dashboard provides a comprehensive overview of your project:

1. **Project Information**: See key project details
2. **Element Summary**: View counts of walls, doors, windows, and rooms
3. **Recent Activity**: Track your actions within the add-in
4. **Tools**: Access settings, help, and reporting features
5. **Project Statistics**: See detailed project metrics

## Quick Actions

### Switching Themes

* **Method 1**: Click the small circle indicator in the top-left corner of any window
* **Method 2**: Press `Ctrl+Shift+D` on your keyboard
* **Method 3**: Go to Settings and toggle the "Dark Theme" switch

### Generating Reports

1. Click the "Generate Report" button at the bottom of the Dashboard
2. Choose either "Project Summary Report" or "Element Details Report"
3. For element reports, select a specific category or "All Categories"
4. Click "Generate"
5. The report will be saved to your desktop

### Checking Data Freshness

* Look at the Cache Status indicator in the bottom-right corner
* "Fresh Data" means data was just analyzed from your project
* "Cached Data" means data is being loaded from cache for better performance
* The indicator also shows how many milliseconds the data load took

## Keyboard Shortcuts

Learn these key shortcuts to work more efficiently:

* `Ctrl+Shift+D`: Toggle between light and dark themes
* `Ctrl+R`: Refresh dashboard data
* `Esc`: Close the current window

## Next Steps

* Review the [User Guide](user-guide.md) for detailed information
* Check the [Keyboard Shortcuts Guide](keyboard_shortcuts.md) for all shortcuts
* Explore the [New Features Guide](new_features_guide.md) for the latest enhancements

## Getting Help

If you need assistance:
* Click the "Help" button on the Dashboard
* Visit our support website
* Contact <NAME_EMAIL>
