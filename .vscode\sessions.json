{"$schema": "https://cdn.statically.io/gh/nguyenngoclongdev/cdn/main/schema/v10/terminal-keeper.json", "theme": "tribe", "active": "autonomous-revit", "activateOnStartup": true, "keepExistingTerminals": false, "sessions": {"autonomous-revit": [{"name": "🚀 Autonomous Pipeline", "autoExecuteCommands": true, "icon": "rocket", "color": "terminal.ansiBlue", "commands": ["cd c:\\GITHUB", "Write-Host '🤖 AUTONOMOUS REVIT SYSTEM ONLINE' -ForegroundColor Cyan", "Write-Host '⚡ Zero-Manual Operation Mode Activated' -ForegroundColor Green"]}, [{"name": "🔨 Build Monitor", "autoExecuteCommands": true, "icon": "tools", "color": "terminal.ansi<PERSON><PERSON>w", "commands": ["cd c:\\GITHUB\\src\\RevitAddIn2025", "Write-Host '🔨 BUILD MONITOR ACTIVE' -ForegroundColor Yellow", "dotnet watch build --verbosity minimal"]}, {"name": "🎯 Deploy Monitor", "autoExecuteCommands": true, "icon": "target", "color": "terminal.ansiMagenta", "commands": ["cd c:\\GITHUB", "Write-Host '🎯 DEPLOYMENT MONITOR READY' -ForegroundColor Magenta", "Write-Host '📦 Watching for successful builds...' -ForegroundColor Gray"]}], [{"name": "🛡️ Error Monitor", "autoExecuteCommands": true, "icon": "shield", "color": "terminal.ansiRed", "commands": ["cd c:\\GITHUB", "Write-Host '🛡️ ERROR MONITORING SYSTEM ONLINE' -ForegroundColor Red", "Write-Host '🔍 Self-healing protocols activated' -ForegroundColor Green"]}, {"name": "📊 Dashboard", "autoExecuteCommands": true, "icon": "graph", "color": "terminal.ansiCyan", "commands": ["cd c:\\GITHUB", "Write-Host '📊 AUTONOMOUS DASHBOARD LAUNCHING' -ForegroundColor Cyan", "Write-Host '🎨 Visual monitoring interface ready' -ForegroundColor Green"]}]], "ultimate-autonomous": [{"name": "🎯 ZERO-CLICK SYSTEM", "autoExecuteCommands": true, "icon": "zap", "color": "terminal.ansiBrightGreen", "commands": ["cd c:\\GITHUB", "Write-Host '🎯 ULTIMATE ZERO-CLICK SYSTEM ACTIVATED' -ForegroundColor Green", "Write-Host '🤖 AI-Powered Autonomous Operation Mode' -ForegroundColor Cyan", "powershell -ExecutionPolicy Bypass -File .\\ULTIMATE_ZERO_CLICK_SYSTEM.ps1"]}], "default": [{"name": "🚀 Quick Start", "autoExecuteCommands": false, "icon": "rocket", "color": "terminal.ansiGreen", "commands": ["cd c:\\GITHUB", "Write-Host '🚀 REVIT AUTONOMOUS DEVELOPMENT ENVIRONMENT' -ForegroundColor Green"]}]}}