# INSPECT ASSEMBLY - RevitAddIn2025
# Check what classes are actually in the DLL

$dllPath = "C:\Users\<USER>\AppData\Roaming\Autodesk\Revit\Addins\2025\RevitAddIn2025_20250527_184504\RevitAddIn2025.dll"

Write-Host "ASSEMBLY INSPECTION" -ForegroundColor Magenta
Write-Host "===================" -ForegroundColor Magenta
Write-Host "DLL: $dllPath" -ForegroundColor Gray

try {
    $assembly = [System.Reflection.Assembly]::LoadFrom($dllPath)
    Write-Host ""
    Write-Host "Assembly Full Name: $($assembly.FullName)" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "ALL TYPES IN ASSEMBLY:" -ForegroundColor Cyan
    Write-Host "======================" -ForegroundColor Cyan
    
    $types = $assembly.GetTypes()
    foreach ($type in $types) {
        Write-Host "  $($type.FullName)" -ForegroundColor White
        
        # Check if this implements IExternalApplication
        $interfaces = $type.GetInterfaces()
        foreach ($interface in $interfaces) {
            if ($interface.Name -eq "IExternalApplication") {
                Write-Host "    -> IMPLEMENTS IExternalApplication" -ForegroundColor Green
            }
        }
    }
    
    Write-Host ""
    Write-Host "LOOKING FOR SPECIFIC CLASS:" -ForegroundColor Cyan
    Write-Host "===========================" -ForegroundColor Cyan
    
    $targetClass = "RevitAddIn2025.App.RevitApplication"
    $foundClass = $assembly.GetType($targetClass)
    
    if ($foundClass) {
        Write-Host "SUCCESS: Found $targetClass" -ForegroundColor Green
        Write-Host "  Full Name: $($foundClass.FullName)" -ForegroundColor White
        Write-Host "  Namespace: $($foundClass.Namespace)" -ForegroundColor White
        Write-Host "  Assembly: $($foundClass.Assembly.FullName)" -ForegroundColor White
        
        # Check interfaces
        $interfaces = $foundClass.GetInterfaces()
        Write-Host "  Interfaces:" -ForegroundColor White
        foreach ($interface in $interfaces) {
            Write-Host "    - $($interface.FullName)" -ForegroundColor Gray
        }
    } else {
        Write-Host "ERROR: Class $targetClass not found" -ForegroundColor Red
        
        Write-Host ""
        Write-Host "SEARCHING FOR SIMILAR CLASSES:" -ForegroundColor Yellow
        $similarClasses = $types | Where-Object { $_.Name -like "*Application*" -or $_.Name -like "*Revit*" }
        foreach ($class in $similarClasses) {
            Write-Host "  Found similar: $($class.FullName)" -ForegroundColor Yellow
        }
    }
    
} catch {
    Write-Host "ERROR: Failed to load assembly: $($_.Exception.Message)" -ForegroundColor Red
}
