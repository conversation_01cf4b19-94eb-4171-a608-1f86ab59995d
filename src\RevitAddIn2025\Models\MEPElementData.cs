using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Plumbing;

namespace RevitAddIn2025.Models
{
    /// <summary>
    /// Contains data about an MEP element for use in transformer AI analysis
    /// </summary>
    public class MEPElementData
    {
        // Element identification
        public ElementId Id { get; }
        public int ElementId { get; set; } // Additional numeric ID for transformer compatibility
        public string ElementName { get; }
        public string ElementType { get; }
        public string SystemType { get; set; } // Made settable for configuration
        public MEPDiscipline Discipline { get; }
        public MEPSystemType SystemTypeEnum { get; set; }

        // Enhanced system classification
        public MEPSystemClassifier.DetailedMEPSystemType DetailedSystemType { get; }
        public int SystemPriority { get; set; }
        public int Priority { get; set; } // Additional priority field used by transformers
        public double RecommendedClearance { get; }

        // Spatial information
        public BoundingBoxXYZ BoundingBox { get; }
        public XYZ Centroid { get; }
        public MEPPosition3D Position { get; set; } // Normalized position for transformers
        public MEPPosition3D Location { get; set; } // Alias for Position for transformer compatibility
        public MEPDimensions Dimensions { get; set; } // Normalized dimensions for transformers
        public double Length { get; set; }
        public double Diameter { get; set; }
        public double Area { get; set; }
        public double Volume { get; set; }

        // MEP-specific properties for transformer analysis
        public double FlowRate { get; set; }
        public double Pressure { get; set; }
        public double Temperature { get; set; }
        public double Velocity { get; set; }
        public double Efficiency { get; set; }
        public double SystemClearance { get; set; }
        public double SpacingInterval { get; set; }

        // Properties for transformer processing
        public double[] EmbeddingVector { get; set; }
        public double ClashProbability { get; set; }
        public double EnergyEfficiencyScore { get; set; }
        public bool IsCodeCompliant { get; set; }

        // Additional properties for energy analysis
        public string Level { get; set; }
        public Dictionary<string, object> Properties { get; set; }
        /// <summary>
        /// Creates a new MEP element data object from a Revit element
        /// </summary>
        public MEPElementData(Element element)
        {
            if (element == null) throw new ArgumentNullException(nameof(element));
            Id = element.Id;
            ElementId = (int)element.Id.Value; // Use Value instead of deprecated IntegerValue with cast
            ElementName = element.Name;
            ElementType = element.GetType().Name;

            // Extract bounding box
            BoundingBox = element.get_BoundingBox(null);
            if (BoundingBox != null)
            {
                Centroid = (BoundingBox.Max + BoundingBox.Min) / 2.0;
                // Create normalized position
                Position = MEPPosition3D.FromXYZ(Centroid);
                Location = Position; // Set alias for transformer compatibility
                // Create normalized dimensions
                var size = BoundingBox.Max - BoundingBox.Min;
                Dimensions = MEPDimensions.FromRevitUnits(size.X, size.Y, size.Z);
            }
            else
            {
                // Fallback for elements without bounding box
                Centroid = XYZ.Zero;
                Position = new MEPPosition3D();
                Location = Position; // Set alias for transformer compatibility
                Dimensions = new MEPDimensions();
            }

            // Set discipline based on element type
            Discipline = DetermineDiscipline(element);

            // Extract system type
            SystemType = GetSystemTypeName(element);

            // Determine system type enum
            SystemTypeEnum = DetermineSystemTypeEnum(element);
            // Set priority based on system type
            Priority = GetSystemPriority(SystemTypeEnum);
            SystemPriority = Priority;

            // Use new advanced system classifier
            DetailedSystemType = MEPSystemClassifier.ClassifyMEPElement(element);
            RecommendedClearance = MEPSystemClassifier.GetRecommendedClearance(DetailedSystemType);

            // Extract geometric properties
            ExtractGeometricProperties(element);

            // Initialize MEP-specific properties with defaults
            FlowRate = 0.0;
            Pressure = 0.0;
            Temperature = 70.0; // Room temperature default
            Velocity = 0.0; // Default velocity
            Efficiency = 0.8; // Default efficiency (80%)
            SystemClearance = 24.0; // Default clearance in inches
            SpacingInterval = 10.0; // Default spacing in feet

            // Initialize transformer properties
            EmbeddingVector = new double[256]; // Default size for embedding vector
            ClashProbability = 0.0;
            EnergyEfficiencyScore = 0.5; // Neutral starting point
            IsCodeCompliant = true; // Assume compliant by default

            // Initialize additional properties
            Level = GetElementLevel(element);
            Properties = ExtractElementProperties(element);
        }

        /// <summary>
        /// Determines which MEP discipline the element belongs to
        /// </summary>
        private MEPDiscipline DetermineDiscipline(Element element)
        {
            if (element is Duct || element is DuctType || element is DuctInsulation || element is MechanicalSystem)
            {
                return MEPDiscipline.Mechanical;
            }
            else if (element is CableTray || element is Conduit || element is Wire || element is ElectricalSystem)
            {
                return MEPDiscipline.Electrical;
            }
            else if (element is Pipe || element is PipeType || element is PipeInsulation || element is PipingSystem)
            {
                return MEPDiscipline.Plumbing;
            }
            else
            {                // Try to determine by category
                switch (element.Category?.Id.Value)
                {
                    case (int)BuiltInCategory.OST_DuctCurves:
                    case (int)BuiltInCategory.OST_DuctFitting:
                    case (int)BuiltInCategory.OST_DuctTerminal:
                    case (int)BuiltInCategory.OST_MechanicalEquipment:
                        return MEPDiscipline.Mechanical;

                    case (int)BuiltInCategory.OST_CableTray:
                    case (int)BuiltInCategory.OST_Conduit:
                    case (int)BuiltInCategory.OST_ElectricalEquipment:
                    case (int)BuiltInCategory.OST_ElectricalFixtures:
                        return MEPDiscipline.Electrical;

                    case (int)BuiltInCategory.OST_PipeCurves:
                    case (int)BuiltInCategory.OST_PipeFitting:
                    case (int)BuiltInCategory.OST_PlumbingFixtures:
                    case (int)BuiltInCategory.OST_Sprinklers:
                        return MEPDiscipline.Plumbing;

                    default:
                        return MEPDiscipline.Other;
                }
            }
        }

        /// <summary>
        /// Gets the system type name for the element
        /// </summary>
        private string GetSystemTypeName(Element element)
        {
            // Try to get system information
            if (element.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM) is Parameter systemParam)
            {
                string systemName = systemParam.AsString();
                if (!string.IsNullOrEmpty(systemName))
                {
                    return systemName;
                }
            }
            // Fallbacks based on element type
            if (element is MEPCurve curve)
            {
                ElementId systemTypeId = curve.MEPSystem?.GetTypeId();
                if (systemTypeId != null && systemTypeId.Value != -1)
                {
                    Element systemType = element.Document.GetElement(systemTypeId);
                    return systemType?.Name ?? "Unknown System";
                }
            }

            return "Unknown System";
        }

        /// <summary>
        /// Extract geometric properties from the element
        /// </summary>
        private void ExtractGeometricProperties(Element element)
        {
            // Initialize default values
            Length = 0;
            Diameter = 0;
            Area = 0;
            Volume = 0;

            try
            {
                // Get length for curve elements
                if (element is MEPCurve mepCurve)
                {
                    if (mepCurve.Location is LocationCurve locationCurve)
                    {
                        Length = locationCurve.Curve.Length;
                    }

                    // Get diameter/size
                    if (element is Duct duct)
                    {
                        Diameter = duct.Diameter;
                    }
                    else if (element is Pipe pipe)
                    {
                        Diameter = pipe.Diameter;
                    }
                    else if (element is Conduit conduit)
                    {
                        Diameter = conduit.Diameter;
                    }
                }

                // Try to get area and volume through parameters
                Parameter areaParam = element.get_Parameter(BuiltInParameter.HOST_AREA_COMPUTED);
                if (areaParam != null)
                {
                    Area = areaParam.AsDouble();
                }

                Parameter volumeParam = element.get_Parameter(BuiltInParameter.HOST_VOLUME_COMPUTED);
                if (volumeParam != null)
                {
                    Volume = volumeParam.AsDouble();
                }
            }
            catch (Exception)
            {
                // Silently handle exceptions - use default values
            }
        }

        /// <summary>
        /// Determines the MEP system type enum for the element
        /// </summary>
        private MEPSystemType DetermineSystemTypeEnum(Element element)
        {
            switch (Discipline)
            {
                case MEPDiscipline.Electrical:
                    return MEPSystemType.Electrical;
                case MEPDiscipline.Mechanical:
                    return MEPSystemType.Mechanical;
                case MEPDiscipline.Plumbing:                    // Check if it's fire protection
                    if (element.Category?.Id.Value == (int)BuiltInCategory.OST_Sprinklers ||
                        SystemType.ToLower().Contains("fire") ||
                        SystemType.ToLower().Contains("sprinkler"))
                    {
                        return MEPSystemType.FireProtection;
                    }
                    return MEPSystemType.Plumbing;
                default:
                    return MEPSystemType.Other;
            }
        }

        /// <summary>
        /// Gets the system priority based on MEP system type
        /// </summary>
        private int GetSystemPriority(MEPSystemType systemType)
        {
            return systemType switch
            {
                MEPSystemType.FireProtection => 1, // Highest priority
                MEPSystemType.Electrical => 2,
                MEPSystemType.Plumbing => 3,
                MEPSystemType.Mechanical => 4,
                MEPSystemType.Other => 5, // Lowest priority
                _ => 5
            };
        }

        /// <summary>
        /// Gets the level name for the element
        /// </summary>
        private string GetElementLevel(Element element)
        {
            try
            {
                // Try to get level from parameter
                Parameter levelParam = element.get_Parameter(BuiltInParameter.SCHEDULE_LEVEL_PARAM);
                if (levelParam != null && !string.IsNullOrEmpty(levelParam.AsString()))
                {
                    return levelParam.AsString();
                }

                // Try to get level from element's level ID
                if (element.LevelId != null && element.LevelId.Value != -1)
                {
                    Element level = element.Document.GetElement(element.LevelId);
                    return level?.Name ?? "Unknown Level";
                }

                // Fallback based on Z coordinate
                if (Centroid != null)
                {
                    double elevation = Centroid.Z;
                    if (elevation < 10) return "Ground Floor";
                    else if (elevation < 20) return "Second Floor";
                    else if (elevation < 30) return "Third Floor";
                    else return $"Level {Math.Ceiling(elevation / 10)}";
                }

                return "Unknown Level";
            }
            catch (Exception)
            {
                return "Unknown Level";
            }
        }

        /// <summary>
        /// Extracts element properties for energy analysis
        /// </summary>
        private Dictionary<string, object> ExtractElementProperties(Element element)
        {
            var properties = new Dictionary<string, object>();

            try
            {
                // Extract common MEP properties
                ExtractProperty(element, BuiltInParameter.RBS_PIPE_DIAMETER_PARAM, "Diameter", properties);
                ExtractProperty(element, BuiltInParameter.RBS_CURVE_DIAMETER_PARAM, "Diameter", properties);
                ExtractProperty(element, BuiltInParameter.RBS_DUCT_STATIC_PRESSURE, "Pressure", properties);
                ExtractProperty(element, BuiltInParameter.RBS_PIPE_STATIC_PRESSURE, "Pressure", properties);
                ExtractProperty(element, BuiltInParameter.RBS_VELOCITY, "Velocity", properties);
                ExtractProperty(element, BuiltInParameter.RBS_PIPE_FLOW_PARAM, "FlowRate", properties);
                ExtractProperty(element, BuiltInParameter.RBS_DUCT_FLOW_PARAM, "FlowRate", properties);

                // Extract electrical properties
                ExtractProperty(element, BuiltInParameter.RBS_ELEC_APPARENT_LOAD, "ApparentLoad", properties);
                ExtractProperty(element, BuiltInParameter.RBS_ELEC_VOLTAGE, "Voltage", properties);

                // Extract lighting properties
                ExtractProperty(element, BuiltInParameter.FBX_LIGHT_LIMUNOUS_FLUX, "LuminousFlux", properties);
                ExtractProperty(element, BuiltInParameter.LIGHTING_FIXTURE_WATTAGE, "Wattage", properties);

                // Extract equipment properties
                ExtractProperty(element, BuiltInParameter.RBS_ELEC_POWER_FACTOR, "PowerFactor", properties);
                ExtractProperty(element, BuiltInParameter.ALL_MODEL_INSTANCE_COMMENTS, "Comments", properties);

                // Add calculated properties
                properties["ElementType"] = ElementType;
                properties["SystemType"] = SystemType;
                properties["Discipline"] = Discipline.ToString();
                properties["Length"] = Length;
                properties["Area"] = Area;
                properties["Volume"] = Volume;

                // Add default values for missing properties
                if (!properties.ContainsKey("Wattage"))
                {
                    // Default wattage based on element type
                    if (ElementType.Contains("Light") || ElementType.Contains("Fixture"))
                    {
                        properties["Wattage"] = 40.0; // Default fixture wattage
                    }
                }

                if (!properties.ContainsKey("PowerRating"))
                {
                    // Default power rating for equipment
                    if (ElementType.Contains("Equipment") || ElementType.Contains("Motor"))
                    {
                        properties["PowerRating"] = 1000.0; // Default 1kW
                    }
                }

                if (!properties.ContainsKey("FillRatio"))
                {
                    // Default conduit fill ratio
                    if (ElementType.Contains("Conduit"))
                    {
                        properties["FillRatio"] = 0.3; // 30% fill
                    }
                }

                if (!properties.ContainsKey("Slope"))
                {
                    // Default pipe slope for drainage
                    if (SystemType.Contains("Drain") || SystemType.Contains("Waste"))
                    {
                        properties["Slope"] = 0.02; // 2% slope
                    }
                }
            }
            catch (Exception)
            {
                // Silently handle exceptions and return what we have
            }

            return properties;
        }

        /// <summary>
        /// Helper method to extract a parameter value
        /// </summary>
        private void ExtractProperty(Element element, BuiltInParameter paramId, string propertyName, Dictionary<string, object> properties)
        {
            try
            {
                Parameter param = element.get_Parameter(paramId);
                if (param != null && param.HasValue)
                {
                    switch (param.StorageType)
                    {
                        case StorageType.Double:
                            properties[propertyName] = param.AsDouble();
                            break;
                        case StorageType.Integer:
                            properties[propertyName] = param.AsInteger();
                            break;
                        case StorageType.String:
                            string stringValue = param.AsString();
                            if (!string.IsNullOrEmpty(stringValue))
                                properties[propertyName] = stringValue;
                            break;
                        case StorageType.ElementId:
                            ElementId elementId = param.AsElementId();
                            if (elementId != null && elementId.Value != -1)
                                properties[propertyName] = elementId.Value;
                            break;
                    }
                }
            }
            catch (Exception)
            {
                // Silently ignore parameter extraction errors
            }
        }
    }

    /// <summary>
    /// Batch of MEP elements for transformer processing
    /// </summary>
    public class MEPElementBatch
    {
        public List<MEPElementData> Elements { get; } = new List<MEPElementData>();

        public void AddElement(MEPElementData element)
        {
            Elements.Add(element);
        }
    }

    /// <summary>
    /// Result of clash detection between MEP elements
    /// </summary>
    public class MEPClashResult
    {
        public ElementId Element1Id { get; }
        public ElementId Element2Id { get; }
        public XYZ ClashPoint { get; }
        public double ClashSeverity { get; } // 0.0 to 1.0
        public string ClashDescription { get; }

        public MEPClashResult(ElementId element1Id, ElementId element2Id, XYZ clashPoint, double clashSeverity)
        {
            Element1Id = element1Id;
            Element2Id = element2Id;
            ClashPoint = clashPoint;
            ClashSeverity = clashSeverity;

            ClashDescription = $"Clash between elements {element1Id.Value} and {element2Id.Value} with severity {clashSeverity:F2}";
        }
    }

    /// <summary>
    /// Energy efficiency report for MEP systems
    /// </summary>
    public class MEPEnergyReport
    {
        public double OverallEfficiencyScore { get; set; }
        public List<MEPSystemEfficiency> SystemEfficiencies { get; } = new List<MEPSystemEfficiency>();
        public List<MEPEnergyRecommendation> Recommendations { get; } = new List<MEPEnergyRecommendation>();
    }

    /// <summary>
    /// Efficiency data for a specific MEP system
    /// </summary>
    public class MEPSystemEfficiency
    {
        public string SystemName { get; set; }
        public MEPDiscipline Discipline { get; set; }
        public double EfficiencyScore { get; set; }
        public List<ElementId> CriticalElements { get; } = new List<ElementId>();
    }

    /// <summary>
    /// Energy efficiency recommendation
    /// </summary>
    public class MEPEnergyRecommendation
    {
        public string Description { get; set; }
        public double PotentialSavings { get; set; }
        public List<ElementId> AffectedElements { get; } = new List<ElementId>();
    }

    /// <summary>
    /// Code violation in MEP systems
    /// </summary>
    public class MEPCodeViolation
    {
        public string ViolationCode { get; set; }
        public string Description { get; set; }
        public ElementId ElementId { get; set; }
        public string RecommendedFix { get; set; }
    }

    /// <summary>
    /// MEP discipline types
    /// </summary>
    public enum MEPDiscipline
    {
        Mechanical,
        Electrical,
        Plumbing,
        FireProtection,
        Other
    }
}
