# MEP Code Compliance Setup Guide

This guide provides step-by-step instructions for setting up and configuring the MEP Code Compliance verification feature in the Revit 2025 add-in.

## Installation

The MEP Code Compliance verification feature is included in the main RevitAddIn2025 package. Follow these steps to ensure it's properly installed:

1. Ensure you have Revit 2025 installed on your system
2. Install the RevitAddIn2025 package following the instructions in the main deployment guide
3. Verify that the MEP Transformer ribbon panel appears in Revit's interface
4. Check that the "Code Compliance" button appears in the MEP Analysis dropdown

## Initial Configuration

Before using the code compliance verification feature, you may want to configure it for your specific needs:

1. Open Revit 2025 with a project containing MEP elements
2. Navigate to the RevitAddIn2025 tab
3. Click on Settings
4. In the Settings dialog, navigate to the "MEP Compliance" tab
5. Configure the following settings:
   - Default building codes to check against
   - Minimum clearance requirements (if different from code defaults)
   - Support spacing requirements (if different from code defaults)
   - Compliance reporting preferences

## Using Code Compliance Verification

### Basic Verification

To perform a basic code compliance verification:

1. Open your Revit project containing MEP elements
2. Navigate to the RevitAddIn2025 tab
3. In the "MEP AI" panel, click on the "MEP Analysis" dropdown
4. Select "Code Compliance"
5. Wait for the analysis to complete
6. Review the results in the Compliance Dashboard

### Advanced Options

The Code Compliance Verification tool offers several advanced options:

#### System Filtering

You can filter which MEP systems to verify:

1. In the Code Compliance dialog, click on "Filter Systems"
2. Select the system types you wish to verify
3. Click "Apply Filter" to update the verification scope

#### Saving Compliance Reports

To save a compliance report:

1. After running a verification, click "Export Report"
2. Choose your preferred format (PDF, HTML, or Excel)
3. Select a destination location
4. Click "Save"

#### Highlighting Issues in the Model

To highlight non-compliant elements directly in the Revit model:

1. In the compliance dashboard, locate the "Critical Issues" or "Major Issues" section
2. Click the "Highlight in Model" button
3. The view will zoom to and highlight the affected elements

## Customizing Code Standards

You can customize which code standards are used for verification:

1. Navigate to Settings > MEP Compliance > Standards
2. Select from available standards:
   - IBC 2021 (International Building Code)
   - IMC 2021 (International Mechanical Code)
   - IPC 2021 (International Plumbing Code)
   - NEC 2023 (National Electrical Code)
   - NFPA 13 2022 (Fire Sprinkler Systems)
3. Click "Apply" to save your preferences

## Troubleshooting

### No Issues Detected When Expected

If the system isn't detecting expected compliance issues:

1. Verify that the correct code standards are enabled in Settings
2. Check that system filtering isn't excluding the elements you're concerned about
3. Ensure the elements in question have the correct system type assigned

### Analysis Takes Too Long

If the compliance verification process is taking too long:

1. Try filtering to specific system types rather than analyzing all systems
2. Close other memory-intensive applications
3. Consider breaking up very large models into smaller analysis sections

### Element Highlighting Doesn't Work

If element highlighting isn't working:

1. Verify you have the correct view active
2. Check that the elements haven't been hidden in the current view
3. Try selecting a different issue to highlight

## Getting Help

For additional assistance with the MEP Code Compliance verification feature:

1. Check the MEP_Code_Compliance_Guide.md documentation file
2. Visit the RevitAddIn2025 support portal
3. Contact technical <NAME_EMAIL>

## Best Practices

For best results with the MEP Code Compliance verification feature:

1. Run verifications early in the design process to identify issues before they become costly
2. Verify compliance after making significant changes to MEP systems
3. Save regular compliance reports to track progress
4. Address critical issues first, followed by major, minor, and advisory issues
5. Use the system as a guide, but always apply professional judgment
6. Keep local code amendments in mind when reviewing results
