using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Plumbing;
using RevitAddIn2025.AI.Transformer;
using RevitAddIn2025.AI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RevitAddIn2025.Models
{
    /// <summary>
    /// Provides advanced MEP code compliance verification using AI-driven analysis
    /// </summary>
    public class MEPCodeComplianceVerifier
    {
        private readonly MEPSystemClassifier _systemClassifier;
        private readonly MEPTransformerModel _transformerModel;
        private readonly Dictionary<string, BuildingCodeStandard> _buildingCodeStandards;

        /// <summary>
        /// Initializes a new instance of the MEP Code Compliance Verifier
        /// </summary>
        /// <param name="systemClassifier">MEP system classifier to identify system types</param>
        /// <param name="transformerModel">AI transformer model for complex compliance analysis</param>
        public MEPCodeComplianceVerifier(MEPSystemClassifier systemClassifier, MEPTransformerModel transformerModel)
        {
            _systemClassifier = systemClassifier ?? throw new ArgumentNullException(nameof(systemClassifier));
            _transformerModel = transformerModel ?? throw new ArgumentNullException(nameof(transformerModel));
            _buildingCodeStandards = InitializeBuildingCodes();
        }

        /// <summary>
        /// Verifies code compliance for a collection of MEP elements
        /// </summary>
        /// <param name="elements">The MEP element data to verify</param>
        /// <returns>A collection of compliance results with issues and recommendations</returns>
        public async Task<ComplianceVerificationResult> VerifyComplianceAsync(IEnumerable<MEPElementData> elements)
        {
            if (elements == null || !elements.Any())
            {
                throw new ArgumentException("No elements provided for compliance verification");
            }

            var result = new ComplianceVerificationResult
            {
                ComplianceIssues = new List<ComplianceIssue>(),
                OverallComplianceScore = 100.0, // Start with perfect score and deduct based on issues
                CodeReferences = new List<CodeReference>(),
                Timestamp = DateTime.Now
            };

            foreach (var element in elements)
            {
                // Get applicable code standards based on element system type
                var applicableCodes = GetApplicableCodeStandards(element);

                // Check element against each applicable code
                foreach (var code in applicableCodes)
                {
                    var issues = await VerifyElementAgainstCodeAsync(element, code);

                    if (issues.Any())
                    {
                        result.ComplianceIssues.AddRange(issues);

                        // Reduce overall score based on issue severity
                        foreach (var issue in issues)
                        {
                            switch (issue.Severity)
                            {
                                case ComplianceIssueSeverity.Critical:
                                    result.OverallComplianceScore -= 10.0;
                                    break;
                                case ComplianceIssueSeverity.Major:
                                    result.OverallComplianceScore -= 5.0;
                                    break;
                                case ComplianceIssueSeverity.Minor:
                                    result.OverallComplianceScore -= 2.0;
                                    break;
                                case ComplianceIssueSeverity.Advisory:
                                    result.OverallComplianceScore -= 1.0;
                                    break;
                            }
                        }
                    }

                    // Add code reference if not already included
                    if (!result.CodeReferences.Any(cr => cr.Code == code.Code && cr.Section == code.Sections.First()))
                    {
                        result.CodeReferences.Add(new CodeReference
                        {
                            Code = code.Code,
                            Section = code.Sections.First(),
                            Title = code.Title,
                            Url = code.ReferenceUrl
                        });
                    }
                }
            }

            // Cap minimum score at 0
            result.OverallComplianceScore = Math.Max(0, result.OverallComplianceScore);

            // Calculate compliance categories
            result.ComplianceCategories = CalculateComplianceCategories(result.ComplianceIssues);

            return result;
        }

        /// <summary>
        /// Verifies a single MEP element against a specific building code
        /// </summary>
        private async Task<List<ComplianceIssue>> VerifyElementAgainstCodeAsync(MEPElementData element, BuildingCodeStandard code)
        {
            var issues = new List<ComplianceIssue>();            // Check clearance requirements
            if (element.SystemClearance < code.MinimumClearance)
            {
                issues.Add(new ComplianceIssue
                {
                    ElementId = element.ElementId.ToString(),
                    SystemType = element.SystemType,
                    IssueType = ComplianceIssueType.ClearanceViolation,
                    Description = $"Insufficient clearance: {element.SystemClearance}\" provided, {code.MinimumClearance}\" required by {code.Code}",
                    Severity = ComplianceIssueSeverity.Major,
                    CodeReference = new CodeReference
                    {
                        Code = code.Code,
                        Section = code.Sections.FirstOrDefault(s => s.Contains("clearance")) ?? code.Sections.First(),
                        Title = "Minimum Clearance Requirements",
                        Url = code.ReferenceUrl
                    },
                    RecommendedAction = $"Increase clearance to at least {code.MinimumClearance}\" or request a variance"
                });
            }            // Check spacing requirements
            if (element.SystemType.Contains("Support") && element.SpacingInterval > code.MaximumSpacing)
            {
                issues.Add(new ComplianceIssue
                {
                    ElementId = element.ElementId.ToString(),
                    SystemType = element.SystemType,
                    IssueType = ComplianceIssueType.SpacingViolation,
                    Description = $"Support spacing exceeds maximum: {element.SpacingInterval}' provided, {code.MaximumSpacing}' maximum allowed by {code.Code}",
                    Severity = ComplianceIssueSeverity.Major,
                    CodeReference = new CodeReference
                    {
                        Code = code.Code,
                        Section = code.Sections.FirstOrDefault(s => s.Contains("spacing")) ?? code.Sections.First(),
                        Title = "Maximum Support Spacing Requirements",
                        Url = code.ReferenceUrl
                    },
                    RecommendedAction = $"Add additional supports to maintain maximum spacing of {code.MaximumSpacing}'"
                });
            }            // Check sizing requirements
            if (!MeetsSizingRequirements(element, code))
            {
                issues.Add(new ComplianceIssue
                {
                    ElementId = element.ElementId.ToString(),
                    SystemType = element.SystemType,
                    IssueType = ComplianceIssueType.SizingViolation,
                    Description = $"Element sizing does not meet requirements in {code.Code}",
                    Severity = ComplianceIssueSeverity.Major,
                    CodeReference = new CodeReference
                    {
                        Code = code.Code,
                        Section = code.Sections.FirstOrDefault(s => s.Contains("size")) ?? code.Sections.First(),
                        Title = "Sizing Requirements",
                        Url = code.ReferenceUrl
                    },
                    RecommendedAction = "Review element sizing against code requirements and adjust as needed"
                });
            }

            // Use transformer model for advanced compliance checks
            var advancedIssues = await RunTransformerComplianceChecksAsync(element, code);
            issues.AddRange(advancedIssues);

            return issues;
        }

        /// <summary>
        /// Uses the transformer model to perform advanced compliance checks
        /// </summary>
        private async Task<List<ComplianceIssue>> RunTransformerComplianceChecksAsync(MEPElementData element, BuildingCodeStandard code)
        {
            try
            {
                // Convert the element data to model input format
                var elementVector = ConvertElementToInputVector(element);
                var codeVector = ConvertCodeToInputVector(code);

                // Combine inputs for the model
                var combinedInput = MergeVectors(elementVector, codeVector);

                // Use transformer model to predict compliance issues
                var predictions = await _transformerModel.PredictComplianceIssuesAsync(combinedInput);

                // Convert predictions to compliance issues
                return InterpretPredictions(predictions, element, code);
            }
            catch (Exception ex)
            {
                // Return empty list on error, but log the exception
                System.Diagnostics.Debug.WriteLine($"Error in transformer compliance check: {ex}");
                return new List<ComplianceIssue>();
            }
        }

        /// <summary>
        /// Converts element data to input vector for transformer model
        /// </summary>
        private double[] ConvertElementToInputVector(MEPElementData element)
        {
            // This is a simplified version - actual implementation would be more comprehensive
            return new double[]
            {
                NormalizeSystemType(element.SystemType),
                element.Position.X / 1000.0,
                element.Position.Y / 1000.0,
                element.Position.Z / 100.0,
                element.Dimensions.Width / 10.0,
                element.Dimensions.Height / 10.0,
                element.Dimensions.Length / 100.0,
                element.SystemClearance / 36.0,  // Normalize to 3 feet max
                element.SpacingInterval / 20.0,  // Normalize to 20 feet max
                element.FlowRate / 1000.0,       // Normalize to 1000 units max
                element.Pressure / 100.0,        // Normalize to 100 units max
                element.Temperature / 212.0      // Normalize to boiling point (Fahrenheit)
            };
        }

        /// <summary>
        /// Converts code data to input vector for transformer model
        /// </summary>
        private double[] ConvertCodeToInputVector(BuildingCodeStandard code)
        {
            // This is a simplified version - actual implementation would be more comprehensive
            return new double[]
            {
                code.Year / 2025.0,              // Normalize to current year
                code.MinimumClearance / 36.0,    // Normalize to 3 feet max
                code.MaximumSpacing / 20.0,      // Normalize to 20 feet max
                code.Priority / 10.0            // Normalize to 10 max
            };
        }

        /// <summary>
        /// Merges multiple vectors for model input
        /// </summary>
        private double[] MergeVectors(params double[][] vectors)
        {
            int totalLength = vectors.Sum(v => v.Length);
            double[] result = new double[totalLength];

            int currentIndex = 0;
            foreach (var vector in vectors)
            {
                Array.Copy(vector, 0, result, currentIndex, vector.Length);
                currentIndex += vector.Length;
            }

            return result;
        }

        /// <summary>
        /// Interprets model predictions and converts to compliance issues
        /// </summary>
        private List<ComplianceIssue> InterpretPredictions(double[] predictions, MEPElementData element, BuildingCodeStandard code)
        {
            var issues = new List<ComplianceIssue>();

            // Simplified interpretation - in reality, would use classification or more complex logic            if (predictions[0] > 0.7) // High probability of clearance issue
            {
                issues.Add(new ComplianceIssue
                {
                    ElementId = element.ElementId.ToString(),
                    SystemType = element.SystemType,
                    IssueType = ComplianceIssueType.ClearanceViolation,
                    Description = $"AI detected potential clearance issues with {element.SystemType} element",
                    Severity = ComplianceIssueSeverity.Major,
                    CodeReference = new CodeReference
                    {
                        Code = code.Code,
                        Section = code.Sections.FirstOrDefault(s => s.Contains("clearance")) ?? code.Sections.First(),
                        Title = "Clearance Requirements",
                        Url = code.ReferenceUrl
                    },
                    RecommendedAction = "Review element positioning and clearances"
                });
            }
            if (predictions[1] > 0.6) // Medium-high probability of fire safety issue
            {
                issues.Add(new ComplianceIssue
                {
                    ElementId = element.ElementId.ToString(),
                    SystemType = element.SystemType,
                    IssueType = ComplianceIssueType.FireSafetyViolation,
                    Description = $"AI detected potential fire safety issues with {element.SystemType} element",
                    Severity = ComplianceIssueSeverity.Critical,
                    CodeReference = new CodeReference
                    {
                        Code = code.Code,
                        Section = code.Sections.FirstOrDefault(s => s.Contains("fire")) ?? code.Sections.First(),
                        Title = "Fire Safety Requirements",
                        Url = code.ReferenceUrl
                    },
                    RecommendedAction = "Review fire safety compliance and fire stopping measures"
                });
            }

            return issues;
        }

        /// <summary>
        /// Checks if an element meets sizing requirements according to a code
        /// </summary>
        private bool MeetsSizingRequirements(MEPElementData element, BuildingCodeStandard code)
        {
            // Simplified implementation
            switch (element.SystemType)
            {
                case "HVAC Duct":
                    return element.Dimensions.Width >= 4 && element.Dimensions.Height >= 4;

                case "Water Supply":
                    return element.Dimensions.Width >= 0.5;

                case "Electrical Conduit":
                    return element.Dimensions.Width >= 0.5;

                default:
                    return true; // Assume compliant for other types
            }
        }

        /// <summary>
        /// Gets applicable code standards based on the element system type
        /// </summary>
        private List<BuildingCodeStandard> GetApplicableCodeStandards(MEPElementData element)
        {
            var result = new List<BuildingCodeStandard>();

            // Add general building code
            if (_buildingCodeStandards.TryGetValue("IBC", out var generalCode))
                result.Add(generalCode);

            // Add system-specific codes
            if (element.SystemType.Contains("HVAC") || element.SystemType.Contains("Mechanical"))
            {
                if (_buildingCodeStandards.TryGetValue("IMC", out var mechanicalCode))
                    result.Add(mechanicalCode);
            }

            if (element.SystemType.Contains("Water") || element.SystemType.Contains("Sanitary") ||
                element.SystemType.Contains("Storm") || element.SystemType.Contains("Gas"))
            {
                if (_buildingCodeStandards.TryGetValue("IPC", out var plumbingCode))
                    result.Add(plumbingCode);
            }

            if (element.SystemType.Contains("Electrical") || element.SystemType.Contains("Power") ||
                element.SystemType.Contains("Lighting"))
            {
                if (_buildingCodeStandards.TryGetValue("NEC", out var electricalCode))
                    result.Add(electricalCode);
            }

            return result;
        }

        /// <summary>
        /// Calculate compliance statistics by category
        /// </summary>
        private Dictionary<string, ComplianceCategoryStatistics> CalculateComplianceCategories(List<ComplianceIssue> issues)
        {
            var categories = new Dictionary<string, ComplianceCategoryStatistics>();

            // Set up categories
            categories["Fire Safety"] = new ComplianceCategoryStatistics();
            categories["Clearance"] = new ComplianceCategoryStatistics();
            categories["Sizing"] = new ComplianceCategoryStatistics();
            categories["Support"] = new ComplianceCategoryStatistics();
            categories["Material"] = new ComplianceCategoryStatistics();
            categories["Other"] = new ComplianceCategoryStatistics();

            // Count issues by category
            foreach (var issue in issues)
            {
                switch (issue.IssueType)
                {
                    case ComplianceIssueType.FireSafetyViolation:
                        categories["Fire Safety"].IssueCount++;
                        categories["Fire Safety"].IsBelowThreshold = false;
                        break;

                    case ComplianceIssueType.ClearanceViolation:
                        categories["Clearance"].IssueCount++;
                        categories["Clearance"].IsBelowThreshold = false;
                        break;

                    case ComplianceIssueType.SizingViolation:
                        categories["Sizing"].IssueCount++;
                        categories["Sizing"].IsBelowThreshold = false;
                        break;

                    case ComplianceIssueType.SpacingViolation:
                        categories["Support"].IssueCount++;
                        categories["Support"].IsBelowThreshold = false;
                        break;

                    case ComplianceIssueType.MaterialViolation:
                        categories["Material"].IssueCount++;
                        categories["Material"].IsBelowThreshold = false;
                        break;

                    default:
                        categories["Other"].IssueCount++;
                        categories["Other"].IsBelowThreshold = false;
                        break;
                }
            }

            return categories;
        }

        /// <summary>
        /// Helper to normalize system type to a numeric value for AI model input
        /// </summary>
        private double NormalizeSystemType(string systemType)
        {
            // Map system types to numeric values between 0 and 1
            switch (systemType)
            {
                case "HVAC Duct": return 0.1;
                case "HVAC Pipe": return 0.15;
                case "Water Supply": return 0.2;
                case "Sanitary Waste": return 0.25;
                case "Storm Drainage": return 0.3;
                case "Natural Gas": return 0.35;
                case "Fire Protection": return 0.4;
                case "Electrical Power": return 0.45;
                case "Electrical Lighting": return 0.5;
                case "Data/Comm": return 0.55;
                case "Security": return 0.6;
                case "Medical Gas": return 0.65;
                case "Pneumatic": return 0.7;
                case "Hydronic": return 0.75;
                case "Steam": return 0.8;
                case "Condenser Water": return 0.85;
                case "Chilled Water": return 0.9;
                case "Hot Water": return 0.95;
                default: return 0.0;
            }
        }

        /// <summary>
        /// Initialize standard building codes database
        /// </summary>
        private Dictionary<string, BuildingCodeStandard> InitializeBuildingCodes()
        {
            return new Dictionary<string, BuildingCodeStandard>
            {
                { "IBC", new BuildingCodeStandard
                    {
                        Code = "IBC 2021",
                        Title = "International Building Code",
                        Year = 2021,
                        Sections = new List<string> { "Chapter 9", "Chapter 10", "Chapter 16" },
                        MinimumClearance = 30.0,
                        MaximumSpacing = 16.0,
                        Priority = 9,
                        ReferenceUrl = "https://codes.iccsafe.org/content/IBC2021P2"
                    }
                },
                { "IMC", new BuildingCodeStandard
                    {
                        Code = "IMC 2021",
                        Title = "International Mechanical Code",
                        Year = 2021,
                        Sections = new List<string> { "Section 306", "Section 603", "Section 1305" },
                        MinimumClearance = 24.0,
                        MaximumSpacing = 12.0,
                        Priority = 8,
                        ReferenceUrl = "https://codes.iccsafe.org/content/IMC2021P1"
                    }
                },
                { "IPC", new BuildingCodeStandard
                    {
                        Code = "IPC 2021",
                        Title = "International Plumbing Code",
                        Year = 2021,
                        Sections = new List<string> { "Section 305", "Section 308", "Section 403" },
                        MinimumClearance = 18.0,
                        MaximumSpacing = 10.0,
                        Priority = 8,
                        ReferenceUrl = "https://codes.iccsafe.org/content/IPC2021P1"
                    }
                },
                { "NEC", new BuildingCodeStandard
                    {
                        Code = "NFPA 70 (NEC) 2023",
                        Title = "National Electrical Code",
                        Year = 2023,
                        Sections = new List<string> { "Article 110", "Article 300", "Article 408" },
                        MinimumClearance = 36.0,
                        MaximumSpacing = 8.0,
                        Priority = 9,
                        ReferenceUrl = "https://www.nfpa.org/codes-and-standards/all-codes-and-standards/list-of-codes-and-standards/detail?code=70"
                    }
                },
                { "NFPA", new BuildingCodeStandard
                    {
                        Code = "NFPA 13 2022",
                        Title = "Standard for the Installation of Sprinkler Systems",
                        Year = 2022,
                        Sections = new List<string> { "Chapter 8", "Chapter 9", "Chapter 11" },
                        MinimumClearance = 18.0,
                        MaximumSpacing = 15.0,
                        Priority = 10,
                        ReferenceUrl = "https://www.nfpa.org/codes-and-standards/all-codes-and-standards/list-of-codes-and-standards/detail?code=13"
                    }
                }
            };
        }
    }

    /// <summary>
    /// Represents a building code standard with key requirements
    /// </summary>
    public class BuildingCodeStandard
    {
        public string Code { get; set; }
        public string Title { get; set; }
        public int Year { get; set; }
        public List<string> Sections { get; set; }
        public double MinimumClearance { get; set; } // In inches
        public double MaximumSpacing { get; set; } // In feet
        public int Priority { get; set; } // 1-10 scale, 10 being highest priority
        public string ReferenceUrl { get; set; }
    }

    /// <summary>
    /// Results of code compliance verification
    /// </summary>
    public class ComplianceVerificationResult
    {
        public List<ComplianceIssue> ComplianceIssues { get; set; }
        public double OverallComplianceScore { get; set; } // 0-100 scale
        public List<CodeReference> CodeReferences { get; set; }
        public Dictionary<string, ComplianceCategoryStatistics> ComplianceCategories { get; set; }
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Represents a specific compliance issue detected during verification
    /// </summary>
    public class ComplianceIssue
    {
        public string ElementId { get; set; }
        public string SystemType { get; set; }
        public ComplianceIssueType IssueType { get; set; }
        public string Description { get; set; }
        public ComplianceIssueSeverity Severity { get; set; }
        public CodeReference CodeReference { get; set; }
        public string RecommendedAction { get; set; }
    }

    /// <summary>
    /// Reference to a building code section
    /// </summary>
    public class CodeReference
    {
        public string Code { get; set; }
        public string Section { get; set; }
        public string Title { get; set; }
        public string Url { get; set; }
    }

    /// <summary>
    /// Types of compliance issues that can be detected
    /// </summary>
    public enum ComplianceIssueType
    {
        ClearanceViolation,
        FireSafetyViolation,
        SizingViolation,
        SpacingViolation,
        MaterialViolation,
        InstallationViolation,
        DocumentationViolation,
        AccessibilityViolation,
        OtherViolation
    }

    /// <summary>
    /// Severity levels for compliance issues
    /// </summary>
    public enum ComplianceIssueSeverity
    {
        Critical,   // Must be fixed - safety concern
        Major,      // Important to address
        Minor,      // Should be fixed but not critical
        Advisory    // Best practice recommendation
    }

    /// <summary>
    /// Statistics for a compliance category
    /// </summary>
    public class ComplianceCategoryStatistics
    {
        public int IssueCount { get; set; } = 0;
        public bool IsBelowThreshold { get; set; } = true;
    }
}
