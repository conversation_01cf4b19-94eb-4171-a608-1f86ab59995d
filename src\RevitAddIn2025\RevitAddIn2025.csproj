<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProjectGuid>{A7B5C4D3-2E8F-4A9B-8C7D-5E2F1A3B6C8D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>RevitAddIn2025</RootNamespace>
    <AssemblyName>RevitAddIn2025</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System.Xaml" />
    <Reference Include="RevitAPI">
      <HintPath>C:\Program Files\Autodesk\Revit 2025\RevitAPI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RevitAPIUI">
      <HintPath>C:\Program Files\Autodesk\Revit 2025\RevitAPIUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="App\RevitApplication.cs" />

    <!-- Essential Commands -->
    <Compile Include="Commands\Commands.cs" />

    <!-- Development Commands (Debug only) -->
    <Compile Include="Commands\DevReloadCommand.cs" Condition="'$(Configuration)' == 'Debug'" />

    <!-- Development Tools (Debug only) -->
    <Compile Include="Development\HotReloadManager.cs" Condition="'$(Configuration)' == 'Debug'" />

    <!-- Essential Utilities -->
    <Compile Include="Utilities\ResourceHelper.cs" />
    <Compile Include="Utilities\Logger.cs" />
    <Compile Include="Utilities\ReportGenerator.cs" />
    <Compile Include="Utilities\RevitUtils.cs" />
    <Compile Include="Utilities\DeploymentUtils.cs" />

    <!-- Resources -->
    <Compile Include="Resources\Icons\IconGenerator.cs" />

    <!-- Models (Essential for functionality) -->
    <Compile Include="Models\AppSettings.cs" />
    <Compile Include="Models\ProjectAnalytics.cs" />
    <Compile Include="Models\MEPOptimizationResult.cs" />
    <Compile Include="Models\MEPElementData.cs" />
    <Compile Include="Models\MEPPosition3D.cs" />
    <Compile Include="Models\MEPDimensions.cs" />
    <Compile Include="Models\MEPTransformerAnalyzer.cs" />
    <Compile Include="Models\MEPSystemClassifier.cs" />
    <Compile Include="Models\MEPSystemTypes.cs" />
    <Compile Include="Models\MEPEnergyOptimizer.cs" />
    <Compile Include="Models\MEPCodeComplianceVerifier.cs" />
    <Compile Include="Models\MEPClashDetector.cs" />

    <!-- AI Components - FULLY ACTIVATED -->
    <Compile Include="AI\MEPTransformerModel.cs" />
    <Compile Include="AI\Transformer\MEPDataModels.cs" />

    <!-- Commands - AI Integration -->
    <Compile Include="Commands\RevitTransformerMEPPlugin.cs" />
    <Compile Include="Commands\MEPCodeComplianceCommand.cs" />

    <!-- UI Components - AI Integration -->
    <Compile Include="UI\HelpWindow.cs" />
    <Compile Include="UI\TransformerMEPDialog.cs" />
    <Compile Include="UI\MEPTransformerDialog.cs" />
    <Compile Include="UI\MEPTransformerRibbonPanel.cs" />
    <Compile Include="UI\MEPCodeComplianceVisualization.cs" />
    <Compile Include="UI\MEPEnergyVisualization.cs" />

    <!-- UI Components - FULLY ACTIVATED -->
    <Compile Include="UI\Common\BaseWindow.cs" />
    <Compile Include="UI\Common\ResourceLoader.cs" />
    <Compile Include="UI\Common\ThemeManager.cs" />
    <!-- <Compile Include="UI\Common\TitleBarControl.xaml.cs">
      <DependentUpon>TitleBarControl.xaml</DependentUpon>
    </Compile> -->
    <Compile Include="UI\Dashboard\DashboardWindow.xaml.cs">
      <DependentUpon>DashboardWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\Settings\SettingsWindow.xaml.cs">
      <DependentUpon>SettingsWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="UI\DashboardClasses.cs" />
    <Compile Include="UI\DashboardWindow.cs" />
    <Compile Include="UI\UIResources.cs" />
  </ItemGroup>

  <ItemGroup>
    <!-- XAML Pages - DISABLED (Using Programmatic UI) -->
    <!-- <Page Include="UI\Common\AppStyles.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page> -->
    <!-- <Page Include="UI\Common\ResourceDictionary.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page> -->
    <!-- <Page Include="UI\Common\TitleBarControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page> -->
    <!-- <Page Include="UI\Dashboard\DashboardWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page> -->
    <!-- <Page Include="UI\Settings\SettingsWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page> -->
  </ItemGroup>

  <ItemGroup>
    <!-- Icon Resources -->
    <Resource Include="Resources\Icons\PNG\*.png" />
    <Resource Include="Icons\*.png" />
  </ItemGroup>

  <!-- Remove App.xaml and App.xaml.cs to avoid conflicts with Revit hosting -->

  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
