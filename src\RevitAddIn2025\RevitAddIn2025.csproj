<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x64</Platform>
    <ProjectGuid>{A7B5C4D3-2E8F-4A9B-8C7D-5E2F1A3B6C8D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>RevitAddIn2025</RootNamespace>
    <AssemblyName>RevitAddIn2025</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <PlatformTarget>x64</PlatformTarget>
    <RestorePackages>false</RestorePackages>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System.Xaml" />
    <Reference Include="RevitAPI">
      <HintPath>C:\Program Files\Autodesk\Revit 2025\RevitAPI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="RevitAPIUI">
      <HintPath>C:\Program Files\Autodesk\Revit 2025\RevitAPIUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="App\RevitApplication.cs" />

    <!-- Essential Commands Only -->
    <Compile Include="Commands\Commands.cs" />

    <!-- Essential Utilities -->
    <Compile Include="Utilities\ResourceHelper.cs" />
  </ItemGroup>

  <ItemGroup>
    <!-- Icon Resources -->
    <Resource Include="Resources\Icons\PNG\*.png" />
    <Resource Include="Icons\*.png" />
  </ItemGroup>

  <!-- Remove App.xaml and App.xaml.cs to avoid conflicts with Revit hosting -->

  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
