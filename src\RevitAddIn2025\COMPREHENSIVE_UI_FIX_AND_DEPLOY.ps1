# COMPREHENSIVE UI FIX AND DEPLOY - RevitAddIn2025
# This script fixes all UI loading issues and deploys the plugin correctly

param(
    [switch]$Force,
    [switch]$Verbose
)

# Configuration
$ErrorActionPreference = "Stop"
$scriptDir = $PSScriptRoot
$projectRoot = Split-Path -Parent (Split-Path -Parent $scriptDir)
$buildOutput = Join-Path $projectRoot "bin\Release"
$revitAddinsFolder = "$env:APPDATA\Autodesk\Revit\Addins\2025"
$revitAddinFolder = Join-Path $revitAddinsFolder "RevitAddIn2025"
$revitAddinFile = Join-Path $revitAddinsFolder "RevitAddIn2025.addin"

# Colors for output
function Write-ColorText($text, $color) {
    Write-Host $text -ForegroundColor $color
}

function Write-Step($text) {
    Write-ColorText "`n🔧 $text" "Cyan"
}

function Write-Success($text) {
    Write-ColorText "✅ $text" "Green"
}

function Write-Error($text) {
    Write-ColorText "❌ $text" "Red"
}

function Write-Warning($text) {
    Write-ColorText "⚠️ $text" "Yellow"
}

# Header
Write-ColorText "`n🚀 COMPREHENSIVE UI FIX AND DEPLOY - RevitAddIn2025" "Magenta"
Write-ColorText "=" * 60 "Magenta"

# Step 1: Verify build output exists
Write-Step "Verifying build output"
$dllPath = Join-Path $buildOutput "RevitAddIn2025.dll"
if (-not (Test-Path $dllPath)) {
    Write-Error "DLL not found at: $dllPath"
    Write-ColorText "Building project first..." "Yellow"

    $projectFile = Join-Path $scriptDir "RevitAddIn2025.csproj"
    try {
        & dotnet build $projectFile -c Release -p:Platform=x64 --verbosity minimal
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Build completed successfully"
        }
        else {
            Write-Error "Build failed with exit code: $LASTEXITCODE"
            exit 1
        }
    }
    catch {
        Write-Error "Build failed: $_"
        exit 1
    }
}

if (Test-Path $dllPath) {
    $dllInfo = Get-Item $dllPath
    $sizeKB = [math]::Round($dllInfo.Length / 1KB, 2)
    Write-Success "DLL found: $($dllInfo.Name) ($sizeKB KB)"
    Write-ColorText "  Last modified: $($dllInfo.LastWriteTime)" "Gray"
}
else {
    Write-Error "DLL still not found after build attempt"
    exit 1
}

# Step 2: Clean existing Revit deployment
Write-Step "Cleaning existing Revit deployment"
if (Test-Path $revitAddinFolder) {
    try {
        Remove-Item -Path $revitAddinFolder -Recurse -Force
        Write-Success "Cleaned existing deployment folder"
    }
    catch {
        Write-Warning "Could not clean deployment folder: $_"
    }
}

if (Test-Path $revitAddinFile) {
    try {
        Remove-Item -Path $revitAddinFile -Force
        Write-Success "Removed existing .addin file"
    }
    catch {
        Write-Warning "Could not remove existing .addin file: $_"
    }
}

# Step 3: Create deployment directory
Write-Step "Creating deployment directory"
try {
    New-Item -ItemType Directory -Path $revitAddinFolder -Force | Out-Null
    Write-Success "Created deployment directory: $revitAddinFolder"
}
catch {
    Write-Error "Failed to create deployment directory: $_"
    exit 1
}

# Step 4: Copy DLL and dependencies
Write-Step "Copying DLL and dependencies"
try {
    # Copy main DLL
    Copy-Item -Path $dllPath -Destination $revitAddinFolder -Force
    Write-Success "Copied main DLL"

    # Copy PDB file if exists
    $pdbPath = Join-Path $buildOutput "RevitAddIn2025.pdb"
    if (Test-Path $pdbPath) {
        Copy-Item -Path $pdbPath -Destination $revitAddinFolder -Force
        Write-Success "Copied PDB file for debugging"
    }

    # Copy any additional dependencies
    $additionalFiles = Get-ChildItem -Path $buildOutput -File | Where-Object {
        $_.Name -ne "RevitAddIn2025.dll" -and
        $_.Name -ne "RevitAddIn2025.pdb" -and
        $_.Extension -in @(".dll", ".config", ".json")
    }

    foreach ($file in $additionalFiles) {
        Copy-Item -Path $file.FullName -Destination $revitAddinFolder -Force
        Write-ColorText "  Copied dependency: $($file.Name)" "Gray"
    }

}
catch {
    Write-Error "Failed to copy files: $_"
    exit 1
}

# Step 5: Create correct .addin file
Write-Step "Creating correct .addin file"
$addinContent = @"
<?xml version="1.0" encoding="utf-8"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>RevitAddIn2025</Name>
    <Assembly>$revitAddinFolder\RevitAddIn2025.dll</Assembly>
    <AddInId>B8399B5E-9B39-42E9-9B1D-869C8F59E76E</AddInId>
    <FullClassName>RevitAddIn2025.App.RevitApplication</FullClassName>
    <VendorId>REVIT2025</VendorId>
    <VendorDescription>Revit 2025 Add-in with Apple-inspired UI</VendorDescription>
  </AddIn>
</RevitAddIns>
"@

try {
    Set-Content -Path $revitAddinFile -Value $addinContent -Encoding UTF8
    Write-Success "Created .addin file with absolute path"
    Write-ColorText "  Assembly path: $revitAddinFolder\RevitAddIn2025.dll" "Gray"
}
catch {
    Write-Error "Failed to create .addin file: $_"
    exit 1
}

# Step 6: Verify deployment
Write-Step "Verifying deployment"
$deployedDll = Join-Path $revitAddinFolder "RevitAddIn2025.dll"
if (Test-Path $deployedDll) {
    $deployedInfo = Get-Item $deployedDll
    $deployedSizeKB = [math]::Round($deployedInfo.Length / 1KB, 2)
    Write-Success "Deployed DLL verified: $($deployedInfo.Name) ($deployedSizeKB KB)"
}
else {
    Write-Error "Deployed DLL not found!"
    exit 1
}

if (Test-Path $revitAddinFile) {
    Write-Success "Deployment .addin file verified"
}
else {
    Write-Error "Deployment .addin file not found!"
    exit 1
}

# Step 7: Final verification and instructions
Write-Step "Final verification complete"
Write-ColorText "`n🎯 DEPLOYMENT SUMMARY:" "Green"
Write-ColorText "✅ DLL deployed to: $deployedDll" "Green"
Write-ColorText "✅ .addin file created: $revitAddinFile" "Green"
Write-ColorText "✅ Assembly path uses absolute path (FIXED)" "Green"

Write-ColorText "`n🚀 NEXT STEPS:" "Cyan"
Write-ColorText "1. Close Revit completely if it's running" "White"
Write-ColorText "2. Start Revit 2025" "White"
Write-ColorText "3. Look for startup dialogs from the plugin" "White"
Write-ColorText "4. Check for 'RevitAddIn2025' tab in the ribbon" "White"
Write-ColorText "5. Test the ribbon buttons" "White"

Write-ColorText "`n🔍 TROUBLESHOOTING:" "Yellow"
Write-ColorText "• If no startup dialogs appear, check Windows Event Viewer" "White"
Write-ColorText "• If ribbon doesn't appear, the .addin file may not be loaded" "White"
Write-ColorText "• If buttons don't work, check the log files" "White"

Write-ColorText "`n✅ DEPLOYMENT COMPLETED SUCCESSFULLY!" "Green"
