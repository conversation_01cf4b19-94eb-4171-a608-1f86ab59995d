using System;
using System.Collections.Generic;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using RevitAddIn2025.Commands;
using RevitAddIn2025.Models;

namespace RevitAddIn2025.UI
{
    /// <summary>
    /// Helper class to manage MEP Transformer panel and buttons in the Revit ribbon
    /// </summary>
    public class MEPTransformerRibbonPanel
    {
        private readonly UIControlledApplication _application;
        private readonly string _tabName;
        private RibbonPanel _panel;

        public MEPTransformerRibbonPanel(UIControlledApplication application, string tabName)
        {
            _application = application;
            _tabName = tabName;
        }

        /// <summary>
        /// Creates the MEP Transformer panel in the specified ribbon tab
        /// </summary>
        public void Create()
        {
            // Create panel
            _panel = _application.CreateRibbonPanel(_tabName, "MEP AI");

            // Create button for MEP Transformer
            CreateMEPTransformerButton();

            // Create split buttons for additional tools
            CreateMEPAnalysisTools();
        }

        /// <summary>
        /// Creates the main MEP Transformer button
        /// </summary>
        private void CreateMEPTransformerButton()
        {
            // Create push button data
            PushButtonData buttonData = new PushButtonData(
                "MEPTransformerButton",
                "MEP\nTransformer",
                typeof(MEPTransformerCommand).Assembly.Location,
                typeof(MEPTransformerCommand).FullName)
            {
                ToolTip = "AI-powered MEP coordination with transformer neural networks",
                LongDescription = "Use advanced AI to optimize MEP systems, detect clashes, improve energy efficiency, and ensure code compliance.",
                Image = LoadStandardIcon(StandardIcons.IconInfo),
                LargeImage = LoadStandardIcon(StandardIcons.IconInfo)
            };

            // Add button to panel
            PushButton button = _panel.AddItem(buttonData) as PushButton;
            button.AvailabilityClassName = typeof(MEPTransformerCommand).FullName;
        }        /// <summary>
                 /// Creates split button with MEP analysis tools
                 /// </summary>
        private void CreateMEPAnalysisTools()
        {
            // Create data for MEP analysis tools
            SplitButtonData splitButtonData = new SplitButtonData("MEPAnalysisTools", "MEP\nAnalysis");
            SplitButton splitButton = _panel.AddItem(splitButtonData) as SplitButton;

            // Create analysis buttons
            PushButtonData clashDetectionData = new PushButtonData(
                "ClashDetection",
                "Clash Detection",
                typeof(MEPTransformerCommand).Assembly.Location,
                typeof(MEPTransformerCommand).FullName)
            {
                ToolTip = "Detect clashes between MEP elements",
                LargeImage = LoadStandardIcon(StandardIcons.IconCheckmark)
            };

            PushButtonData energyAnalysisData = new PushButtonData(
                "EnergyAnalysis",
                "Energy Analytics",
                typeof(EnergyAnalyticsCommand).Assembly.Location,
                typeof(EnergyAnalyticsCommand).FullName)
            {
                ToolTip = "Comprehensive energy consumption and carbon footprint analysis using ASHRAE standards",
                LongDescription = "Advanced energy analytics with real-time calculations, carbon footprint analysis, and optimization recommendations based on industry standards.",
                LargeImage = LoadStandardIcon(StandardIcons.IconCheckmark)
            };

            PushButtonData codeComplianceData = new PushButtonData(
                "CodeCompliance",
                "Code Compliance",
                typeof(MEPCodeComplianceCommand).Assembly.Location,
                typeof(MEPCodeComplianceCommand).FullName)
            {
                ToolTip = "Verify MEP systems against building codes and standards",
                LongDescription = "Use AI-powered analysis to check your MEP systems against applicable building codes, identify compliance issues, and get remediation recommendations.",
                LargeImage = LoadStandardIcon(StandardIcons.IconInfo)
            };

            // Add buttons to split button
            splitButton.AddPushButton(clashDetectionData);
            splitButton.AddPushButton(energyAnalysisData);
            splitButton.AddPushButton(codeComplianceData);
        }

        /// <summary>
        /// Helper method to load a standard Revit icon as a fallback until custom icons are created
        /// </summary>
        private BitmapSource LoadStandardIcon(StandardIcons icon)
        {
            // This is a placeholder - in a real implementation, we would load actual icon files
            return new BitmapImage(new Uri("pack://application:,,,/RevitAddIn2025;component/Resources/mep_transformer_icon.png"));
        }

        /// <summary>
        /// Standard Revit icons for fallback use
        /// </summary>
        internal enum StandardIcons
        {
            IconCheckmark,
            IconWarning,
            IconError,
            IconInfo
        }
    }
}
