{"version": "2.0.0", "tasks": [{"label": "🤖 FULLY AUTONOMOUS REVIT SYSTEM", "type": "shell", "command": "powershell.exe", "args": ["-ExecutionPolicy", "Bypass", "-File", "FULLY_AUTONOMOUS_SYSTEM.ps1", "-MonitorIntervalSeconds", "30"], "group": "build", "isBackground": true, "problemMatcher": []}, {"label": "🚀 Start MCP Server", "type": "shell", "command": "node", "args": ["${workspaceFolder}/src/RevitAddIn2025/AI/MCP/mcp-server.js"], "options": {"env": {"MCP_SERVER_PORT": "3000", "MCP_SERVER_NAME": "revit-mep-ai-enhanced-server", "MCP_SERVER_VERSION": "v2.0.0", "MCP_LEARNING_ENABLED": "true", "MCP_REVIT_WORKSPACE": "${workspaceFolder}/src/RevitAddIn2025", "MCP_ENHANCED_MODE": "true", "MCP_AUTO_ENHANCE": "true"}}, "problemMatcher": [], "isBackground": true, "presentation": {"reveal": "always", "panel": "new"}}, {"label": "📋 Install MCP Server Dependencies", "type": "shell", "command": "cd ${workspaceFolder}/src/RevitAddIn2025/AI/MCP && npm install", "presentation": {"reveal": "always", "panel": "new"}}, {"label": "🔄 Update Claude Desktop MCP Config", "type": "shell", "command": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "args": ["-Path", "${workspaceFolder}/claude_mcp_fixed_config.json", "-Destination", "${workspaceFolder}/claude_desktop_config.json", "-Force"], "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}, {"label": "🧪 Test MCP Connection", "type": "shell", "command": "powershell.exe", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/MCP_CONNECTION_TESTER.ps1"], "presentation": {"reveal": "always", "panel": "new"}, "problemMatcher": []}]}