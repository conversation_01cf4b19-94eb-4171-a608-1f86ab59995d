# RevitAddIn2025 Fix Guide

## The Problem

The "Bad IL format" error occurs because your add-in's DLL file is corrupted or was built with an incompatible .NET version for Revit 2025.

## One-Click Auto-Fix Solution

For the most comprehensive fix, use our new auto-fix script:

```powershell
cd C:\GITHUB\src\RevitAddIn2025
powershell -ExecutionPolicy Bypass -File .\RevitAutoFix.ps1
```

This powerful script will automatically:
- Close any running Revit processes
- Remove corrupted files
- Fix .NET version targeting in the project file
- Create proper directory structure
- Build the add-in or create a minimal working version
- Generate correct `.addin` manifest with absolute paths
- Validate the installation

## Alternative Manual Fix Steps

If you prefer to fix the issue manually, follow these steps:

1. **Close Revit 2025** completely

2. **Run the following commands** in a PowerShell window with administrator privileges:

```powershell
# Go to the Revit add-ins folder
$addinPath = Join-Path $env:APPDATA "Autodesk\Revit\Addins\2025"
$dllFolder = Join-Path $addinPath "RevitAddIn2025"

# Create folders if they don't exist
if (-not (Test-Path $addinPath)) {
    New-Item -ItemType Directory -Path $addinPath -Force
}
if (-not (Test-Path $dllFolder)) {
    New-Item -ItemType Directory -Path $dllFolder -Force
}

# Fix the .addin file with correct <Name> tag
$addinContent = @'
<?xml version="1.0" encoding="utf-8" standalone="no"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>RevitAddIn2025</Name>
    <Assembly>[APPDATA]\Autodesk\Revit\Addins\2025\RevitAddIn2025\RevitAddIn2025.dll</Assembly>
    <AddInId>B8399B5E-9B39-42E9-9B1D-869C8F59E76E</AddInId>
    <FullClassName>RevitAddIn2025.App.RevitApplication</FullClassName>
    <VendorId>YourCompanyName</VendorId>
    <VendorDescription>Your Company Description</VendorDescription>
  </AddIn>
</RevitAddIns>
'@
$addinContent = $addinContent.Replace("[APPDATA]", $env:APPDATA)
$addinFilePath = Join-Path $addinPath "RevitAddIn2025.addin"
Set-Content -Path $addinFilePath -Value $addinContent -Force
```

3. **Rebuild the add-in** from source code:

```powershell
# Navigate to your source code directory
cd C:\GITHUB\src 

# Build the solution
dotnet build RevitAddIn2025.sln -c Release /p:Platform=x64
```

4. **Deploy the built files**:

```powershell
# Copy the built DLL and dependencies
$buildPath = "C:\GITHUB\src\bin\Release"
$dllFolder = Join-Path $env:APPDATA "Autodesk\Revit\Addins\2025\RevitAddIn2025"

# Copy all build files
if (Test-Path $buildPath) {
    Copy-Item "$buildPath\*" -Destination $dllFolder -Recurse -Force
}
```

5. **Restart Revit 2025** and the add-in should load properly.

## Other Fix Solutions

If you prefer a guided approach, you can also use one of our other scripts:

```powershell
# Enhanced deployment with more error checking
cd C:\GITHUB\src\RevitAddIn2025
powershell -ExecutionPolicy Bypass -File .\DeployToRevit_Enhanced.ps1

# Simple manual deployment
cd C:\GITHUB\src\RevitAddIn2025
powershell -ExecutionPolicy Bypass -File .\ManualDeploy.ps1
```

## Explanation of Issues

1. **Bad IL Format**: The DLL was likely built with .NET 8.0 but Revit 2025 requires .NET Framework 4.8 compatibility.

2. **Path Issues**: The .addin file needs to specify an absolute path to the DLL file.

3. **XML Format Error**: The .addin file was using `<n>` tag instead of the correct `<Name>` tag.

4. **Corrupted DLL**: The DLL file may have become corrupted during deployment.

## Preventing These Issues in the Future

1. Always use .NET Framework 4.8 for Revit 2025 add-ins
2. Use the proper deployment process with `RevitAutoFix.ps1` or `DeployToRevit_Enhanced.ps1`
3. Use correct XML tags in your .addin files
4. Close Revit before deploying updates to your add-in
5. Ensure proper absolute paths are used in the .addin file

## Technical Details

### Project Configuration
Your project should use these settings in the .csproj file:
```xml
<PropertyGroup>
  <TargetFramework>net48</TargetFramework>
  <ImplicitUsings>disable</ImplicitUsings>
  <Nullable>disable</Nullable>
  <UseWPF>true</UseWPF>
  <PlatformTarget>x64</PlatformTarget>
  <Platforms>x64</Platforms>
  <LangVersion>latest</LangVersion>
</PropertyGroup>
```

### .addin File Format
Your .addin file should follow this format:
```xml
<?xml version="1.0" encoding="utf-8" standalone="no"?>
<RevitAddIns>
  <AddIn Type="Application">
    <Name>RevitAddIn2025</Name>
    <Assembly>[FULL_PATH_TO_DLL]</Assembly>
    <AddInId>B8399B5E-9B39-42E9-9B1D-869C8F59E76E</AddInId>
    <FullClassName>RevitAddIn2025.App.RevitApplication</FullClassName>
    <VendorId>YourCompanyName</VendorId>
    <VendorDescription>Your Company Description</VendorDescription>
  </AddIn>
</RevitAddIns>
```

If you continue to have issues, please contact support with the following information:
- Revit version and build number
- .NET Framework version installed on your machine
- Full error message from Revit's journal files