using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;

namespace RevitAddIn2025.Models
{
    /// <summary>
    /// Result of MEP optimization analysis
    /// </summary>
    public class MEPOptimizationResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public DateTime Timestamp { get; set; }
        public int ElementCount { get; set; }
        public bool HasErrors { get; set; }

        // Analysis scores
        public double OverallEnergyScore { get; set; }
        public double OverallComplianceScore { get; set; }
        public double OptimizationPotential { get; set; }

        // Results
        public List<OptimizationIssue> Issues { get; set; } = new List<OptimizationIssue>();
        public List<OptimizationRecommendation> Recommendations { get; set; } = new List<OptimizationRecommendation>();

        // Statistics
        public int CriticalIssues => Issues.FindAll(i => i.Severity == OptimizationSeverity.Critical).Count;
        public int HighPriorityRecommendations => Recommendations.FindAll(r => r.Priority == OptimizationPriority.High).Count;
        public double AverageConfidence => Issues.Count > 0 ? Issues.Average(i => i.Confidence) : 0.0;
    }

    /// <summary>
    /// Optimization issue found during analysis
    /// </summary>
    public class OptimizationIssue
    {
        public OptimizationIssueType Type { get; set; }
        public ElementId ElementId { get; set; }
        public OptimizationSeverity Severity { get; set; }
        public string Description { get; set; }
        public XYZ Location { get; set; }
        public float Confidence { get; set; }
        public string RecommendedAction { get; set; }
        public DateTime DetectedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Optimization recommendation from AI analysis
    /// </summary>
    public class OptimizationRecommendation
    {
        public OptimizationRecommendationType Type { get; set; }
        public ElementId ElementId { get; set; }
        public OptimizationPriority Priority { get; set; }
        public string Description { get; set; }
        public float ExpectedImprovement { get; set; }
        public OptimizationCost ImplementationCost { get; set; }
        public string DetailedSteps { get; set; }
        public double EstimatedSavings { get; set; }
        public TimeSpan EstimatedImplementationTime { get; set; }
    }

    // Enums for optimization results
    public enum OptimizationIssueType
    {
        Clash,
        CodeViolation,
        EnergyInefficiency,
        SystemFailure,
        MaintenanceRequired,
        PerformanceDegradation
    }

    public enum OptimizationSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    public enum OptimizationRecommendationType
    {
        EnergyOptimization,
        SystemOptimization,
        CodeCompliance,
        MaintenanceImprovement,
        PerformanceEnhancement,
        CostReduction
    }

    public enum OptimizationPriority
    {
        Low,
        Medium,
        High,
        Critical
    }

    public enum OptimizationCost
    {
        Low,
        Medium,
        High
    }
}
