using System;
using System.Windows;
using System.Windows.Input;

namespace RevitAddIn2025.UI.Common
{
    /// <summary>
    /// Base window class that implements common functionality for all application windows
    /// </summary>
    public class BaseWindow : Window
    {
        // Window drag support
        private bool _isMouseDown = false;
        private Point _mouseDownPosition;

        public BaseWindow()
        {
            // Set common window properties
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(242, 242, 247));

            // Add event handlers for window drag
            MouseLeftButtonDown += BaseWindow_MouseLeftButtonDown;
            MouseLeftButtonUp += BaseWindow_MouseLeftButtonUp;
            MouseMove += BaseWindow_MouseMove;

            // Add keyboard event handlers
            KeyDown += BaseWindow_KeyDown;
        }

        private void BaseWindow_KeyDown(object sender, KeyEventArgs e)
        {
            // Toggle theme with Ctrl+Shift+D
            if (e.Key == Key.D && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control
                && (Keyboard.Modifiers & ModifierKeys.Shift) == ModifierKeys.Shift)
            {
                ThemeManager.ToggleTheme();
                e.Handled = true;
            }
        }

        // Window drag handling
        private void BaseWindow_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left && e.ClickCount == 1)
            {
                _isMouseDown = true;
                _mouseDownPosition = e.GetPosition(this);
            }
        }

        private void BaseWindow_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            _isMouseDown = false;
        }

        private void BaseWindow_MouseMove(object sender, MouseEventArgs e)
        {
            if (_isMouseDown)
            {
                // Calculate the new window position
                Point currentPosition = e.GetPosition(this);
                Point delta = new Point(currentPosition.X - _mouseDownPosition.X, currentPosition.Y - _mouseDownPosition.Y);

                // Move the window
                Left += delta.X;
                Top += delta.Y;
            }
        }

        /// <summary>
        /// Shows a custom message box
        /// </summary>
        public static MessageBoxResult ShowMessage(string message, string title, MessageBoxButton buttons = MessageBoxButton.OK)
        {
            return MessageBox.Show(message, title, buttons, MessageBoxImage.Information);
        }

        /// <summary>
        /// Shows a custom error message box
        /// </summary>
        public static MessageBoxResult ShowError(string message, string title, MessageBoxButton buttons = MessageBoxButton.OK)
        {
            return MessageBox.Show(message, title, buttons, MessageBoxImage.Error);
        }
    }
}
