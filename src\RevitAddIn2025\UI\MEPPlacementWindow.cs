using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Threading.Tasks;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Electrical;
using Autodesk.Revit.DB.Plumbing;
using RevitAddIn2025.Models;
using RevitAddIn2025.Utilities;
using RevitAddIn2025.AI;
using WinGrid = System.Windows.Controls.Grid;
using WinColor = System.Windows.Media.Color;
using WinComboBox = System.Windows.Controls.ComboBox;

namespace RevitAddIn2025.UI
{
    /// <summary>
    /// Intelligent MEP Placement Window with AI-powered ceiling services automation
    /// Integrates with Energy Analytics and Clash Detection for optimal placement
    /// </summary>
    public class MEPPlacementWindow : Window
    {
        private readonly UIApplication _uiApp;
        private readonly Document _document;
        private readonly MEPTransformerModel _aiModel;

        // UI Components
        private WinComboBox _systemTypeCombo;
        private WinComboBox _familySelectionCombo;
        private Slider _spacingSlider;
        private TextBlock _spacingValueText;
        private CheckBox _energyOptimizedCheckBox;
        private CheckBox _clashAvoidanceCheckBox;
        private CheckBox _codeComplianceCheckBox;
        private Button _analyzeSpaceButton;
        private Button _generatePlacementButton;
        private Button _previewButton;
        private ListBox _placementResultsList;
        private Canvas _floorPlanCanvas;
        private ProgressBar _analysisProgress;

        // Placement data
        private List<CeilingSpace> _detectedSpaces;
        private List<PlacementRecommendation> _placementRecommendations;
        private PlacementAnalysisResult _currentAnalysis;

        public MEPPlacementWindow(UIApplication uiApp, MEPTransformerModel aiModel)
        {
            _uiApp = uiApp ?? throw new ArgumentNullException(nameof(uiApp));
            _document = _uiApp.ActiveUIDocument?.Document ?? throw new ArgumentNullException("No active document");
            _aiModel = aiModel ?? throw new ArgumentNullException(nameof(aiModel));

            InitializeWindow();
            CreateUI();
            LoadAvailableFamilies();
        }

        private void InitializeWindow()
        {
            Title = "Intelligent MEP Placement System";
            Width = 1400;
            Height = 900;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250));

            FontFamily = new FontFamily("Segoe UI");
            FontSize = 14;
        }

        private void CreateUI()
        {
            var mainGrid = new WinGrid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) }); // Header
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // Content
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) }); // Footer

            CreateHeader(mainGrid);
            CreateMainContent(mainGrid);
            CreateFooter(mainGrid);

            Content = mainGrid;
        }

        private void CreateHeader(WinGrid parent)
        {
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(WinColor.FromRgb(88, 86, 214)),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var titleText = new TextBlock
            {
                Text = "🏗️ Intelligent MEP Placement",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            var statusText = new TextBlock
            {
                Text = "AI-powered ceiling services automation with energy optimization",
                FontSize = 14,
                Foreground = new SolidColorBrush(WinColor.FromRgb(200, 220, 255)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            headerPanel.Children.Add(titleText);
            headerPanel.Children.Add(statusText);

            WinGrid.SetRow(headerPanel, 0);
            parent.Children.Add(headerPanel);
        }

        private void CreateMainContent(WinGrid parent)
        {
            var contentGrid = new WinGrid();
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(350) }); // Left panel - Controls
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // Center - Floor plan
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(350) }); // Right panel - Results

            CreateControlsPanel(contentGrid);
            CreateFloorPlanPanel(contentGrid);
            CreateResultsPanel(contentGrid);

            WinGrid.SetRow(contentGrid, 1);
            parent.Children.Add(contentGrid);
        }

        private void CreateControlsPanel(WinGrid parent)
        {
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Margin = new Thickness(20, 0, 10, 0)
            };

            var stackPanel = new StackPanel();

            // System selection card
            CreateSystemSelectionCard(stackPanel);

            // Placement parameters card
            CreatePlacementParametersCard(stackPanel);

            // AI optimization options card
            CreateOptimizationOptionsCard(stackPanel);

            // Analysis controls card
            CreateAnalysisControlsCard(stackPanel);

            scrollViewer.Content = stackPanel;
            WinGrid.SetColumn(scrollViewer, 0);
            parent.Children.Add(scrollViewer);
        }

        private void CreateSystemSelectionCard(StackPanel parent)
        {
            var card = CreateCard("🔧 System Selection");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            // System type selection
            var systemLabel = new TextBlock { Text = "MEP System Type:", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) };
            _systemTypeCombo = new WinComboBox
            {
                Height = 35,
                Margin = new Thickness(0, 0, 0, 15)
            };
            _systemTypeCombo.Items.Add(new ComboBoxItem { Content = "💡 Lighting Fixtures", Tag = "Lighting" });
            _systemTypeCombo.Items.Add(new ComboBoxItem { Content = "🌡️ HVAC Diffusers", Tag = "HVAC" });
            _systemTypeCombo.Items.Add(new ComboBoxItem { Content = "🔊 Audio/Visual", Tag = "AV" });
            _systemTypeCombo.Items.Add(new ComboBoxItem { Content = "🚨 Fire Safety", Tag = "FireSafety" });
            _systemTypeCombo.Items.Add(new ComboBoxItem { Content = "📹 Security Systems", Tag = "Security" });
            _systemTypeCombo.SelectedIndex = 0;
            _systemTypeCombo.SelectionChanged += SystemTypeCombo_SelectionChanged;

            // Family selection
            var familyLabel = new TextBlock { Text = "Family Selection:", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) };
            _familySelectionCombo = new WinComboBox
            {
                Height = 35,
                Margin = new Thickness(0, 0, 0, 10)
            };

            contentStack.Children.Add(systemLabel);
            contentStack.Children.Add(_systemTypeCombo);
            contentStack.Children.Add(familyLabel);
            contentStack.Children.Add(_familySelectionCombo);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreatePlacementParametersCard(StackPanel parent)
        {
            var card = CreateCard("📐 Placement Parameters");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            // Spacing control
            var spacingLabel = new TextBlock { Text = "Spacing Distance:", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) };

            var spacingPanel = new StackPanel { Orientation = Orientation.Horizontal, Margin = new Thickness(0, 0, 0, 15) };
            _spacingSlider = new Slider
            {
                Minimum = 4,
                Maximum = 20,
                Value = 8,
                Width = 200,
                VerticalAlignment = VerticalAlignment.Center
            };
            _spacingSlider.ValueChanged += SpacingSlider_ValueChanged;

            _spacingValueText = new TextBlock
            {
                Text = "8.0 ft",
                FontWeight = FontWeights.SemiBold,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(10, 0, 0, 0),
                Foreground = new SolidColorBrush(WinColor.FromRgb(88, 86, 214))
            };

            spacingPanel.Children.Add(_spacingSlider);
            spacingPanel.Children.Add(_spacingValueText);

            // Industry standards info
            var standardsInfo = new TextBlock
            {
                Text = "Spacing based on IES lighting standards, ASHRAE HVAC guidelines, and NFPA fire safety codes",
                FontSize = 11,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 10)
            };

            contentStack.Children.Add(spacingLabel);
            contentStack.Children.Add(spacingPanel);
            contentStack.Children.Add(standardsInfo);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateOptimizationOptionsCard(StackPanel parent)
        {
            var card = CreateCard("🧠 AI Optimization");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            _energyOptimizedCheckBox = new CheckBox
            {
                Content = "⚡ Energy-Optimized Placement",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 10),
                FontWeight = FontWeights.SemiBold
            };

            _clashAvoidanceCheckBox = new CheckBox
            {
                Content = "🔍 Clash Avoidance Analysis",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 10),
                FontWeight = FontWeights.SemiBold
            };

            _codeComplianceCheckBox = new CheckBox
            {
                Content = "📋 Code Compliance Check",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 10),
                FontWeight = FontWeights.SemiBold
            };

            var optimizationInfo = new TextBlock
            {
                Text = "AI integration with Energy Analytics and Clash Detection systems for optimal placement decisions",
                FontSize = 11,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                TextWrapping = TextWrapping.Wrap
            };

            contentStack.Children.Add(_energyOptimizedCheckBox);
            contentStack.Children.Add(_clashAvoidanceCheckBox);
            contentStack.Children.Add(_codeComplianceCheckBox);
            contentStack.Children.Add(optimizationInfo);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateAnalysisControlsCard(StackPanel parent)
        {
            var card = CreateCard("🎯 Analysis Controls");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            _analyzeSpaceButton = new Button
            {
                Content = "🔍 Analyze Ceiling Spaces",
                Height = 40,
                Background = new SolidColorBrush(WinColor.FromRgb(0, 122, 255)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            _analyzeSpaceButton.Click += AnalyzeSpaceButton_Click;

            _generatePlacementButton = new Button
            {
                Content = "🏗️ Generate Placement",
                Height = 40,
                Background = new SolidColorBrush(WinColor.FromRgb(52, 199, 89)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 10),
                IsEnabled = false
            };
            _generatePlacementButton.Click += GeneratePlacementButton_Click;

            _previewButton = new Button
            {
                Content = "👁️ 3D Preview",
                Height = 40,
                Background = new SolidColorBrush(WinColor.FromRgb(255, 149, 0)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontWeight = FontWeights.SemiBold,
                IsEnabled = false
            };
            _previewButton.Click += PreviewButton_Click;

            _analysisProgress = new ProgressBar
            {
                Height = 6,
                Margin = new Thickness(0, 10, 0, 0),
                Visibility = Visibility.Collapsed
            };

            contentStack.Children.Add(_analyzeSpaceButton);
            contentStack.Children.Add(_generatePlacementButton);
            contentStack.Children.Add(_previewButton);
            contentStack.Children.Add(_analysisProgress);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private Border CreateCard(string title)
        {
            var card = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230)),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(0, 0, 0, 20),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = WinColor.FromRgb(0, 0, 0),
                    Opacity = 0.1,
                    BlurRadius = 10,
                    ShadowDepth = 2
                }
            };

            var headerPanel = new StackPanel();
            var headerText = new TextBlock
            {
                Text = title,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(WinColor.FromRgb(51, 51, 51)),
                Margin = new Thickness(20, 15, 20, 0)
            };
            headerPanel.Children.Add(headerText);

            return card;
        }

        private void CreateFloorPlanPanel(WinGrid parent)
        {
            var floorPlanCard = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230)),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(10, 0, 10, 0),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = WinColor.FromRgb(0, 0, 0),
                    Opacity = 0.1,
                    BlurRadius = 10,
                    ShadowDepth = 2
                }
            };

            var contentStack = new StackPanel();

            var headerText = new TextBlock
            {
                Text = "📐 Interactive Floor Plan",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(WinColor.FromRgb(51, 51, 51)),
                Margin = new Thickness(20, 15, 20, 10)
            };

            _floorPlanCanvas = new Canvas
            {
                Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250)),
                Margin = new Thickness(20, 0, 20, 20),
                MinHeight = 600
            };

            // Add grid lines for reference
            DrawGridLines();

            contentStack.Children.Add(headerText);
            contentStack.Children.Add(_floorPlanCanvas);
            floorPlanCard.Child = contentStack;

            WinGrid.SetColumn(floorPlanCard, 1);
            parent.Children.Add(floorPlanCard);
        }

        private void CreateResultsPanel(WinGrid parent)
        {
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Margin = new Thickness(10, 0, 20, 0)
            };

            var stackPanel = new StackPanel();

            // Placement results card
            CreatePlacementResultsCard(stackPanel);

            scrollViewer.Content = stackPanel;
            WinGrid.SetColumn(scrollViewer, 2);
            parent.Children.Add(scrollViewer);
        }

        private void CreatePlacementResultsCard(StackPanel parent)
        {
            var card = CreateCard("📊 Placement Results");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            _placementResultsList = new ListBox
            {
                Height = 500,
                Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250)),
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230)),
                Margin = new Thickness(0, 10, 0, 0)
            };

            var resultsLabel = new TextBlock
            {
                Text = "Analysis results and placement recommendations will appear here",
                FontSize = 12,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                TextWrapping = TextWrapping.Wrap
            };

            contentStack.Children.Add(resultsLabel);
            contentStack.Children.Add(_placementResultsList);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateFooter(WinGrid parent)
        {
            var footerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250)),
                Height = 60
            };

            var statusText = new TextBlock
            {
                Text = "Intelligent placement using IES, ASHRAE, NFPA standards • Integrated with Energy Analytics & Clash Detection",
                FontSize = 12,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            footerPanel.Children.Add(statusText);

            WinGrid.SetRow(footerPanel, 2);
            parent.Children.Add(footerPanel);
        }

        #region Event Handlers

        private void SystemTypeCombo_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (_systemTypeCombo.SelectedItem is ComboBoxItem selectedItem)
            {
                string systemType = selectedItem.Tag.ToString();
                UpdateFamilySelection(systemType);
                UpdateSpacingRecommendations(systemType);
            }
        }

        private void SpacingSlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            if (_spacingValueText != null)
            {
                _spacingValueText.Text = $"{e.NewValue:F1} ft";
            }
        }

        private async void AnalyzeSpaceButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _analyzeSpaceButton.IsEnabled = false;
                _analysisProgress.Visibility = Visibility.Visible;
                _analysisProgress.IsIndeterminate = true;

                Logger.Info("Starting ceiling space analysis");

                // Perform ceiling space detection
                await AnalyzeCeilingSpaces();

                // Update UI with results
                UpdateFloorPlanDisplay();
                UpdateResultsList("Space analysis completed");

                _generatePlacementButton.IsEnabled = true;
                Logger.Info("Ceiling space analysis completed");
            }
            catch (Exception ex)
            {
                Logger.Error("Error during space analysis", ex);
                MessageBox.Show($"Error during analysis: {ex.Message}", "Analysis Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _analyzeSpaceButton.IsEnabled = true;
                _analysisProgress.Visibility = Visibility.Collapsed;
            }
        }

        private async void GeneratePlacementButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _generatePlacementButton.IsEnabled = false;
                _analysisProgress.Visibility = Visibility.Visible;
                _analysisProgress.IsIndeterminate = true;

                Logger.Info("Generating intelligent placement recommendations");

                // Generate AI-powered placement recommendations
                await GenerateIntelligentPlacement();

                // Update displays
                UpdateFloorPlanDisplay();
                UpdateResultsList("Placement generation completed");

                _previewButton.IsEnabled = true;
                Logger.Info("Placement generation completed");
            }
            catch (Exception ex)
            {
                Logger.Error("Error during placement generation", ex);
                MessageBox.Show($"Error generating placement: {ex.Message}", "Placement Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _generatePlacementButton.IsEnabled = true;
                _analysisProgress.Visibility = Visibility.Collapsed;
            }
        }

        private void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Logger.Info("Opening 3D preview window");

                // Create and show 3D preview window
                var previewWindow = new MEP3DPreviewWindow(_uiApp, _placementRecommendations);
                previewWindow.Owner = this;
                previewWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                Logger.Error("Error opening 3D preview", ex);
                MessageBox.Show($"Error opening preview: {ex.Message}", "Preview Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Core Analysis Methods

        private async Task AnalyzeCeilingSpaces()
        {
            try
            {
                Logger.Info("Starting comprehensive ceiling space analysis");

                // Initialize analysis engines
                var ceilingEngine = new CeilingAnalysisEngine(_document);
                var vectorEngine = new VectorPlacementEngine(_document, _aiModel);
                var smartIntegrator = new SmartAnalysisIntegrator(_document, _aiModel, vectorEngine, ceilingEngine);

                // Analyze ceiling types
                var ceilingAnalysis = await ceilingEngine.AnalyzeCeilingTypes();

                // Detect ceiling spaces
                _detectedSpaces = await DetectCeilingSpaces(ceilingAnalysis);

                // Classify office space types
                await ClassifyOfficeSpaceTypes(_detectedSpaces);

                Logger.Info($"Detected {_detectedSpaces.Count} ceiling spaces with {ceilingAnalysis.Count} different ceiling types");
            }
            catch (Exception ex)
            {
                Logger.Error("Error during ceiling space analysis", ex);
                throw;
            }
        }

        private async Task<List<CeilingSpace>> DetectCeilingSpaces(List<CeilingAnalysisResult> ceilingAnalysis)
        {
            var spaces = new List<CeilingSpace>();

            await Task.Run(() =>
            {
                try
                {
                    // Get all rooms/spaces in the document
                    var collector = new FilteredElementCollector(_document)
                        .OfCategory(BuiltInCategory.OST_Rooms)
                        .WhereElementIsNotElementType();

                    foreach (Room room in collector)
                    {
                        if (room.Area > 0) // Only process rooms with area
                        {
                            var space = CreateCeilingSpaceFromRoom(room, ceilingAnalysis);
                            if (space != null)
                            {
                                spaces.Add(space);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error("Error detecting ceiling spaces", ex);
                }
            });

            return spaces;
        }

        private CeilingSpace CreateCeilingSpaceFromRoom(Room room, List<CeilingAnalysisResult> ceilingAnalysis)
        {
            try
            {
                var space = new CeilingSpace
                {
                    SpaceId = room.Id.ToString(),
                    SpaceName = room.Name ?? "Unnamed Room",
                    Area = room.Area,
                    CeilingHeight = GetRoomCeilingHeight(room),
                    WorkPlaneHeight = 2.5 // Default work plane height
                };

                // Get room geometry
                var roomGeometry = GetRoomGeometry(room);
                if (roomGeometry != null)
                {
                    space.CenterPoint = roomGeometry.CenterPoint;
                    space.Width = roomGeometry.Width;
                    space.Length = roomGeometry.Length;
                    space.BoundaryPoints = roomGeometry.BoundaryPoints;
                }

                // Determine ceiling type for this space
                space.CeilingType = DetermineCeilingTypeForSpace(room, ceilingAnalysis);

                // Get existing MEP elements in this space
                space.ExistingMEPElements = GetExistingMEPElementsInSpace(room);

                // Detect obstacles
                space.Obstacles = DetectObstaclesInSpace(room);

                return space;
            }
            catch (Exception ex)
            {
                Logger.Error($"Error creating ceiling space from room {room.Id}", ex);
                return null;
            }
        }

        private async Task GenerateIntelligentPlacement()
        {
            try
            {
                Logger.Info("Generating intelligent placement using AI integration");

                if (_detectedSpaces == null || !_detectedSpaces.Any())
                {
                    throw new InvalidOperationException("No ceiling spaces detected. Please run space analysis first.");
                }

                // Get selected system type
                var selectedSystemType = GetSelectedSystemType();

                // Create placement parameters
                var parameters = CreatePlacementParameters();

                // Initialize smart analysis integrator
                var ceilingEngine = new CeilingAnalysisEngine(_document);
                var vectorEngine = new VectorPlacementEngine(_document, _aiModel);
                var smartIntegrator = new SmartAnalysisIntegrator(_document, _aiModel, vectorEngine, ceilingEngine);

                _placementRecommendations = new List<PlacementRecommendation>();

                // Process each detected space
                foreach (var space in _detectedSpaces)
                {
                    Logger.Info($"Processing space: {space.SpaceName}");

                    // Perform smart analysis for this space
                    var analysisResult = await smartIntegrator.PerformSmartAnalysis(space, selectedSystemType, parameters);

                    if (analysisResult.AnalysisSuccessful)
                    {
                        _placementRecommendations.AddRange(analysisResult.Recommendations);
                        _currentAnalysis = analysisResult;
                    }
                }

                Logger.Info($"Generated {_placementRecommendations.Count} intelligent placement recommendations");
            }
            catch (Exception ex)
            {
                Logger.Error("Error generating intelligent placement", ex);
                throw;
            }
        }

        #endregion

        private void LoadAvailableFamilies()
        {
            try
            {
                // Load families based on selected system type
                UpdateFamilySelection("Lighting");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading families", ex);
            }
        }
    }
}
