using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Autodesk.Revit.DB;

namespace RevitAddIn2025.Models
{
    /// <summary>
    /// Advanced MEP clash detection and resolution using transformer AI insights
    /// </summary>
    public class MEPClashDetector
    {
        /// <summary>
        /// Result of clash detection between MEP elements
        /// </summary>
        public class ClashResult
        {
            public MEPElementData Element1 { get; set; }
            public MEPElementData Element2 { get; set; }
            public XYZ ClashPoint { get; set; }
            public double IntersectionVolume { get; set; }
            public double SeverityScore { get; set; }
            public ClashResolutionType RecommendedResolution { get; set; }
            public string ResolutionDescription { get; set; }
            public double AIConfidenceScore { get; set; }
            public List<string> ApplicableCodes { get; set; }

            public ClashResult()
            {
                ApplicableCodes = new List<string>();
            }
        }

        /// <summary>
        /// Types of clash resolution strategies
        /// </summary>
        public enum ClashResolutionType
        {
            Offset,
            Reroute,
            ReduceSize,
            AddFitting,
            AdjustElevation,
            RelocateEquipment,
            IgnoreClash, // For minor clashes that meet code and won't affect construction
            HighlightForReview // Complex cases requiring human judgment
        }

        private readonly Document _document;
        private readonly AI.MEPTransformerModel _transformerModel;

        /// <summary>
        /// Creates a new MEP clash detector
        /// </summary>
        public MEPClashDetector(Document document, AI.MEPTransformerModel transformerModel = null)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _transformerModel = transformerModel; // Optional AI assistant
        }

        /// <summary>
        /// Detects clashes between MEP elements using AI-enhanced spatial analysis
        /// </summary>
        public async Task<List<ClashResult>> DetectClashesAsync(IList<MEPElementData> mepElements)
        {
            var clashResults = new List<ClashResult>();

            // Create a spatial index for efficient clash detection
            var spatialLookup = CreateSpatialIndex(mepElements);

            // Process elements and detect clashes
            foreach (var element in mepElements)
            {
                // Skip elements without valid geometry
                if (element.BoundingBox == null) continue;

                // Find potential clash candidates using spatial partitioning
                var candidates = GetPotentialClashCandidates(element, spatialLookup);

                foreach (var candidate in candidates)
                {
                    // Skip self-intersection
                    if (element.Id.Value == candidate.Id.Value) continue;

                    // Skip duplicate clashes (each clash is reported only once)
                    if (clashResults.Any(c =>
                        (c.Element1.Id.Value == element.Id.Value && c.Element2.Id.Value == candidate.Id.Value) ||
                        (c.Element1.Id.Value == candidate.Id.Value && c.Element2.Id.Value == element.Id.Value)))
                    {
                        continue;
                    }

                    // Check for actual intersection
                    if (DoElementsClash(element, candidate))
                    {
                        var clash = new ClashResult
                        {
                            Element1 = element,
                            Element2 = candidate,
                            ClashPoint = CalculateClashPoint(element, candidate),
                            IntersectionVolume = EstimateIntersectionVolume(element, candidate),
                            SeverityScore = CalculateClashSeverity(element, candidate)
                        };

                        // Use transformer AI to enhance clash analysis if available
                        if (_transformerModel != null)
                        {
                            await EnhanceClashWithAI(clash);
                        }
                        else
                        {
                            // Default, non-AI recommendations
                            SetDefaultResolutionStrategy(clash);
                        }

                        clashResults.Add(clash);
                    }
                }
            }

            // Sort clashes by severity
            return clashResults.OrderByDescending(c => c.SeverityScore).ToList();
        }

        /// <summary>
        /// Creates a spatial index for quick lookup of elements in proximity
        /// </summary>
        private Dictionary<(int, int, int), List<MEPElementData>> CreateSpatialIndex(IList<MEPElementData> elements)
        {
            // Simple grid-based spatial partitioning
            var grid = new Dictionary<(int, int, int), List<MEPElementData>>();
            double cellSize = 3.0; // 3 feet grid cells - adjust based on project scale

            foreach (var element in elements)
            {
                if (element.BoundingBox == null) continue;

                // Calculate grid cells that this element overlaps
                var minCell = GetGridCell(element.BoundingBox.Min, cellSize);
                var maxCell = GetGridCell(element.BoundingBox.Max, cellSize);

                // Add element to all grid cells it intersects
                for (int x = minCell.Item1; x <= maxCell.Item1; x++)
                {
                    for (int y = minCell.Item2; y <= maxCell.Item2; y++)
                    {
                        for (int z = minCell.Item3; z <= maxCell.Item3; z++)
                        {
                            var cell = (x, y, z);
                            if (!grid.ContainsKey(cell))
                            {
                                grid[cell] = new List<MEPElementData>();
                            }
                            grid[cell].Add(element);
                        }
                    }
                }
            }

            return grid;
        }

        /// <summary>
        /// Gets the grid cell for a point
        /// </summary>
        private (int, int, int) GetGridCell(XYZ point, double cellSize)
        {
            return (
                (int)Math.Floor(point.X / cellSize),
                (int)Math.Floor(point.Y / cellSize),
                (int)Math.Floor(point.Z / cellSize)
            );
        }

        /// <summary>
        /// Gets potential clash candidates for an element
        /// </summary>
        private List<MEPElementData> GetPotentialClashCandidates(MEPElementData element, Dictionary<(int, int, int), List<MEPElementData>> spatialIndex)
        {
            var candidates = new HashSet<MEPElementData>();
            double cellSize = 3.0; // Must match cell size in CreateSpatialIndex

            // Calculate grid cells that this element overlaps
            var minCell = GetGridCell(element.BoundingBox.Min, cellSize);
            var maxCell = GetGridCell(element.BoundingBox.Max, cellSize);

            // Get all elements in the overlapping cells
            for (int x = minCell.Item1; x <= maxCell.Item1; x++)
            {
                for (int y = minCell.Item2; y <= maxCell.Item2; y++)
                {
                    for (int z = minCell.Item3; z <= maxCell.Item3; z++)
                    {
                        var cell = (x, y, z);
                        if (spatialIndex.TryGetValue(cell, out var elementsInCell))
                        {
                            foreach (var candidate in elementsInCell)
                            {
                                if (candidate.Id.Value != element.Id.Value)
                                {
                                    candidates.Add(candidate);
                                }
                            }
                        }
                    }
                }
            }

            return candidates.ToList();
        }

        /// <summary>
        /// Determines if two MEP elements clash
        /// </summary>
        private bool DoElementsClash(MEPElementData element1, MEPElementData element2)
        {
            // Use bounding box intersection as first check
            if (element1.BoundingBox == null || element2.BoundingBox == null)
            {
                return false;
            }

            // Check if bounding boxes intersect
            bool xOverlap = element1.BoundingBox.Min.X <= element2.BoundingBox.Max.X && element1.BoundingBox.Max.X >= element2.BoundingBox.Min.X;
            bool yOverlap = element1.BoundingBox.Min.Y <= element2.BoundingBox.Max.Y && element1.BoundingBox.Max.Y >= element2.BoundingBox.Min.Y;
            bool zOverlap = element1.BoundingBox.Min.Z <= element2.BoundingBox.Max.Z && element1.BoundingBox.Max.Z >= element2.BoundingBox.Min.Z;

            if (!(xOverlap && yOverlap && zOverlap))
            {
                return false;
            }

            // Calculate minimum required clearance between these systems
            double requiredClearance = Math.Max(element1.RecommendedClearance, element2.RecommendedClearance) / 304.8; // Convert mm to feet

            // Calculate closest distance between centroids and compare with minimum required clearance
            // This is a simplified approach - a real implementation would do a more precise geometric collision check
            double distance = element1.Centroid.DistanceTo(element2.Centroid);
            double combinedRadius = (element1.Diameter / 2 + element2.Diameter / 2 + requiredClearance);

            return distance < combinedRadius;
        }

        /// <summary>
        /// Calculates the approximate point where two elements clash
        /// </summary>
        private XYZ CalculateClashPoint(MEPElementData element1, MEPElementData element2)
        {
            // For simple cases, use midpoint between centroids
            return (element1.Centroid + element2.Centroid) / 2.0;
        }

        /// <summary>
        /// Estimates the volume of intersection between two elements
        /// </summary>
        private double EstimateIntersectionVolume(MEPElementData element1, MEPElementData element2)
        {
            // Calculate overlap in each dimension
            double xOverlap = Math.Min(element1.BoundingBox.Max.X, element2.BoundingBox.Max.X) - Math.Max(element1.BoundingBox.Min.X, element2.BoundingBox.Min.X);
            double yOverlap = Math.Min(element1.BoundingBox.Max.Y, element2.BoundingBox.Max.Y) - Math.Max(element1.BoundingBox.Min.Y, element2.BoundingBox.Min.Y);
            double zOverlap = Math.Min(element1.BoundingBox.Max.Z, element2.BoundingBox.Max.Z) - Math.Max(element1.BoundingBox.Min.Z, element2.BoundingBox.Min.Z);

            // Ensure non-negative values
            xOverlap = Math.Max(0, xOverlap);
            yOverlap = Math.Max(0, yOverlap);
            zOverlap = Math.Max(0, zOverlap);

            // Simple box intersection volume
            return xOverlap * yOverlap * zOverlap;
        }

        /// <summary>
        /// Calculates the severity score of a clash
        /// </summary>
        private double CalculateClashSeverity(MEPElementData element1, MEPElementData element2)
        {
            // Base severity is the intersection volume
            double severity = EstimateIntersectionVolume(element1, element2);

            // Multiply by system priorities
            double priorityFactor = element1.SystemPriority * element2.SystemPriority / 10.0;
            severity *= (1.0 + priorityFactor);

            // Consider elements from the same discipline as less severe
            if (element1.Discipline == element2.Discipline)
            {
                severity *= 0.7;
            }

            // Fire protection systems need special consideration
            if (element1.DetailedSystemType == MEPSystemClassifier.DetailedMEPSystemType.FireProtection ||
                element2.DetailedSystemType == MEPSystemClassifier.DetailedMEPSystemType.FireProtection)
            {
                severity *= 1.5;
            }

            return severity;
        }

        /// <summary>
        /// Uses transformer AI to enhance clash analysis
        /// </summary>
        private async Task EnhanceClashWithAI(ClashResult clash)
        {
            try
            {
                // Generate resolution strategy using the transformer model directly with MEP element data
                var clashElements = new List<Models.MEPElementData> { clash.Element1, clash.Element2 };
                var aiPredictions = await _transformerModel.PredictClashResolutionAsync(clashElements);
                var aiPrediction = aiPredictions.FirstOrDefault() ?? "No resolution strategy available";

                // Apply AI recommendations based on string prediction
                clash.ResolutionDescription = aiPrediction;
                clash.AIConfidenceScore = 0.8; // Default confidence for AI predictions

                // Parse the prediction to determine resolution type
                if (aiPrediction.ToLower().Contains("reroute"))
                {
                    clash.RecommendedResolution = ClashResolutionType.Reroute;
                }
                else if (aiPrediction.ToLower().Contains("elevation"))
                {
                    clash.RecommendedResolution = ClashResolutionType.AdjustElevation;
                }
                else if (aiPrediction.ToLower().Contains("offset"))
                {
                    clash.RecommendedResolution = ClashResolutionType.Offset;
                }
                else
                {
                    clash.RecommendedResolution = ClashResolutionType.HighlightForReview;
                }

                // If AI confidence is low, default to human review
                if (clash.AIConfidenceScore < 0.7)
                {
                    clash.RecommendedResolution = ClashResolutionType.HighlightForReview;
                }
            }
            catch (Exception)
            {
                // Fallback to default resolution if AI fails
                SetDefaultResolutionStrategy(clash);
                clash.AIConfidenceScore = 0.0;
            }
        }

        /// <summary>
        /// Sets default resolution strategy based on element types and clash severity
        /// </summary>
        private void SetDefaultResolutionStrategy(ClashResult clash)
        {
            // Use system priorities to decide which element should be modified
            bool element1ShouldMove = clash.Element1.SystemPriority < clash.Element2.SystemPriority;
            var elementToMove = element1ShouldMove ? clash.Element1 : clash.Element2;

            // Default strategies based on element types and clash severity
            if (clash.IntersectionVolume < 0.1)
            {
                // Minor clash, try simple offset
                clash.RecommendedResolution = ClashResolutionType.Offset;
                clash.ResolutionDescription = $"Offset {elementToMove.ElementName} to avoid clash.";
            }
            else if (clash.Element1.DetailedSystemType == MEPSystemClassifier.DetailedMEPSystemType.FireProtection ||
                    clash.Element2.DetailedSystemType == MEPSystemClassifier.DetailedMEPSystemType.FireProtection)
            {
                // Fire protection always has priority
                var nonFireElement = clash.Element1.DetailedSystemType == MEPSystemClassifier.DetailedMEPSystemType.FireProtection
                    ? clash.Element2 : clash.Element1;

                clash.RecommendedResolution = ClashResolutionType.Reroute;
                clash.ResolutionDescription = $"Reroute {nonFireElement.ElementName} to avoid fire protection system.";
            }
            else if (clash.SeverityScore > 5.0)
            {
                // Significant clash
                clash.RecommendedResolution = ClashResolutionType.HighlightForReview;
                clash.ResolutionDescription = "Major clash detected. Manual review required.";
            }
            else
            {
                // Standard case - adjust elevation
                clash.RecommendedResolution = ClashResolutionType.AdjustElevation;
                clash.ResolutionDescription = $"Adjust elevation of {elementToMove.ElementName}.";
            }
        }

        /// <summary>
        /// Creates a feature vector for an element for AI prediction
        /// </summary>
        private async Task<float[]> CreateElementVector(MEPElementData element)
        {
            // Create a basic feature vector representing this MEP element
            // This would be expanded in a real implementation

            float[] features = new float[16];

            // System type (one-hot encoded)
            int systemTypeIndex = (int)element.DetailedSystemType;
            if (systemTypeIndex >= 0 && systemTypeIndex < 16)
            {
                features[systemTypeIndex] = 1.0f;
            }

            return features;
        }
    }
}
