using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Media3D;
using System.Threading.Tasks;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB;
using RevitAddIn2025.Models;
using RevitAddIn2025.Utilities;
using WinColor = System.Windows.Media.Color;
using WinGrid = System.Windows.Controls.Grid;
using WinComboBox = System.Windows.Controls.ComboBox;
using WinMaterial = System.Windows.Media.Media3D.Material;

namespace RevitAddIn2025.UI
{
    /// <summary>
    /// 3D Preview Window for MEP Placement Visualization
    /// Provides interactive 3D visualization of placement recommendations
    /// </summary>
    public class MEP3DPreviewWindow : Window
    {
        private readonly UIApplication _uiApp;
        private readonly List<PlacementRecommendation> _recommendations;

        // 3D Visualization components
        private Viewport3D _viewport3D;
        private ModelVisual3D _modelVisual3D;
        private PerspectiveCamera _camera;
        private Model3DGroup _model3DGroup;

        // UI Controls
        private Slider _transparencySlider;
        private CheckBox _showEnergyHeatmapCheckBox;
        private CheckBox _showClashIndicatorsCheckBox;
        private CheckBox _showComplianceZonesCheckBox;
        private WinComboBox _viewModeCombo;
        private ListBox _recommendationsList;
        private Button _applyPlacementButton;
        private Button _exportReportButton;

        // Visualization data
        private Dictionary<string, WinMaterial> _systemMaterials;
        private List<Visual3D> _placementVisuals;

        public MEP3DPreviewWindow(UIApplication uiApp, List<PlacementRecommendation> recommendations)
        {
            _uiApp = uiApp ?? throw new ArgumentNullException(nameof(uiApp));
            _recommendations = recommendations ?? throw new ArgumentNullException(nameof(recommendations));

            InitializeWindow();
            InitializeMaterials();
            CreateUI();
            LoadRecommendations();
        }

        private void InitializeWindow()
        {
            Title = "MEP Placement 3D Preview";
            Width = 1200;
            Height = 800;
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250));

            FontFamily = new FontFamily("Segoe UI");
            FontSize = 14;
        }

        private void InitializeMaterials()
        {
            _systemMaterials = new Dictionary<string, WinMaterial>
            {
                ["Lighting"] = new DiffuseMaterial(new SolidColorBrush(WinColor.FromRgb(255, 215, 0))), // Gold
                ["HVAC"] = new DiffuseMaterial(new SolidColorBrush(WinColor.FromRgb(0, 191, 255))), // Deep Sky Blue
                ["FireSafety"] = new DiffuseMaterial(new SolidColorBrush(WinColor.FromRgb(255, 69, 0))), // Red Orange
                ["AudioVisual"] = new DiffuseMaterial(new SolidColorBrush(WinColor.FromRgb(138, 43, 226))), // Blue Violet
                ["Security"] = new DiffuseMaterial(new SolidColorBrush(WinColor.FromRgb(34, 139, 34))), // Forest Green
                ["Optimal"] = new DiffuseMaterial(new SolidColorBrush(WinColor.FromRgb(50, 205, 50))), // Lime Green
                ["Warning"] = new DiffuseMaterial(new SolidColorBrush(WinColor.FromRgb(255, 165, 0))), // Orange
                ["Error"] = new DiffuseMaterial(new SolidColorBrush(WinColor.FromRgb(220, 20, 60))) // Crimson
            };
        }

        private void CreateUI()
        {
            var mainGrid = new WinGrid();
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) }); // Header
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) }); // Content
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) }); // Footer

            CreateHeader(mainGrid);
            CreateMainContent(mainGrid);
            CreateFooter(mainGrid);

            Content = mainGrid;
        }

        private void CreateHeader(WinGrid parent)
        {
            var headerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(WinColor.FromRgb(88, 86, 214)),
                Margin = new Thickness(0, 0, 0, 20)
            };

            var titleText = new TextBlock
            {
                Text = "🎯 3D MEP Placement Preview",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            var statusText = new TextBlock
            {
                Text = $"Interactive visualization of {_recommendations.Count} placement recommendations",
                FontSize = 14,
                Foreground = new SolidColorBrush(WinColor.FromRgb(200, 220, 255)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            headerPanel.Children.Add(titleText);
            headerPanel.Children.Add(statusText);

            WinGrid.SetRow(headerPanel, 0);
            parent.Children.Add(headerPanel);
        }

        private void CreateMainContent(WinGrid parent)
        {
            var contentGrid = new WinGrid();
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(300) }); // Left panel - Controls
            contentGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // Center - 3D View

            CreateControlsPanel(contentGrid);
            Create3DViewPanel(contentGrid);

            WinGrid.SetRow(contentGrid, 1);
            parent.Children.Add(contentGrid);
        }

        private void CreateControlsPanel(WinGrid parent)
        {
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                Margin = new Thickness(20, 0, 10, 0)
            };

            var stackPanel = new StackPanel();

            // Visualization controls card
            CreateVisualizationControlsCard(stackPanel);

            // Recommendations list card
            CreateRecommendationsListCard(stackPanel);

            // Action buttons card
            CreateActionButtonsCard(stackPanel);

            scrollViewer.Content = stackPanel;
            WinGrid.SetColumn(scrollViewer, 0);
            parent.Children.Add(scrollViewer);
        }

        private void CreateVisualizationControlsCard(StackPanel parent)
        {
            var card = CreateCard("🎨 Visualization Controls");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            // View mode selection
            var viewModeLabel = new TextBlock { Text = "View Mode:", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) };
            _viewModeCombo = new WinComboBox
            {
                Height = 35,
                Margin = new Thickness(0, 0, 0, 15)
            };
            _viewModeCombo.Items.Add(new ComboBoxItem { Content = "🏗️ All Recommendations", Tag = "All" });
            _viewModeCombo.Items.Add(new ComboBoxItem { Content = "⭐ Optimal Only", Tag = "Optimal" });
            _viewModeCombo.Items.Add(new ComboBoxItem { Content = "⚡ Energy Optimized", Tag = "Energy" });
            _viewModeCombo.Items.Add(new ComboBoxItem { Content = "🔍 Clash Analysis", Tag = "Clash" });
            _viewModeCombo.SelectedIndex = 0;
            _viewModeCombo.SelectionChanged += ViewModeCombo_SelectionChanged;

            // Transparency control
            var transparencyLabel = new TextBlock { Text = "Transparency:", FontWeight = FontWeights.SemiBold, Margin = new Thickness(0, 0, 0, 5) };
            _transparencySlider = new Slider
            {
                Minimum = 0,
                Maximum = 100,
                Value = 20,
                Margin = new Thickness(0, 0, 0, 15)
            };
            _transparencySlider.ValueChanged += TransparencySlider_ValueChanged;

            // Visualization options
            _showEnergyHeatmapCheckBox = new CheckBox
            {
                Content = "⚡ Energy Efficiency Heatmap",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 10),
                FontWeight = FontWeights.SemiBold
            };
            _showEnergyHeatmapCheckBox.Checked += VisualizationOption_Changed;
            _showEnergyHeatmapCheckBox.Unchecked += VisualizationOption_Changed;

            _showClashIndicatorsCheckBox = new CheckBox
            {
                Content = "🔍 Clash Risk Indicators",
                IsChecked = true,
                Margin = new Thickness(0, 0, 0, 10),
                FontWeight = FontWeights.SemiBold
            };
            _showClashIndicatorsCheckBox.Checked += VisualizationOption_Changed;
            _showClashIndicatorsCheckBox.Unchecked += VisualizationOption_Changed;

            _showComplianceZonesCheckBox = new CheckBox
            {
                Content = "📋 Compliance Zones",
                IsChecked = false,
                Margin = new Thickness(0, 0, 0, 10),
                FontWeight = FontWeights.SemiBold
            };
            _showComplianceZonesCheckBox.Checked += VisualizationOption_Changed;
            _showComplianceZonesCheckBox.Unchecked += VisualizationOption_Changed;

            contentStack.Children.Add(viewModeLabel);
            contentStack.Children.Add(_viewModeCombo);
            contentStack.Children.Add(transparencyLabel);
            contentStack.Children.Add(_transparencySlider);
            contentStack.Children.Add(_showEnergyHeatmapCheckBox);
            contentStack.Children.Add(_showClashIndicatorsCheckBox);
            contentStack.Children.Add(_showComplianceZonesCheckBox);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateRecommendationsListCard(StackPanel parent)
        {
            var card = CreateCard("📊 Recommendations");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            _recommendationsList = new ListBox
            {
                Height = 300,
                Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250)),
                BorderThickness = new Thickness(1),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230)),
                Margin = new Thickness(0, 10, 0, 0)
            };
            _recommendationsList.SelectionChanged += RecommendationsList_SelectionChanged;

            var listLabel = new TextBlock
            {
                Text = "Select recommendations to highlight in 3D view",
                FontSize = 12,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                TextWrapping = TextWrapping.Wrap
            };

            contentStack.Children.Add(listLabel);
            contentStack.Children.Add(_recommendationsList);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void CreateActionButtonsCard(StackPanel parent)
        {
            var card = CreateCard("🎯 Actions");
            var contentStack = new StackPanel { Margin = new Thickness(20) };

            _applyPlacementButton = new Button
            {
                Content = "✅ Apply Selected Placement",
                Height = 40,
                Background = new SolidColorBrush(WinColor.FromRgb(52, 199, 89)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontWeight = FontWeights.SemiBold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            _applyPlacementButton.Click += ApplyPlacementButton_Click;

            _exportReportButton = new Button
            {
                Content = "📄 Export 3D Report",
                Height = 40,
                Background = new SolidColorBrush(WinColor.FromRgb(0, 122, 255)),
                Foreground = Brushes.White,
                BorderThickness = new Thickness(0),
                FontWeight = FontWeights.SemiBold
            };
            _exportReportButton.Click += ExportReportButton_Click;

            contentStack.Children.Add(_applyPlacementButton);
            contentStack.Children.Add(_exportReportButton);

            card.Child = contentStack;
            parent.Children.Add(card);
        }

        private void Create3DViewPanel(WinGrid parent)
        {
            var viewCard = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230)),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(10, 0, 20, 0),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = WinColor.FromRgb(0, 0, 0),
                    Opacity = 0.1,
                    BlurRadius = 10,
                    ShadowDepth = 2
                }
            };

            var contentStack = new StackPanel();

            var headerText = new TextBlock
            {
                Text = "🎯 Interactive 3D Placement View",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(WinColor.FromRgb(51, 51, 51)),
                Margin = new Thickness(20, 15, 20, 10)
            };

            // Create 3D viewport
            _viewport3D = new Viewport3D
            {
                Margin = new Thickness(20, 0, 20, 20),
                MinHeight = 500
            };

            Initialize3DScene();

            contentStack.Children.Add(headerText);
            contentStack.Children.Add(_viewport3D);
            viewCard.Child = contentStack;

            WinGrid.SetColumn(viewCard, 1);
            parent.Children.Add(viewCard);
        }

        private void Initialize3DScene()
        {
            // Create camera
            _camera = new PerspectiveCamera
            {
                Position = new Point3D(0, 0, 10),
                LookDirection = new Vector3D(0, 0, -1),
                UpDirection = new Vector3D(0, 1, 0),
                FieldOfView = 60
            };
            _viewport3D.Camera = _camera;

            // Create model group
            _model3DGroup = new Model3DGroup();

            // Add lighting
            var ambientLight = new AmbientLight(Colors.White, 0.4);
            var directionalLight = new DirectionalLight(Colors.White, new Vector3D(-1, -1, -1));

            _model3DGroup.Children.Add(ambientLight);
            _model3DGroup.Children.Add(directionalLight);

            // Create model visual
            _modelVisual3D = new ModelVisual3D { Content = _model3DGroup };
            _viewport3D.Children.Add(_modelVisual3D);

            _placementVisuals = new List<Visual3D>();
        }

        private Border CreateCard(string title)
        {
            var card = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(8),
                BorderBrush = new SolidColorBrush(WinColor.FromRgb(230, 230, 230)),
                BorderThickness = new Thickness(1),
                Margin = new Thickness(0, 0, 0, 20),
                Effect = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = WinColor.FromRgb(0, 0, 0),
                    Opacity = 0.1,
                    BlurRadius = 10,
                    ShadowDepth = 2
                }
            };

            var headerPanel = new StackPanel();
            var headerText = new TextBlock
            {
                Text = title,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(WinColor.FromRgb(51, 51, 51)),
                Margin = new Thickness(20, 15, 20, 0)
            };
            headerPanel.Children.Add(headerText);

            return card;
        }

        private void CreateFooter(WinGrid parent)
        {
            var footerPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Background = new SolidColorBrush(WinColor.FromRgb(248, 249, 250)),
                Height = 60
            };

            var statusText = new TextBlock
            {
                Text = "Interactive 3D visualization • Energy heatmaps • Clash detection • Code compliance zones",
                FontSize = 12,
                Foreground = new SolidColorBrush(WinColor.FromRgb(128, 128, 128)),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(20, 0, 0, 0)
            };

            footerPanel.Children.Add(statusText);

            WinGrid.SetRow(footerPanel, 2);
            parent.Children.Add(footerPanel);
        }

        private void LoadRecommendations()
        {
            try
            {
                Logger.Info("Loading placement recommendations into 3D view");

                // Populate recommendations list
                foreach (var recommendation in _recommendations.Take(10)) // Limit for performance
                {
                    var listItem = new ListBoxItem
                    {
                        Content = $"Score: {recommendation.OverallScore:F2} - {recommendation.RecommendationReason}",
                        Tag = recommendation
                    };
                    _recommendationsList.Items.Add(listItem);
                }

                // Create 3D visualizations
                Create3DVisualizations();

                Logger.Info("3D preview loaded successfully");
            }
            catch (Exception ex)
            {
                Logger.Error("Error loading recommendations", ex);
            }
        }

        private void Create3DVisualizations()
        {
            // Implementation for creating 3D visualizations of placement recommendations
            // This would create 3D models for each placement point with appropriate materials and colors
        }

        #region Event Handlers

        private void ViewModeCombo_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Update 3D view based on selected mode
            Update3DView();
        }

        private void TransparencySlider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
        {
            // Update transparency of 3D objects
            UpdateTransparency(e.NewValue / 100.0);
        }

        private void VisualizationOption_Changed(object sender, RoutedEventArgs e)
        {
            // Update visualization options
            Update3DView();
        }

        private void RecommendationsList_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // Highlight selected recommendation in 3D view
            HighlightSelectedRecommendation();
        }

        private void ApplyPlacementButton_Click(object sender, RoutedEventArgs e)
        {
            // Apply selected placement to Revit model
            ApplySelectedPlacement();
        }

        private void ExportReportButton_Click(object sender, RoutedEventArgs e)
        {
            // Export 3D visualization report
            ExportVisualizationReport();
        }

        #endregion

        #region Helper Methods

        private void Update3DView()
        {
            // Implementation for updating 3D view based on current settings
        }

        private void UpdateTransparency(double transparency)
        {
            // Implementation for updating transparency of 3D objects
        }

        private void HighlightSelectedRecommendation()
        {
            // Implementation for highlighting selected recommendation
        }

        private void ApplySelectedPlacement()
        {
            // Implementation for applying placement to Revit model
        }

        private void ExportVisualizationReport()
        {
            // Implementation for exporting visualization report
        }

        #endregion
    }
}
